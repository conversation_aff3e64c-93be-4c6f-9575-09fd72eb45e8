@echo off
echo ========================================
echo 🔍 WARDEN OSINT Tool v3.0
echo Запуск основной программы
echo ========================================
echo.

REM Создаем директории
if not exist "target\classes" mkdir target\classes
if not exist "target\lib" mkdir target\lib

echo [DOWNLOAD] Скачивание Jackson...
if not exist "target\lib\jackson-core-2.15.2.jar" (
    curl -L -o target\lib\jackson-core-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar
)

if not exist "target\lib\jackson-databind-2.15.2.jar" (
    curl -L -o target\lib\jackson-databind-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar
)

if not exist "target\lib\jackson-annotations-2.15.2.jar" (
    curl -L -o target\lib\jackson-annotations-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar
)

set CLASSPATH=target\lib\*;target\classes

echo.
echo [COMPILE] Компиляция основных модулей...

echo Compiling Logger...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\Logger.java

echo Compiling Localization...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\Localization.java

echo Compiling HWIDGenerator...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\HWIDGenerator.java

echo Compiling User...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\User.java

echo Compiling AuthManager...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\AuthManager.java

echo Compiling TelegramSearchResult...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java

echo Compiling TelegramOSINT...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java

echo Compiling UsersBoxAPI...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\UsersBoxAPI.java

echo Compiling Swing modules...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\swing\SwingWardenDemo.java

echo.
echo ✅ Компиляция завершена!
echo.

echo [LAUNCH] Запуск Swing Demo...
java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingWardenDemo

pause
