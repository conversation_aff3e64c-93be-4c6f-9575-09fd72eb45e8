@echo off
echo ========================================
echo 🔍 WARDEN v3.0 - Swing Edition
echo 🎨 Advanced Theme System (No JavaFX!)
echo ========================================
echo.

echo [LAUNCHER] Checking Java installation...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java not found! Please install Java 17+ from https://adoptium.net/
    pause
    exit /b 1
)
echo ✅ Java found
echo.

echo [LAUNCHER] ✨ NO JavaFX REQUIRED! ✨
echo This version uses pure Java Swing - works out of the box!
echo.

echo [LAUNCHER] Creating target directory...
if not exist "target\classes" mkdir target\classes

echo [LAUNCHER] Compiling Swing WARDEN...

REM Компилируем SwingThemeManager
echo Compiling SwingThemeManager...
javac -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile SwingThemeManager
    pause
    exit /b 1
)

REM Компилируем SwingWardenDemo
echo Compiling SwingWardenDemo...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingWardenDemo.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile SwingWardenDemo
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

echo [LAUNCHER] Starting WARDEN Swing Edition...
echo.

REM Запускаем приложение
java -cp target\classes com.warden.osint.gui.swing.SwingWardenDemo

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start WARDEN Swing Edition
    echo.
    echo TROUBLESHOOTING:
    echo 1. Make sure Java 17+ is installed
    echo 2. Check compilation errors above
    echo 3. Try running: java -version
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ WARDEN Swing Edition started successfully!
echo 🎨 Don't forget to try the theme switching!
echo.
pause
