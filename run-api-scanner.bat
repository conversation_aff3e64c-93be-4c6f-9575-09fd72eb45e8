@echo off
echo ========================================
echo 🔍 WARDEN API Scanner - Database Hunter
echo 🚀 Поиск баз данных по множеству API
echo ========================================
echo.

echo [COMPILE] Компиляция API Scanner...

REM Создаем директории
if not exist "target\classes" mkdir target\classes

REM Компилируем утилиты
echo Compiling utilities...
javac -d target\classes src\main\java\com\warden\osint\utils\Logger.java
javac -d target\classes src\main\java\com\warden\osint\utils\Localization.java

REM Компилируем Swing Theme Manager
echo Compiling Swing Theme Manager...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java

REM Компилируем API Scanner
echo Compiling API Scanner models...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIEndpointResult.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanResult.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling API Scanner core...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanner.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

REM Компилируем GUI
echo Compiling API Scanner GUI...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIScannerGUI.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

echo [LAUNCH] Запуск API Scanner...
echo.

REM Запускаем API Scanner
java -cp target\classes com.warden.osint.gui.swing.APIScannerGUI

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start API Scanner
    pause
    exit /b 1
)

echo.
echo ✅ API Scanner started successfully!
pause
goto :end

:compile_error
echo ❌ Compilation error occurred!
pause
exit /b 1

:end
