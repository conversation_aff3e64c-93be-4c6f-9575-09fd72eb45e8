#!/bin/bash

echo "========================================"
echo "🎨 WARDEN Theme System Demo"
echo "========================================"
echo

echo "[DEMO] Starting theme demonstration..."
echo

# Компилируем проект
echo "[INFO] Compiling project..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "[ERROR] Compilation failed!"
    exit 1
fi

# Определяем путь к JavaFX
JAVAFX_PATH=""

if [ -d "/usr/share/openjfx/lib" ]; then
    JAVAFX_PATH="/usr/share/openjfx/lib"
elif [ -d "$HOME/javafx-sdk-17/lib" ]; then
    JAVAFX_PATH="$HOME/javafx-sdk-17/lib"
elif [ -d "/opt/javafx-sdk-17/lib" ]; then
    JAVAFX_PATH="/opt/javafx-sdk-17/lib"
else
    echo "[ERROR] JavaFX SDK not found."
    echo
    echo "Please install JavaFX:"
    echo "1. Download from: https://openjfx.io/"
    echo "2. Extract to: $HOME/javafx-sdk-17/"
    echo "3. Or install via package manager:"
    echo "   sudo apt install openjfx  # Ubuntu/Debian"
    echo
    exit 1
fi

echo "[INFO] Using JavaFX from: $JAVAFX_PATH"

# Запускаем демо
echo "[INFO] Starting theme demo..."
java --module-path "$JAVAFX_PATH" --add-modules javafx.controls,javafx.fxml -cp "target/classes" com.warden.osint.demo.ThemeDemo

if [ $? -ne 0 ]; then
    echo
    echo "[ERROR] Theme demo failed to start."
    echo
    echo "Please make sure:"
    echo "1. JavaFX SDK is properly installed"
    echo "2. Java 17+ is installed"
    echo "3. Project is compiled: mvn clean compile"
    echo
    exit 1
fi

echo
echo "[SUCCESS] Theme demo completed!"
