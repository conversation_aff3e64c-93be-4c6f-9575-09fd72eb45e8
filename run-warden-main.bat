@echo off
echo ========================================
echo 🔍 WARDEN OSINT - Main Launcher
echo 🚀 Advanced Intelligence Platform
echo ========================================
echo.

echo [COMPILE] Быстрая компиляция основных модулей...

REM Создаем директории
if not exist "target\classes" mkdir target\classes

REM Компилируем утилиты
javac -d target\classes src\main\java\com\warden\osint\utils\Logger.java 2>nul
javac -d target\classes src\main\java\com\warden\osint\utils\Localization.java 2>nul

REM Компилируем Swing Theme Manager
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

REM Создаем заглушки для Jackson (для компиляции без зависимостей)
echo package com.fasterxml.jackson.annotation; public @interface JsonProperty { String value() default ""; } > temp_JsonProperty.java
echo package com.fasterxml.jackson.annotation; public @interface JsonIgnoreProperties { boolean ignoreUnknown() default false; } > temp_JsonIgnoreProperties.java
echo package com.fasterxml.jackson.databind; public class ObjectMapper { public ObjectMapper() {} } > temp_ObjectMapper.java
echo package com.fasterxml.jackson.databind; public class JsonNode { } > temp_JsonNode.java

javac -d target\classes temp_*.java
del temp_*.java

REM Компилируем API модули
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramUserInfo.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramChannelMatch.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramAccount.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramGroupInfo.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramChannelActivity.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\UsersBoxAPI.java 2>nul

REM Компилируем API Scanner
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIEndpointResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanner.java

REM Компилируем API Finder
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APISource.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinderResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinder.java

REM Компилируем GUI
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIScannerGUI.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIFinderGUI.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingMainWindow.java

if %ERRORLEVEL% NEQ 0 goto :compile_error

echo ✅ Компиляция завершена!
echo.

echo [LAUNCH] Запуск WARDEN OSINT Main Window...
echo.
echo 🔍 WARDEN OSINT - Advanced Intelligence Platform
echo ═══════════════════════════════════════════════════════════════
echo 📱 Telegram OSINT - мощный поиск по Telegram
echo 🔍 API Scanner - сканирование API на наличие данных  
echo 🔍 API Finder - поиск API источников с базами данных
echo 📱 Phone Lookup - поиск по номеру телефона
echo 📧 Email Search - поиск по email адресу
echo ═══════════════════════════════════════════════════════════════
echo.

REM Запускаем основное окно WARDEN
java -cp target\classes com.warden.osint.gui.swing.SwingMainWindow

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start WARDEN Main Window
    pause
    exit /b 1
)

echo.
echo ✅ WARDEN OSINT started successfully!
pause
goto :end

:compile_error
echo ❌ Compilation error occurred!
echo.
echo Возможные причины:
echo - Отсутствует Java JDK
echo - Синтаксические ошибки в коде
echo.
pause
exit /b 1

:end
