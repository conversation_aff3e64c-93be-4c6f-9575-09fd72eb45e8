@echo off
echo ========================================
echo 🔍 WARDEN v3.0 - Simple Launcher
echo 🎨 With Advanced Theme System
echo ========================================
echo.

echo [LAUNCHER] Checking Java installation...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java not found! Please install Java 17+ from https://adoptium.net/
    pause
    exit /b 1
)
echo ✅ Java found
echo.

echo [LAUNCHER] Checking JavaFX SDK...

REM Проверяем разные возможные пути JavaFX
set JAVAFX_PATH=""

if exist "C:\javafx-sdk-21\lib\javafx.controls.jar" (
    set JAVAFX_PATH=C:\javafx-sdk-21\lib
    echo ✅ JavaFX SDK found at C:\javafx-sdk-21\lib
) else if exist "C:\javafx-sdk-17\lib\javafx.controls.jar" (
    set JAVAFX_PATH=C:\javafx-sdk-17\lib
    echo ✅ JavaFX SDK found at C:\javafx-sdk-17\lib
) else if exist "C:\javafx\lib\javafx.controls.jar" (
    set JAVAFX_PATH=C:\javafx\lib
    echo ✅ JavaFX SDK found at C:\javafx\lib
) else (
    echo ❌ JavaFX SDK not found!
    echo.
    echo SETUP REQUIRED:
    echo 1. Download JavaFX SDK from: https://openjfx.io/
    echo 2. Choose JavaFX 21 SDK for Windows x64
    echo 3. Extract to: C:\javafx-sdk-21\
    echo 4. Make sure C:\javafx-sdk-21\lib\javafx.controls.jar exists
    echo.
    echo See JAVAFX_SETUP.md for detailed instructions
    echo.
    pause
    exit /b 1
)

echo.
echo [LAUNCHER] Compiling WARDEN project...

REM Создаем папку для классов если её нет
if not exist "target\classes" mkdir target\classes

REM Компилируем основные классы
echo Compiling core classes...
javac -cp "%JAVAFX_PATH%\*" -d target\classes src\main\java\com\warden\Launcher.java src\main\java\com\warden\Main1.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile main classes
    pause
    exit /b 1
)

REM Компилируем утилиты
echo Compiling utilities...
javac -cp "%JAVAFX_PATH%\*;target\classes" -d target\classes src\main\java\com\warden\osint\utils\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile utilities
    pause
    exit /b 1
)

REM Компилируем аутентификацию
echo Compiling authentication...
javac -cp "%JAVAFX_PATH%\*;target\classes" -d target\classes src\main\java\com\warden\osint\auth\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile authentication
    pause
    exit /b 1
)

REM Компилируем API
echo Compiling API...
javac -cp "%JAVAFX_PATH%\*;target\classes" -d target\classes src\main\java\com\warden\osint\api\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile API
    pause
    exit /b 1
)

REM Компилируем чат
echo Compiling chat...
javac -cp "%JAVAFX_PATH%\*;target\classes" -d target\classes src\main\java\com\warden\osint\chat\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile chat
    pause
    exit /b 1
)

REM Компилируем GUI
echo Compiling GUI...
javac -cp "%JAVAFX_PATH%\*;target\classes" -d target\classes src\main\java\com\warden\osint\gui\*.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile GUI
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

echo [LAUNCHER] Testing theme system...
java -cp "target\classes" com.warden.osint.test.ThemeTest

if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ Theme test had issues, but continuing...
)

echo.
echo [LAUNCHER] Starting WARDEN with JavaFX...
echo Using JavaFX from: %JAVAFX_PATH%
echo.

REM Запускаем приложение
java --module-path "%JAVAFX_PATH%" --add-modules javafx.controls,javafx.fxml -cp "target\classes" com.warden.Launcher

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start WARDEN
    echo.
    echo TROUBLESHOOTING:
    echo 1. Make sure JavaFX SDK is properly installed
    echo 2. Check that %JAVAFX_PATH% contains .jar files
    echo 3. See JAVAFX_SETUP.md for detailed help
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ WARDEN started successfully!
echo 🎨 Don't forget to try the new theme system!
echo.
pause
