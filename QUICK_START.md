# 🚀 WARDEN v3.0 - Быстрый старт

## 🎨 Новая система тем!

WARDEN v3.0 теперь поддерживает **две потрясающие темы**:
- 🟢 **Matrix Theme** - классическая зеленая тема хакера
- 🌙 **Midnight Theme** - элегантная полуночная тема

## ⚡ Быстрый запуск

### 1️⃣ Самый простой способ (Maven)
```bash
mvn clean compile javafx:run
```

### 2️⃣ Использование готовых скриптов

#### Windows:
```cmd
# Основное приложение
run-warden.bat

# Тест системы тем
test-themes.bat

# Демо тем с GUI
run-theme-demo.bat
```

#### Linux/Mac:
```bash
# Сделать скрипты исполняемыми
chmod +x *.sh

# Основное приложение
./run-warden.sh

# Тест системы тем
./test-themes.sh

# Демо тем с GUI
./run-theme-demo.sh
```

## 🔧 Если возникает ошибка JavaFX

### Автоматическое решение:
Просто запустите соответствующий скрипт - он автоматически найдет и настроит JavaFX!

### Ручная установка JavaFX:

#### Windows:
1. Скачайте: https://openjfx.io/
2. Распакуйте в `C:\javafx-sdk-17\`
3. Запустите `run-warden.bat`

#### Linux:
```bash
# Ubuntu/Debian
sudo apt install openjfx

# Или вручную
wget https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_linux-x64_bin-sdk.zip
unzip openjfx-17.0.2_linux-x64_bin-sdk.zip
mv javafx-sdk-17.0.2 ~/javafx-sdk-17
```

#### macOS:
```bash
brew install openjfx
```

## 🎨 Как переключать темы

### В окне входа:
- Найдите кнопку **"🌙 Switch to Midnight Theme"**
- Нажмите для переключения

### В основном окне:
- В левой панели найдите кнопку **"🎨 Тема"**
- Нажмите для переключения между темами

## 🧪 Тестирование

### Тест без GUI (быстрый):
```bash
# Windows
test-themes.bat

# Linux/Mac
./test-themes.sh
```

### Демо с полным GUI:
```bash
# Windows
run-theme-demo.bat

# Linux/Mac
./run-theme-demo.sh
```

## 📋 Что нужно

- **Java 17+** (обязательно)
- **Maven 3.6+** (для сборки)
- **JavaFX** (автоматически или вручную)

## 🎯 Первый запуск

1. **Клонируйте проект**
2. **Запустите**: `mvn clean compile javafx:run`
3. **Если ошибка JavaFX**: используйте `run-warden.bat` (Windows) или `./run-warden.sh` (Linux/Mac)
4. **Попробуйте темы**: нажмите кнопку переключения тем
5. **Наслаждайтесь!** 🎉

## 🆘 Помощь

### Частые проблемы:

**"JavaFX runtime components are missing"**
```bash
# Решение: используйте скрипты
run-warden.bat      # Windows
./run-warden.sh     # Linux/Mac
```

**"No main manifest attribute"**
```bash
# Решение: используйте Maven
mvn clean compile javafx:run
```

**Проблемы с Java**
```bash
# Проверьте версию (должна быть 17+)
java -version

# Установите Java 17
# Windows: https://adoptium.net/
# Linux: sudo apt install openjdk-17-jdk
# Mac: brew install openjdk@17
```

### Контакты поддержки:
- Telegram: @InfernoSoulAttack
- Telegram: @Svuanstvo

## 🎉 Готово!

После успешного запуска вы увидите:
- 🔐 Окно входа с возможностью переключения тем
- 🎨 Плавные анимации смены цветов
- 🌙 Потрясающую Midnight тему
- 🟢 Классическую Matrix тему

**Добро пожаловать в WARDEN v3.0!** 🔍✨
