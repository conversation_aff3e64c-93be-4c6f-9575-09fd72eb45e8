#!/bin/bash

echo "========================================"
echo "🔍 WARDEN v3.0 - Intelligence Platform"
echo "🎨 With Advanced Theme System"
echo "========================================"
echo

echo "[LAUNCHER] Checking Java version..."
java -version
echo

echo "[LAUNCHER] Starting WARDEN with JavaFX support..."
echo

# Попробуем запустить через Maven (рекомендуемый способ)
echo "[INFO] Attempting to run via Maven..."
mvn clean compile javafx:run

# Если Maven не работает, попробуем прямой запуск
if [ $? -ne 0 ]; then
    echo
    echo "[WARNING] Maven execution failed. Trying direct Java execution..."
    echo
    
    # Компилируем проект
    echo "[INFO] Compiling project..."
    mvn clean compile
    
    # Определяем путь к JavaFX (попробуем несколько вариантов)
    JAVAFX_PATH=""
    
    if [ -d "/usr/share/openjfx/lib" ]; then
        JAVAFX_PATH="/usr/share/openjfx/lib"
    elif [ -d "$HOME/javafx-sdk-17/lib" ]; then
        JAVAFX_PATH="$HOME/javafx-sdk-17/lib"
    elif [ -d "/opt/javafx-sdk-17/lib" ]; then
        JAVAFX_PATH="/opt/javafx-sdk-17/lib"
    else
        echo "[ERROR] JavaFX SDK not found. Please install JavaFX SDK."
        echo
        echo "Download from: https://openjfx.io/"
        echo "Extract to: $HOME/javafx-sdk-17/"
        echo
        exit 1
    fi
    
    echo "[INFO] Using JavaFX from: $JAVAFX_PATH"
    
    # Запускаем с JavaFX параметрами
    echo "[INFO] Running with JavaFX parameters..."
    java --module-path "$JAVAFX_PATH" --add-modules javafx.controls,javafx.fxml -cp "target/classes:target/dependency/*" com.warden.Launcher
    
    if [ $? -ne 0 ]; then
        echo
        echo "[ERROR] Direct execution also failed."
        echo
        echo "========================================"
        echo "TROUBLESHOOTING GUIDE:"
        echo "========================================"
        echo
        echo "1. Install JavaFX SDK:"
        echo "   wget https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_linux-x64_bin-sdk.zip"
        echo "   unzip openjfx-17.0.2_linux-x64_bin-sdk.zip"
        echo "   mv javafx-sdk-17.0.2 \$HOME/javafx-sdk-17"
        echo
        echo "2. Make sure Java 17+ is installed:"
        echo "   sudo apt install openjdk-17-jdk  # Ubuntu/Debian"
        echo "   sudo yum install java-17-openjdk  # CentOS/RHEL"
        echo
        echo "3. Run: mvn clean compile javafx:run"
        echo
        echo "Alternative VM arguments for IDE:"
        echo "--module-path \$HOME/javafx-sdk-17/lib"
        echo "--add-modules javafx.controls,javafx.fxml"
        echo
        echo "========================================"
        exit 1
    fi
fi

echo
echo "[SUCCESS] WARDEN started successfully!"
echo "🎨 Try the new theme system - switch between Matrix and Midnight themes!"
echo
