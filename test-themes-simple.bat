@echo off
echo ========================================
echo 🧪 WARDEN Theme System Test (No GUI)
echo ========================================
echo.

echo [TEST] Checking Java...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java not found!
    pause
    exit /b 1
)
echo ✅ Java found
echo.

echo [TEST] Creating target directory...
if not exist "target\classes" mkdir target\classes

echo [TEST] Compiling theme system...

REM Компилируем Logger (нужен для ThemeManager)
javac -d target\classes src\main\java\com\warden\osint\utils\Logger.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile Logger
    pause
    exit /b 1
)

REM Компилируем ThemeManager
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\utils\ThemeManager.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile ThemeManager
    pause
    exit /b 1
)

REM Компилируем тест
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\test\ThemeTest.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile ThemeTest
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

echo [TEST] Running theme system test...
echo.

java -cp target\classes com.warden.osint.test.ThemeTest

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ THEME SYSTEM TEST PASSED!
    echo ========================================
    echo.
    echo 🎨 Theme system is working correctly!
    echo.
    echo Next steps:
    echo 1. Install JavaFX SDK (see JAVAFX_SETUP.md)
    echo 2. Run: run-warden-simple.bat
    echo 3. Try switching themes in the GUI!
    echo.
) else (
    echo.
    echo ❌ Theme test failed!
    echo Please check the error messages above.
    echo.
)

pause
