# 🔍 WARDEN API Database Finder

## 🚀 Программа для поиска API источников с базами данных

### 🎯 **Что это такое:**

**API Database Finder** - это программа, которая **ищет сами API с базами данных**, а не пробивает данные. Она находит источники API для дальнейшего использования в OSINT.

### 🔍 **Что программа ищет:**

#### **📱 Телефонные API:**
- API для поиска по номерам телефонов
- Базы данных с контактной информацией
- Reverse phone lookup сервисы
- Telegram боты для поиска номеров

#### **📧 Email API:**
- Email verification сервисы
- Базы данных email адресов
- Email finder API
- Breach detection сервисы

#### **🔒 Базы утечек:**
- API для проверки утечек данных
- Breach databases
- Leaked credentials API
- Password breach checkers

#### **👥 Социальные сети API:**
- API социальных платформ
- People search engines
- Social media databases
- Profile lookup services

#### **💰 Криптовалютные API:**
- Blockchain explorers
- Crypto address lookup
- Transaction analysis API
- Wallet information services

#### **📁 GitHub репозитории:**
- Open source OSINT tools
- Database projects
- API collections
- Research repositories

### 🛠️ **Как работает поиск:**

#### **1. Поисковые системы:**
```
🔍 Google: "phone database API", "email database API"
🔍 Bing: "OSINT database API", "breach database API"  
🔍 DuckDuckGo: "telegram database API", "people search API"
🔍 Yahoo: "reverse phone lookup API"
🔍 Yandex: "contact database API"
```

#### **2. GitHub поиск:**
```
📁 Репозитории с ключевыми словами:
   - "phone database API"
   - "email database API" 
   - "OSINT database"
   - "telegram database"
   - "breach database"
   - "people search API"
```

#### **3. Каталоги API:**
```
📚 RapidAPI: поиск по категориям
📚 ProgrammableWeb: каталог API
📚 Public APIs: открытые API
📚 APIList: коллекции API
```

#### **4. Telegram каналы:**
```
📱 @api_databases
📱 @osint_apis
📱 @leaked_apis
📱 @phone_apis
📱 @email_apis
📱 @database_apis
```

### 📊 **Результаты поиска:**

#### **Таблица найденных API:**
```
┌──────┬─────────────────────┬─────────────────────────┬──────────┬─────────┬─────────────┐
│ Тип  │ Название API        │ URL                     │ Записей  │ Качество│ Статус      │
├──────┼─────────────────────┼─────────────────────────┼──────────┼─────────┼─────────────┤
│ 📱   │ UsersBox API        │ api.usersbox.net        │ 1.5M     │ 95/100  │ ✅ Проверен │
│ 📧   │ Hunter.io API       │ api.hunter.io           │ 200M     │ 92/100  │ ✅ Проверен │
│ 🔒   │ HaveIBeenPwned API  │ haveibeenpwned.com/api  │ 12B      │ 98/100  │ ✅ Проверен │
│ 👥   │ Pipl API            │ api.pipl.com            │ 3B       │ 94/100  │ ✅ Проверен │
│ 💰   │ Blockchain.info API │ api.blockchain.info     │ 850M     │ 96/100  │ ✅ Проверен │
│ 🌍   │ Shodan API          │ api.shodan.io           │ 45M      │ 95/100  │ ✅ Проверен │
│ 📱   │ TrueCaller API      │ api.truecaller.com      │ 850K     │ 90/100  │ ✅ Проверен │
│ 📁   │ OSINT-Tools Repo    │ github.com/jivoi/...    │ -        │ 85/100  │ ❓ Не проверен│
└──────┴─────────────────────┴─────────────────────────┴──────────┴─────────┴─────────────┘
```

#### **Статистика по типам:**
```
📊 СТАТИСТИКА ПО ТИПАМ БД:
  📱 Телефоны: 15 API, 4.6M записей
  📧 Email: 8 API, 2.1M записей
  🔒 Утечки: 5 API, 12B записей
  👥 Люди: 6 API, 3.2B записей
  💰 Криптовалюты: 3 API, 850M записей
  🌍 IP/Сети: 7 API, 145M записей
  📁 GitHub репо: 25 проектов
```

#### **Топ лучших API:**
```
🏆 ТОП-10 ЛУЧШИХ API:
1. HaveIBeenPwned API - ✅ 12B записей (Утечки) - ⭐98/100
2. Blockchain.info API - ✅ 850M записей (Криптовалюты) - ⭐96/100
3. UsersBox API - ✅ 1.5M записей (Телефоны) - ⭐95/100
4. Shodan API - ✅ 45M записей (IP/Сети) - ⭐95/100
5. Pipl API - ✅ 3B записей (Люди) - ⭐94/100
6. Hunter.io API - ✅ 200M записей (Email) - ⭐92/100
7. TrueCaller API - ✅ 850K записей (Телефоны) - ⭐90/100
8. Clearbit API - ✅ 150M записей (Email) - ⭐88/100
9. VirusTotal API - ✅ 25M записей (IP/Сети) - ⭐90/100
10. NumVerify API - ✅ 500K записей (Телефоны) - ⭐85/100
```

### 🚀 **Как использовать:**

#### **1. Запуск:**
```cmd
run-api-finder.bat
```

#### **2. Поиск API:**
1. Нажмите **"🚀 Найти API с базами данных"**
2. Дождитесь завершения поиска (30-60 секунд)
3. Просмотрите результаты в таблице

#### **3. Работа с результатами:**
- **Одинарный клик** - выбор API для просмотра деталей
- **Двойной клик** - копирование URL в буфер обмена
- **Вкладки**: Результаты / Логи / Детали

#### **4. Фильтрация результатов:**
- ✅ **Проверенные API** - готовы к использованию
- ❓ **Непроверенные API** - требуют дополнительной проверки
- 🏆 **Высокое качество** (80+ баллов) - лучшие источники
- 💎 **Большие БД** (1M+ записей) - самые ценные

### 💡 **Практическое применение:**

#### **Для OSINT исследователей:**
```
1. Найти API с телефонными базами
2. Получить список email verification сервисов
3. Найти новые источники утечек данных
4. Обнаружить GitHub репозитории с инструментами
5. Получить доступ к криптовалютным API
```

#### **Для разработчиков:**
```
1. Интеграция найденных API в свои проекты
2. Создание агрегаторов данных
3. Построение OSINT платформ
4. Автоматизация сбора данных
```

#### **Для исследователей безопасности:**
```
1. Мониторинг новых источников утечек
2. Анализ доступности персональных данных
3. Исследование surface web источников
4. Оценка рисков утечки информации
```

### 🔧 **Технические особенности:**

#### **Многопоточный поиск:**
- **30 параллельных потоков**
- **Поиск по 5 поисковым системам**
- **Сканирование GitHub API**
- **Проверка каталогов API**
- **Анализ Telegram каналов**

#### **Умный анализ:**
- **Автоматическое определение типа API**
- **Оценка количества записей в БД**
- **Расчет качества источника**
- **Проверка доступности**
- **Извлечение метаданных**

#### **Результаты:**
- **Экспорт в таблицу**
- **Детальная информация по каждому API**
- **Статистика по типам и источникам**
- **Копирование URL одним кликом**

### ⚠️ **Важные замечания:**

#### **Легальность:**
- Используйте только для законных целей
- Соблюдайте Terms of Service найденных API
- Не нарушайте авторские права
- Получайте разрешения при необходимости

#### **Качество данных:**
- ✅ **Проверенные API** - протестированы и работают
- ❓ **Непроверенные API** - найдены автоматически, требуют проверки
- 🏆 **Высокие оценки** - основаны на репутации и размере БД

---

**🔍 WARDEN API Database Finder** - Найди лучшие API источники для OSINT! 🚀📊✨

**Больше не нужно искать API вручную - программа сделает это за тебя!** 💪
