@echo off
echo ========================================
echo 🔧 Компиляция рабочих модулей WARDEN
echo ========================================
echo.

echo [COMPILE] Создание директорий...
if not exist "target\classes" mkdir target\classes

echo [COMPILE] Компиляция утилит...
javac -d target\classes src\main\java\com\warden\osint\utils\Logger.java
javac -d target\classes src\main\java\com\warden\osint\utils\Localization.java
javac -d target\classes src\main\java\com\warden\osint\utils\HWIDGenerator.java

echo [COMPILE] Компиляция Telegram API...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramUserInfo.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramChannelMatch.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramAccount.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramGroupInfo.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramChannelActivity.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java

echo [COMPILE] Компиляция Swing GUI...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingWardenDemo.java

echo.
echo ✅ Компиляция завершена!
echo.
echo 🚀 Для запуска Swing версии:
echo java -cp target\classes com.warden.osint.gui.swing.SwingWardenDemo
echo.
echo 🧪 Для тестирования тем:
echo java -cp target\classes com.warden.osint.test.ThemeTest
echo.
pause
