@echo off
echo ========================================
echo 🔧 КОМПИЛЯЦИЯ ОСНОВНОЙ WARDEN OSINT
echo 🚀 Восстановление оригинальной программы
echo ========================================
echo.

REM Создаем директории
if not exist "target\classes" mkdir target\classes
if not exist "target\lib" mkdir target\lib

echo [STEP 1] Скачивание основных зависимостей...

REM Скачиваем Jackson
if not exist "target\lib\jackson-core-2.15.2.jar" (
    echo Downloading Jackson Core...
    curl -L -o target\lib\jackson-core-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar
)

if not exist "target\lib\jackson-databind-2.15.2.jar" (
    echo Downloading Jackson Databind...
    curl -L -o target\lib\jackson-databind-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar
)

if not exist "target\lib\jackson-annotations-2.15.2.jar" (
    echo Downloading Jackson Annotations...
    curl -L -o target\lib\jackson-annotations-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar
)

REM Создаем classpath
set CLASSPATH=target\lib\*;target\classes

echo.
echo [STEP 2] Компиляция основных модулей...

echo Compiling utilities...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling authentication...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling API modules...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\UsersBoxAPI.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling chat system...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling GUI (JavaFX)...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\*.java 2>nul
REM Игнорируем ошибки JavaFX если нет SDK

echo Compiling Swing GUI...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\swing\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling main launcher...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\*.java 2>nul
REM Игнорируем ошибки JavaFX если нет SDK

echo.
echo ✅ Компиляция завершена успешно!
echo.
echo 📊 Скомпилированные модули:
echo   ✅ Utilities (Logger, Localization, HWID)
echo   ✅ Authentication (User, AuthManager)
echo   ✅ API (UsersBox, Telegram OSINT)
echo   ✅ Chat System
echo   ✅ Swing GUI
echo   ❓ JavaFX GUI (если доступен JavaFX SDK)
echo.
echo 🚀 Команды для запуска:
echo   Swing Demo: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingWardenDemo
echo   Theme Test: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingThemeManager
echo.
echo 💡 Для запуска основного JavaFX приложения нужен JavaFX SDK
echo.
pause
goto :end

:compile_error
echo ❌ Ошибка компиляции!
echo.
echo Возможные причины:
echo - Отсутствует Java JDK
echo - Проблемы с загрузкой зависимостей
echo - Синтаксические ошибки в коде
echo.
echo Попробуйте:
echo 1. Проверить подключение к интернету
echo 2. Запустить от имени администратора
echo 3. Проверить версию Java (требуется JDK 11+)
echo.
pause
exit /b 1

:end
