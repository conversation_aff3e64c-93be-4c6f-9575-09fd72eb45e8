# 🎨 WARDEN Theme System v3.0

## Обзор

WARDEN v3.0 теперь поддерживает продвинутую систему тем, позволяющую пользователям переключаться между различными цветовыми схемами интерфейса в реальном времени.

## 🌟 Доступные темы

### 🟢 Matrix Theme (По умолчанию)
- **Описание**: Классическая зеленая тема в стиле хакера
- **Цветовая схема**: 
  - Основной: `#00ff00` (ярко-зеленый)
  - Вторичный: `#00ccff` (голубой)
  - Акцент: `#ffff00` (желтый)
  - Фон: `#000000` (черный)
- **Особенности**:
  - Высокий контраст для лучшей читаемости
  - Ретро терминальный стиль
  - Неоновые эффекты свечения
  - Классический хакерский вид

### 🌙 Midnight Theme (Новая!)
- **Описание**: Элегантная полуночная тема с фиолетово-синими оттенками
- **Цветовая схема**:
  - Основной: `#bb86fc` (фиолетовый)
  - Вторичный: `#03dac6` (бирюзовый)
  - Акцент: `#cf6679` (розовый)
  - Фон: `#121212` (темно-серый)
- **Особенности**:
  - Современный дизайн
  - Плавные градиенты
  - Профессиональный внешний вид
  - Приятные для глаз цвета

## 🚀 Как использовать

### В LoginWindow
1. На экране входа найдите кнопку **"🌙 Switch to Midnight Theme"**
2. Нажмите для переключения на полуночную тему
3. Все элементы интерфейса обновятся с плавной анимацией

### В MainWindow
1. В левой панели функций найдите кнопку **"🎨 Тема"**
2. Нажмите для переключения между темами
3. Система покажет уведомление об успешном переключении

### Горячие клавиши
- `Ctrl+T` - Быстрое переключение темы (планируется)

## 🛠️ Техническая реализация

### ThemeManager
Центральный класс для управления темами:

```java
ThemeManager themeManager = ThemeManager.getInstance();

// Переключение темы
themeManager.toggleTheme();

// Применение темы к элементу
themeManager.applyThemeToNode(button, "button", true);

// Получение цветов текущей темы
String primaryColor = themeManager.getPrimaryColor();
String backgroundColor = themeManager.getBackgroundColor();
```

### Поддерживаемые типы элементов
- `root` - Основные контейнеры
- `panel` - Панели и блоки
- `login_panel` - Специальная панель входа
- `title` - Заголовки
- `subtitle` - Подзаголовки
- `status` - Статусные элементы
- `terminal` - Терминальные области
- `input` - Поля ввода
- `button` - Кнопки
- `chat_panel` - Элементы чата
- `admin_panel` - Админские элементы

### Анимации
Все переключения тем сопровождаются плавными анимациями:
- Затухание (200ms)
- Применение новой темы
- Появление (300ms)

## 🎯 Особенности

### Автоматическое применение
- Темы применяются ко всем элементам интерфейса автоматически
- Рекурсивное обновление всех дочерних элементов
- Сохранение пользовательских настроек

### Адаптивность
- Темы адаптируются под различные разрешения экрана
- Сохранение читаемости на всех устройствах
- Оптимизация для различных размеров шрифтов

### Расширяемость
Система легко расширяется для добавления новых тем:

```java
public enum Theme {
    MATRIX("Matrix", "🟢 Matrix Theme", "Классическая зеленая тема"),
    MIDNIGHT("Midnight", "🌙 Midnight Theme", "Элегантная полуночная тема"),
    // Добавьте новую тему здесь
    CYBERPUNK("Cyberpunk", "🔥 Cyberpunk Theme", "Неоновая киберпанк тема");
}
```

## 🔧 Настройка и кастомизация

### Добавление новых цветов
В `ThemeManager.java` добавьте новые методы для цветов:

```java
public String getNewColor() {
    return currentTheme == Theme.MATRIX ? "#matrix_color" : "#midnight_color";
}
```

### Создание новых типов элементов
Добавьте новый тип в `initializeThemeAppliers()`:

```java
themeAppliers.put("new_element", this::applyNewElementTheme);
```

## 📱 Совместимость

- **JavaFX**: 17.0.2+
- **Java**: 17+
- **Операционные системы**: Windows, macOS, Linux
- **Разрешения экрана**: 1024x768 и выше

## 🐛 Известные ограничения

1. Некоторые системные элементы могут не поддерживать полную кастомизацию
2. Анимации могут быть медленными на старых системах
3. Темы применяются только к элементам JavaFX

## 🔮 Планы развития

### Версия 3.1
- [ ] Добавление Cyberpunk темы
- [ ] Пользовательские темы
- [ ] Импорт/экспорт тем
- [ ] Горячие клавиши для переключения

### Версия 3.2
- [ ] Автоматическое переключение по времени суток
- [ ] Синхронизация тем между устройствами
- [ ] Темы для различных модулей
- [ ] Анимированные фоны

## 🎨 Демонстрация

Для тестирования системы тем запустите:

```bash
java -cp target/classes com.warden.osint.demo.ThemeDemo
```

Это откроет демонстрационное окно с возможностью переключения тем в реальном времени.

## 📞 Поддержка

При возникновении проблем с темами:
1. Проверьте логи приложения
2. Перезапустите приложение
3. Обратитесь к администраторам:
   - Telegram: @InfernoSoulAttack
   - Telegram: @Svuanstvo

---

**WARDEN v3.0** - Advanced Intelligence Platform with Beautiful Themes 🎨✨
