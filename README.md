# 🔍 WARDEN v3.0 - Intelligence Platform

## 🎨 NEW: Advanced Theme System!

WARDEN v3.0 теперь поддерживает продвинутую систему тем с возможностью переключения между **Matrix** (зеленая) и **Midnight** (фиолетово-синяя) темами в реальном времени!

### ✨ Особенности новой системы тем:
- 🟢 **Matrix Theme** - классическая зеленая тема в стиле хакера
- 🌙 **Midnight Theme** - элегантная полуночная тема с фиолетово-синими оттенками
- 🎭 Плавные анимации переключения
- 🎯 Автоматическое применение ко всем элементам интерфейса
- 🔧 Легко расширяемая архитектура

## 🚀 Быстрый старт

### Вариант 1: Использование Maven (Рекомендуется)
```bash
# Компиляция и запуск
mvn clean compile javafx:run
```

### Вариант 2: Использование готовых скриптов
```bash
# Windows
run-warden.bat

# Linux/Mac
chmod +x run-warden.sh
./run-warden.sh
```

### Вариант 3: Тестирование системы тем
```bash
# Тест без GUI (проверка логики)
test-themes.bat  # Windows
./test-themes.sh # Linux/Mac

# Демо с GUI
run-theme-demo.bat  # Windows
./run-theme-demo.sh # Linux/Mac
```

## 📋 Требования

- **Java**: 17 или выше
- **JavaFX**: 17.0.2 (автоматически загружается Maven)
- **Maven**: 3.6+ (для сборки)
- **ОС**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)

## 🛠️ Установка JavaFX (если нужно)

### Windows:
1. Скачайте JavaFX SDK: https://openjfx.io/
2. Распакуйте в `C:\javafx-sdk-17\`
3. Запустите `run-warden.bat`

### Linux:
```bash
# Ubuntu/Debian
sudo apt install openjfx

# Или скачайте SDK
wget https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_linux-x64_bin-sdk.zip
unzip openjfx-17.0.2_linux-x64_bin-sdk.zip
mv javafx-sdk-17.0.2 ~/javafx-sdk-17
```

### macOS:
```bash
# Homebrew
brew install openjfx

# Или скачайте SDK с https://openjfx.io/
```

## 🎨 Как использовать темы

### В окне входа:
1. Найдите кнопку **"🌙 Switch to Midnight Theme"**
2. Нажмите для переключения на полуночную тему
3. Все элементы обновятся с плавной анимацией

### В основном окне:
1. В левой панели найдите кнопку **"🎨 Тема"**
2. Нажмите для переключения между темами
3. Наслаждайтесь новым внешним видом!

## 🏗️ Архитектура проекта

```
warden/
├── src/main/java/com/warden/
│   ├── Launcher.java              # Точка входа
│   ├── Main1.java                 # JavaFX Application
│   └── osint/
│       ├── api/                   # API интеграции
│       │   └── UsersBoxAPI.java   # OSINT API
│       ├── auth/                  # Аутентификация
│       │   ├── AuthManager.java   # Firebase Auth
│       │   └── User.java          # Модель пользователя
│       ├── chat/                  # Система чата
│       ├── gui/                   # Интерфейс
│       │   ├── LoginWindow.java   # Окно входа
│       │   ├── MainWindow.java    # Главное окно
│       │   └── AdminPanel.java    # Админка
│       ├── utils/                 # Утилиты
│       │   ├── ThemeManager.java  # 🎨 Система тем
│       │   ├── Localization.java  # Локализация
│       │   ├── Logger.java        # Логирование
│       │   └── HWIDGenerator.java # HWID
│       ├── demo/                  # Демонстрации
│       │   └── ThemeDemo.java     # 🎨 Демо тем
│       └── test/                  # Тесты
│           └── ThemeTest.java     # 🧪 Тест тем
├── run-warden.bat/.sh            # Скрипты запуска
├── test-themes.bat/.sh           # Тесты тем
└── THEME_SYSTEM.md               # 📖 Документация тем
```

## 🔧 Функциональность

### 🔍 OSINT возможности:
- 📱 Поиск по номеру телефона
- 📧 Поиск по email (планируется)
- 👤 Поиск по username (планируется)
- 🌐 IP lookup (планируется)

### 🔐 Безопасность:
- Firebase Authentication
- HWID привязка к железу
- Система ролей (admin/user)
- Логирование всех действий

### 💬 Коммуникация:
- Командный чат для аналитиков
- Система ролей с иконками
- Поддержка команд
- Онлайн статусы

### 👑 Администрирование:
- Панель управления пользователями
- Одобрение новых аккаунтов
- Блокировка пользователей
- Мониторинг активности

## 🎯 Новые возможности v3.0

### 🎨 Система тем:
- **Matrix Theme**: Классическая зеленая тема хакера
- **Midnight Theme**: Современная фиолетово-синяя тема
- Плавные анимации переключения
- Автоматическое применение ко всем элементам

### 🌐 Локализация:
- Поддержка русского и английского языков
- Переключение языка в реальном времени
- Более 100 переводов

### ⚡ Производительность:
- Оптимизированный рендеринг
- Асинхронные API запросы
- Улучшенное управление памятью

## 🐛 Решение проблем

### "JavaFX runtime components are missing"
```bash
# Решение 1: Используйте Maven
mvn javafx:run

# Решение 2: Используйте скрипты
run-warden.bat  # Windows
./run-warden.sh # Linux/Mac

# Решение 3: Установите JavaFX SDK
# Скачайте с https://openjfx.io/ и следуйте инструкциям выше
```

### Проблемы с компиляцией
```bash
# Очистите и пересоберите
mvn clean compile

# Проверьте версию Java
java -version  # Должна быть 17+

# Проверьте Maven
mvn -version
```

### Проблемы с темами
```bash
# Запустите тест тем
test-themes.bat  # Windows
./test-themes.sh # Linux/Mac

# Проверьте логи в консоли
```

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в консоли
2. Убедитесь, что Java 17+ установлена
3. Попробуйте `mvn clean compile javafx:run`
4. Обратитесь к администраторам:
   - Telegram: @InfernoSoulAttack
   - Telegram: @Svuanstvo

## 📈 Планы развития

### v3.1:
- [ ] Cyberpunk тема
- [ ] Пользовательские темы
- [ ] Горячие клавиши для тем
- [ ] Автопереключение по времени

### v3.2:
- [ ] Больше OSINT источников
- [ ] Расширенный чат
- [ ] Мобильная версия
- [ ] API для интеграций

---

**WARDEN v3.0** - Advanced Intelligence Platform with Beautiful Themes 🎨✨

*Создано для профессиональных OSINT аналитиков и исследователей*
