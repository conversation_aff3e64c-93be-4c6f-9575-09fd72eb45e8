@echo off
echo ========================================
echo 🔍 WARDEN API Database Finder
echo 🚀 Поиск API источников с базами данных
echo ========================================
echo.

echo [COMPILE] Компиляция API Database Finder...

REM Создаем директории
if not exist "target\classes" mkdir target\classes

REM Компилируем утилиты
echo Compiling utilities...
javac -d target\classes src\main\java\com\warden\osint\utils\Logger.java 2>nul
javac -d target\classes src\main\java\com\warden\osint\utils\Localization.java 2>nul

REM Компилируем Swing Theme Manager
echo Compiling Swing Theme Manager...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

REM Компилируем API Finder модели
echo Compiling API Finder models...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APISource.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinderResult.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

REM Компилируем API Finder core
echo Compiling API Finder core...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinder.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

REM Компилируем GUI
echo Compiling API Finder GUI...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIFinderGUI.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo ✅ Compilation successful!
echo.

echo [LAUNCH] Запуск API Database Finder...
echo.
echo 🔍 Эта программа найдет API источники с базами данных:
echo   📱 Телефонные базы данных
echo   📧 Email базы данных  
echo   🔒 Базы утечек данных
echo   👥 Социальные сети API
echo   💰 Криптовалютные API
echo   📁 GitHub репозитории с API
echo.

REM Запускаем API Finder
java -cp target\classes com.warden.osint.gui.swing.APIFinderGUI

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start API Finder
    pause
    exit /b 1
)

echo.
echo ✅ API Finder started successfully!
pause
goto :end

:compile_error
echo ❌ Compilation error occurred!
echo.
echo Возможные причины:
echo - Отсутствует Java JDK
echo - Неправильные пути к файлам
echo - Синтаксические ошибки в коде
echo.
pause
exit /b 1

:end
