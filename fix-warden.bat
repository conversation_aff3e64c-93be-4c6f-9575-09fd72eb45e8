@echo off
echo ========================================
echo 🔧 FIXING WARDEN OSINT - Download JavaFX
echo ========================================
echo.

REM Создаем директории
if not exist "target\classes" mkdir target\classes
if not exist "target\lib" mkdir target\lib

echo [DOWNLOAD] Downloading JavaFX dependencies...

REM JavaFX Base
if not exist "target\lib\javafx-base-19.jar" (
    echo Downloading JavaFX Base...
    curl -L -o target\lib\javafx-base-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-base/19/javafx-base-19.jar
)

REM JavaFX Controls
if not exist "target\lib\javafx-controls-19.jar" (
    echo Downloading JavaFX Controls...
    curl -L -o target\lib\javafx-controls-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-controls/19/javafx-controls-19.jar
)

REM JavaFX Graphics
if not exist "target\lib\javafx-graphics-19.jar" (
    echo Downloading JavaFX Graphics...
    curl -L -o target\lib\javafx-graphics-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-graphics/19/javafx-graphics-19.jar
)

REM JavaFX FXML
if not exist "target\lib\javafx-fxml-19.jar" (
    echo Downloading JavaFX FXML...
    curl -L -o target\lib\javafx-fxml-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-fxml/19/javafx-fxml-19.jar
)

REM Jackson Core
if not exist "target\lib\jackson-core-2.15.2.jar" (
    echo Downloading Jackson Core...
    curl -L -o target\lib\jackson-core-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar
)

REM Jackson Databind
if not exist "target\lib\jackson-databind-2.15.2.jar" (
    echo Downloading Jackson Databind...
    curl -L -o target\lib\jackson-databind-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar
)

REM Jackson Annotations
if not exist "target\lib\jackson-annotations-2.15.2.jar" (
    echo Downloading Jackson Annotations...
    curl -L -o target\lib\jackson-annotations-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar
)

echo.
echo [COMPILE] Compiling WARDEN modules...

set CLASSPATH=target\lib\*;target\classes

echo Compiling utilities...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\Logger.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\Localization.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\HWIDGenerator.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\ThemeManager.java

echo Compiling authentication...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\User.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\AuthManager.java

echo Compiling API modules...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\UsersBoxAPI.java

echo Compiling chat system...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\ChatMessage.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\ChatService.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\ChatUtils.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\MessageRenderer.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\ChatWindow.java

echo Compiling GUI...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\LoginWindow.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\AdminPanel.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\MainWindow.java

echo Compiling main launcher...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\Main1.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\Launcher.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo.
echo ✅ All modules compiled successfully!
echo.

echo [LAUNCH] Starting WARDEN...
java --module-path target\lib --add-modules javafx.controls,javafx.fxml -cp "%CLASSPATH%" com.warden.Launcher

pause
