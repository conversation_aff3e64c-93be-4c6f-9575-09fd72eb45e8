@echo off
echo ========================================
echo 🔧 ПОЛНАЯ КОМПИЛЯЦИЯ WARDEN OSINT
echo 🚀 Компиляция всех модулей с зависимостями
echo ========================================
echo.

REM Создаем директории
if not exist "target\classes" mkdir target\classes
if not exist "target\lib" mkdir target\lib

echo [STEP 1] Скачивание зависимостей...
echo Downloading Jackson Core...
curl -L -o target\lib\jackson-core-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar

echo Downloading Jackson Databind...
curl -L -o target\lib\jackson-databind-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar

echo Downloading Jackson Annotations...
curl -L -o target\lib\jackson-annotations-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar

echo Downloading Firebase Admin SDK...
curl -L -o target\lib\firebase-admin-9.2.0.jar https://repo1.maven.org/maven2/com/google/firebase/firebase-admin/9.2.0/firebase-admin-9.2.0.jar

echo Downloading Google Auth Library...
curl -L -o target\lib\google-auth-library-oauth2-http-1.19.0.jar https://repo1.maven.org/maven2/com/google/auth/google-auth-library-oauth2-http/1.19.0/google-auth-library-oauth2-http-1.19.0.jar

REM Создаем classpath
set CLASSPATH=target\lib\*;target\classes

echo.
echo [STEP 2] Компиляция утилит...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo [STEP 3] Компиляция аутентификации...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo [STEP 4] Компиляция API модулей...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramUserInfo.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramChannelMatch.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramAccount.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramGroupInfo.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramChannelActivity.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\UsersBoxAPI.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo [STEP 5] Компиляция чата...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo [STEP 6] Компиляция Swing GUI...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\swing\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo [STEP 7] Компиляция JavaFX GUI...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\*.java 2>nul
REM Игнорируем ошибки JavaFX если нет SDK

echo [STEP 8] Компиляция основного лаунчера...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\*.java 2>nul
REM Игнорируем ошибки JavaFX если нет SDK

echo.
echo ✅ Компиляция завершена успешно!
echo.
echo 📊 Доступные модули:
echo   🔧 Swing GUI (работает без JavaFX)
echo   📱 Telegram OSINT
echo   🔍 API Scanner  
echo   🔍 API Finder
echo   💬 Chat System
echo   🔐 Authentication
echo.
echo 🚀 Команды для запуска:
echo   Swing Demo: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingWardenDemo
echo   API Scanner: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.APIScannerGUI
echo   API Finder: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.APIFinderGUI
echo   Theme Test: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingThemeManager
echo.
pause
goto :end

:compile_error
echo ❌ Ошибка компиляции!
echo.
echo Возможные причины:
echo - Отсутствует Java JDK
echo - Проблемы с загрузкой зависимостей
echo - Синтаксические ошибки в коде
echo.
echo Попробуйте:
echo 1. Проверить подключение к интернету
echo 2. Запустить от имени администратора
echo 3. Проверить версию Java (требуется JDK 11+)
echo.
pause
exit /b 1

:end
