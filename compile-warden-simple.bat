@echo off
echo ========================================
echo 🔧 БЫСТРАЯ КОМПИЛЯЦИЯ WARDEN
echo 🚀 Компиляция без внешних зависимостей
echo ========================================
echo.

REM Создаем директории
if not exist "target\classes" mkdir target\classes

echo [COMPILE] Компиляция утилит...
javac -d target\classes src\main\java\com\warden\osint\utils\Logger.java 2>nul
javac -d target\classes src\main\java\com\warden\osint\utils\Localization.java 2>nul
javac -d target\classes src\main\java\com\warden\osint\utils\HWIDGenerator.java 2>nul

echo [COMPILE] Компиляция Swing Theme Manager...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo [COMPILE] Компиляция Telegram API (без Jackson)...
REM Временно создаем заглушки для Jackson
echo package com.fasterxml.jackson.annotation; public @interface JsonProperty { String value() default ""; } > temp_JsonProperty.java
echo package com.fasterxml.jackson.annotation; public @interface JsonIgnoreProperties { boolean ignoreUnknown() default false; } > temp_JsonIgnoreProperties.java
echo package com.fasterxml.jackson.databind; public class ObjectMapper { } > temp_ObjectMapper.java
echo package com.fasterxml.jackson.databind; public class JsonNode { } > temp_JsonNode.java

javac -d target\classes temp_*.java
del temp_*.java

javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramUserInfo.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramChannelMatch.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramAccount.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramGroupInfo.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramChannelActivity.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java

echo [COMPILE] Компиляция API Scanner...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIEndpointResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanner.java

echo [COMPILE] Компиляция API Finder...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APISource.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinderResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinder.java

echo [COMPILE] Компиляция Swing GUI...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingWardenDemo.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIScannerGUI.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIFinderGUI.java

if %ERRORLEVEL% NEQ 0 goto :compile_error

echo.
echo ✅ Быстрая компиляция завершена!
echo.
echo ⚠️  ВНИМАНИЕ: Скомпилированы только Swing модули без Firebase/Jackson
echo.
echo 🚀 Доступные команды:
echo   Swing Demo: java -cp target\classes com.warden.osint.gui.swing.SwingWardenDemo
echo   API Scanner: java -cp target\classes com.warden.osint.gui.swing.APIScannerGUI  
echo   API Finder: java -cp target\classes com.warden.osint.gui.swing.APIFinderGUI
echo.
echo 💡 Для полной функциональности используйте: compile-full-warden.bat
echo.
pause
goto :end

:compile_error
echo ❌ Ошибка компиляции!
pause
exit /b 1

:end
