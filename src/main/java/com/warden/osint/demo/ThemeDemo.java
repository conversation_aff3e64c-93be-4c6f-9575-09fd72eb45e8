package com.warden.osint.demo;

import com.warden.osint.utils.ThemeManager;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.stage.Stage;

/**
 * Демонстрация системы тем WARDEN
 * Показывает переключение между Matrix и Midnight темами
 */
public class ThemeDemo extends Application {
    
    private ThemeManager themeManager;
    private VBox root;
    private Label titleLabel;
    private Label subtitleLabel;
    private Label statusLabel;
    private TextArea terminalArea;
    private TextField inputField;
    private Button themeButton;
    private Button testButton;
    
    @Override
    public void start(Stage primaryStage) {
        themeManager = ThemeManager.getInstance();
        
        initializeUI();
        setupEventHandlers();
        
        Scene scene = new Scene(root, 800, 600);
        primaryStage.setScene(scene);
        primaryStage.setTitle("WARDEN Theme Demo");
        primaryStage.show();
        
        // Применяем начальную тему
        applyCurrentTheme();
    }
    
    private void initializeUI() {
        root = new VBox(20);
        root.setAlignment(Pos.CENTER);
        root.setPadding(new Insets(30));
        
        // Заголовок
        titleLabel = new Label("🔍 WARDEN THEME DEMO");
        titleLabel.setStyle("-fx-font-size: 24px;");
        
        // Подзаголовок
        subtitleLabel = new Label("🎨 Advanced Theme System Demonstration");
        subtitleLabel.setStyle("-fx-font-size: 16px;");
        
        // Статус
        statusLabel = new Label("🟢 THEME SYSTEM ONLINE | ✨ READY FOR SWITCHING");
        statusLabel.setStyle("-fx-font-size: 12px;");
        
        // Терминальная область
        terminalArea = new TextArea();
        terminalArea.setEditable(false);
        terminalArea.setPrefRowCount(10);
        terminalArea.setText(
            "╔══════════════════════════════════════════════════════════════════════════════╗\n" +
            "║                        🎨 WARDEN THEME SYSTEM DEMO                          ║\n" +
            "╚══════════════════════════════════════════════════════════════════════════════╝\n\n" +
            "🟢 Matrix Theme Features:\n" +
            "  • Classic green hacker aesthetic\n" +
            "  • High contrast for readability\n" +
            "  • Retro terminal styling\n" +
            "  • Neon glow effects\n\n" +
            "🌙 Midnight Theme Features:\n" +
            "  • Modern purple-blue color scheme\n" +
            "  • Elegant dark design\n" +
            "  • Smooth gradients\n" +
            "  • Professional appearance\n\n" +
            "Click 'Toggle Theme' to switch between themes!\n" +
            "Notice how all elements change color smoothly.\n"
        );
        
        // Поле ввода
        inputField = new TextField();
        inputField.setPromptText("Type something to test input styling...");
        inputField.setPrefWidth(400);
        
        // Кнопки
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        
        themeButton = new Button("🌙 Switch to Midnight Theme");
        themeButton.setPrefWidth(200);
        themeButton.setPrefHeight(40);
        
        testButton = new Button("🧪 Test Button");
        testButton.setPrefWidth(150);
        testButton.setPrefHeight(40);
        
        buttonBox.getChildren().addAll(themeButton, testButton);
        
        // Информационная панель
        VBox infoPanel = new VBox(10);
        infoPanel.setAlignment(Pos.CENTER);
        infoPanel.setPadding(new Insets(20));
        
        Label infoTitle = new Label("📋 Current Theme Information");
        infoTitle.setStyle("-fx-font-size: 14px; -fx-font-weight: bold;");
        
        Label currentThemeLabel = new Label("Current: " + themeManager.getCurrentTheme().getDisplayName());
        Label descriptionLabel = new Label(themeManager.getCurrentTheme().getDescription());
        
        infoPanel.getChildren().addAll(infoTitle, currentThemeLabel, descriptionLabel);
        
        root.getChildren().addAll(
            titleLabel,
            subtitleLabel, 
            statusLabel,
            terminalArea,
            inputField,
            buttonBox,
            infoPanel
        );
    }
    
    private void setupEventHandlers() {
        themeButton.setOnAction(e -> {
            themeManager.toggleTheme();
            updateThemeButtonText();
            applyCurrentTheme();
            updateTerminalText();
        });
        
        testButton.setOnAction(e -> {
            terminalArea.appendText("\n🧪 Test button clicked! Theme: " + themeManager.getCurrentTheme().getName());
        });
    }
    
    private void updateThemeButtonText() {
        ThemeManager.Theme currentTheme = themeManager.getCurrentTheme();
        if (currentTheme == ThemeManager.Theme.MATRIX) {
            themeButton.setText("🌙 Switch to Midnight Theme");
        } else {
            themeButton.setText("🟢 Switch to Matrix Theme");
        }
    }
    
    private void applyCurrentTheme() {
        // Применяем тему ко всем элементам
        themeManager.applyThemeToNode(root, "root", true);
        themeManager.applyThemeToNode(titleLabel, "title", true);
        themeManager.applyThemeToNode(subtitleLabel, "subtitle", true);
        themeManager.applyThemeToNode(statusLabel, "status", true);
        themeManager.applyThemeToNode(terminalArea, "terminal", true);
        themeManager.applyThemeToNode(inputField, "input", true);
        themeManager.applyThemeToNode(themeButton, "button", true);
        themeManager.applyThemeToNode(testButton, "button", true);
        
        // Обновляем информационную панель
        updateInfoPanel();
    }
    
    private void updateInfoPanel() {
        // Находим информационную панель и обновляем её
        VBox infoPanel = (VBox) root.getChildren().get(6);
        themeManager.applyThemeToNode(infoPanel, "panel", true);
        
        // Обновляем лейблы в панели
        for (javafx.scene.Node node : infoPanel.getChildren()) {
            if (node instanceof Label) {
                Label label = (Label) node;
                if (label.getText().contains("Current Theme")) {
                    themeManager.applyThemeToNode(label, "subtitle", true);
                } else if (label.getText().contains("Current:")) {
                    label.setText("Current: " + themeManager.getCurrentTheme().getDisplayName());
                    themeManager.applyThemeToNode(label, "title", true);
                } else if (label.getText().contains("theme") || label.getText().contains("Theme")) {
                    label.setText(themeManager.getCurrentTheme().getDescription());
                    themeManager.applyThemeToNode(label, "subtitle", true);
                }
            }
        }
    }
    
    private void updateTerminalText() {
        String currentThemeName = themeManager.getCurrentTheme().getDisplayName();
        terminalArea.appendText("\n\n🎨 Theme switched to: " + currentThemeName);
        terminalArea.appendText("\n✨ All interface elements updated with new color scheme!");
        
        if (themeManager.getCurrentTheme() == ThemeManager.Theme.MIDNIGHT) {
            terminalArea.appendText("\n🌙 Welcome to the elegant Midnight theme!");
            terminalArea.appendText("\n💜 Enjoy the modern purple-blue aesthetic.");
        } else {
            terminalArea.appendText("\n🟢 Back to the classic Matrix theme!");
            terminalArea.appendText("\n💚 Experience the retro hacker vibes.");
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
