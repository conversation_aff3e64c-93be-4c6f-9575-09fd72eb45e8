package com.warden.osint.api;

import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.time.Duration;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * API Database Finder - поиск API с базами данных
 * Ищет сами источники API, а не пробивает данные
 */
public class APIFinder {
    
    // Поисковые запросы для поиска API
    private static final String[] SEARCH_QUERIES = {
        "phone database API",
        "email database API", 
        "telegram database API",
        "OSINT database API",
        "breach database API",
        "social media database API",
        "crypto database API",
        "people search API",
        "reverse phone lookup API",
        "email verification API",
        "phone number API",
        "contact database API",
        "leaked database API",
        "data breach API",
        "background check API",
        "identity verification API"
    };
    
    // Поисковые движки для поиска API
    private static final String[] SEARCH_ENGINES = {
        "https://www.google.com/search?q=",
        "https://www.bing.com/search?q=",
        "https://duckduckgo.com/?q=",
        "https://search.yahoo.com/search?p=",
        "https://yandex.ru/search/?text="
    };
    
    // GitHub поиск API репозиториев
    private static final String[] GITHUB_SEARCHES = {
        "https://api.github.com/search/repositories?q=phone+database+API",
        "https://api.github.com/search/repositories?q=email+database+API",
        "https://api.github.com/search/repositories?q=OSINT+database",
        "https://api.github.com/search/repositories?q=telegram+database",
        "https://api.github.com/search/repositories?q=breach+database",
        "https://api.github.com/search/repositories?q=people+search+API"
    };
    
    // Специализированные каталоги API
    private static final String[] API_CATALOGS = {
        "https://rapidapi.com/search/phone",
        "https://rapidapi.com/search/email", 
        "https://rapidapi.com/search/people",
        "https://rapidapi.com/search/social",
        "https://programmableweb.com/apis/directory",
        "https://apilist.fun/category/data",
        "https://public-apis.io/category/data",
        "https://github.com/public-apis/public-apis"
    };
    
    // Telegram каналы с API
    private static final String[] TELEGRAM_CHANNELS = {
        "https://t.me/s/api_databases",
        "https://t.me/s/osint_apis", 
        "https://t.me/s/leaked_apis",
        "https://t.me/s/phone_apis",
        "https://t.me/s/email_apis",
        "https://t.me/s/database_apis"
    };
    
    private final HttpClient httpClient;
    private final ExecutorService executor;
    
    public APIFinder() {
        this.httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .followRedirects(HttpClient.Redirect.NORMAL)
            .build();
        this.executor = Executors.newFixedThreadPool(30);
    }
    
    /**
     * ОСНОВНОЙ МЕТОД - Поиск API с базами данных
     */
    public CompletableFuture<APIFinderResult> findAPIsWithDatabases() {
        return CompletableFuture.supplyAsync(() -> {
            APIFinderResult result = new APIFinderResult();
            result.setStartTime(System.currentTimeMillis());
            
            List<CompletableFuture<List<APISource>>> futures = new ArrayList<>();
            
            // 1. Поиск через поисковые системы
            futures.add(searchViaSearchEngines());
            
            // 2. Поиск в GitHub репозиториях
            futures.add(searchViaGitHub());
            
            // 3. Поиск в каталогах API
            futures.add(searchViaAPICatalogs());
            
            // 4. Поиск в Telegram каналах
            futures.add(searchViaTelegramChannels());
            
            // 5. Поиск популярных OSINT API
            futures.add(findPopularOSINTAPIs());
            
            // Собираем все результаты
            List<APISource> allSources = new ArrayList<>();
            for (CompletableFuture<List<APISource>> future : futures) {
                try {
                    List<APISource> sources = future.get();
                    allSources.addAll(sources);
                } catch (Exception e) {
                    // Игнорируем ошибки отдельных источников
                }
            }
            
            result.setApiSources(allSources);
            result.setEndTime(System.currentTimeMillis());
            result.analyzeResults();
            
            return result;
        }, executor);
    }
    
    /**
     * Поиск через поисковые системы
     */
    private CompletableFuture<List<APISource>> searchViaSearchEngines() {
        return CompletableFuture.supplyAsync(() -> {
            List<APISource> sources = new ArrayList<>();
            
            for (String query : SEARCH_QUERIES) {
                for (String searchEngine : SEARCH_ENGINES) {
                    try {
                        String searchUrl = searchEngine + query.replace(" ", "+");
                        
                        HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(searchUrl))
                            .timeout(Duration.ofSeconds(15))
                            .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                            .GET()
                            .build();
                        
                        HttpResponse<String> response = httpClient.send(request, 
                            HttpResponse.BodyHandlers.ofString());
                        
                        if (response.statusCode() == 200) {
                            List<APISource> foundSources = extractAPIsFromSearchResults(response.body(), query);
                            sources.addAll(foundSources);
                        }
                        
                        Thread.sleep(1000); // Задержка между запросами
                        
                    } catch (Exception e) {
                        // Продолжаем поиск
                    }
                }
            }
            
            return sources;
        }, executor);
    }
    
    /**
     * Поиск в GitHub репозиториях
     */
    private CompletableFuture<List<APISource>> searchViaGitHub() {
        return CompletableFuture.supplyAsync(() -> {
            List<APISource> sources = new ArrayList<>();
            
            for (String githubUrl : GITHUB_SEARCHES) {
                try {
                    HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(githubUrl))
                        .timeout(Duration.ofSeconds(10))
                        .header("Accept", "application/vnd.github.v3+json")
                        .header("User-Agent", "WARDEN-API-Finder")
                        .GET()
                        .build();
                    
                    HttpResponse<String> response = httpClient.send(request, 
                        HttpResponse.BodyHandlers.ofString());
                    
                    if (response.statusCode() == 200) {
                        List<APISource> githubSources = extractAPIsFromGitHub(response.body());
                        sources.addAll(githubSources);
                    }
                    
                } catch (Exception e) {
                    // Продолжаем поиск
                }
            }
            
            return sources;
        }, executor);
    }
    
    /**
     * Поиск в каталогах API
     */
    private CompletableFuture<List<APISource>> searchViaAPICatalogs() {
        return CompletableFuture.supplyAsync(() -> {
            List<APISource> sources = new ArrayList<>();
            
            for (String catalogUrl : API_CATALOGS) {
                try {
                    HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(catalogUrl))
                        .timeout(Duration.ofSeconds(15))
                        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                        .GET()
                        .build();
                    
                    HttpResponse<String> response = httpClient.send(request, 
                        HttpResponse.BodyHandlers.ofString());
                    
                    if (response.statusCode() == 200) {
                        List<APISource> catalogSources = extractAPIsFromCatalog(response.body(), catalogUrl);
                        sources.addAll(catalogSources);
                    }
                    
                } catch (Exception e) {
                    // Продолжаем поиск
                }
            }
            
            return sources;
        }, executor);
    }
    
    /**
     * Поиск в Telegram каналах
     */
    private CompletableFuture<List<APISource>> searchViaTelegramChannels() {
        return CompletableFuture.supplyAsync(() -> {
            List<APISource> sources = new ArrayList<>();
            
            for (String channelUrl : TELEGRAM_CHANNELS) {
                try {
                    HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(channelUrl))
                        .timeout(Duration.ofSeconds(10))
                        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                        .GET()
                        .build();
                    
                    HttpResponse<String> response = httpClient.send(request, 
                        HttpResponse.BodyHandlers.ofString());
                    
                    if (response.statusCode() == 200) {
                        List<APISource> telegramSources = extractAPIsFromTelegram(response.body());
                        sources.addAll(telegramSources);
                    }
                    
                } catch (Exception e) {
                    // Продолжаем поиск
                }
            }
            
            return sources;
        }, executor);
    }
    
    /**
     * Поиск популярных OSINT API
     */
    private CompletableFuture<List<APISource>> findPopularOSINTAPIs() {
        return CompletableFuture.supplyAsync(() -> {
            List<APISource> sources = new ArrayList<>();
            
            // Добавляем известные API с оценкой их БД
            sources.add(createKnownAPISource("api.usersbox.net", "UsersBox API", "PHONE", 1500000, 95));
            sources.add(createKnownAPISource("api.truecaller.com", "TrueCaller API", "PHONE", 850000, 90));
            sources.add(createKnownAPISource("api.numverify.com", "NumVerify API", "PHONE", 500000, 85));
            sources.add(createKnownAPISource("haveibeenpwned.com/api", "HaveIBeenPwned API", "BREACH", 12000000000L, 98));
            sources.add(createKnownAPISource("api.hunter.io", "Hunter.io API", "EMAIL", 200000000, 92));
            sources.add(createKnownAPISource("api.clearbit.com", "Clearbit API", "EMAIL", 150000000, 88));
            sources.add(createKnownAPISource("api.shodan.io", "Shodan API", "IP", 45000000, 95));
            sources.add(createKnownAPISource("api.virustotal.com", "VirusTotal API", "IP", 25000000, 90));
            sources.add(createKnownAPISource("api.blockchain.info", "Blockchain.info API", "CRYPTO", 850000000, 96));
            sources.add(createKnownAPISource("api.pipl.com", "Pipl API", "PEOPLE", 3000000000L, 94));
            
            return sources;
        }, executor);
    }
    
    /**
     * Извлечение API из результатов поиска
     */
    private List<APISource> extractAPIsFromSearchResults(String html, String query) {
        List<APISource> sources = new ArrayList<>();
        
        // Паттерны для поиска API URL
        String[] patterns = {
            "https?://[\\w.-]+/api[\\w/]*",
            "https?://api\\.[\\w.-]+[\\w/]*",
            "https?://[\\w.-]+\\.com/v\\d+[\\w/]*"
        };
        
        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(html);
            
            while (matcher.find() && sources.size() < 20) {
                String apiUrl = matcher.group();
                APISource source = new APISource();
                source.setUrl(apiUrl);
                source.setName(extractNameFromUrl(apiUrl));
                source.setType(detectTypeFromQuery(query));
                source.setSource("SEARCH_ENGINE");
                source.setEstimatedRecords(estimateRecordsFromContext(html, apiUrl));
                sources.add(source);
            }
        }
        
        return sources;
    }
    
    /**
     * Извлечение API из GitHub
     */
    private List<APISource> extractAPIsFromGitHub(String json) {
        List<APISource> sources = new ArrayList<>();
        
        // Простой парсинг JSON для поиска репозиториев с API
        String[] lines = json.split("\n");
        for (String line : lines) {
            if (line.contains("\"html_url\"") && line.contains("github.com")) {
                String url = extractUrlFromJson(line);
                if (url != null) {
                    APISource source = new APISource();
                    source.setUrl(url);
                    source.setName(extractRepoName(url));
                    source.setType("GITHUB_REPO");
                    source.setSource("GITHUB");
                    sources.add(source);
                }
            }
        }
        
        return sources;
    }
    
    /**
     * Извлечение API из каталогов
     */
    private List<APISource> extractAPIsFromCatalog(String html, String catalogUrl) {
        List<APISource> sources = new ArrayList<>();
        
        // Поиск ссылок на API в каталогах
        Pattern pattern = Pattern.compile("https?://[\\w.-]+[\\w/]*api[\\w/]*", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(html);
        
        while (matcher.find() && sources.size() < 50) {
            String apiUrl = matcher.group();
            APISource source = new APISource();
            source.setUrl(apiUrl);
            source.setName(extractNameFromUrl(apiUrl));
            source.setSource("API_CATALOG");
            sources.add(source);
        }
        
        return sources;
    }
    
    /**
     * Извлечение API из Telegram каналов
     */
    private List<APISource> extractAPIsFromTelegram(String html) {
        List<APISource> sources = new ArrayList<>();
        
        // Поиск API ссылок в Telegram каналах
        Pattern pattern = Pattern.compile("https?://[\\w.-]+[\\w/]*");
        Matcher matcher = pattern.matcher(html);
        
        while (matcher.find() && sources.size() < 30) {
            String url = matcher.group();
            if (url.contains("api") || url.contains("database")) {
                APISource source = new APISource();
                source.setUrl(url);
                source.setName(extractNameFromUrl(url));
                source.setSource("TELEGRAM");
                sources.add(source);
            }
        }
        
        return sources;
    }
    
    // Вспомогательные методы
    private APISource createKnownAPISource(String url, String name, String type, long records, int quality) {
        APISource source = new APISource();
        source.setUrl(url);
        source.setName(name);
        source.setType(type);
        source.setEstimatedRecords(records);
        source.setQualityScore(quality);
        source.setSource("KNOWN_API");
        source.setVerified(true);
        return source;
    }
    
    private String extractNameFromUrl(String url) {
        try {
            String domain = URI.create(url).getHost();
            return domain != null ? domain : url;
        } catch (Exception e) {
            return url;
        }
    }
    
    private String detectTypeFromQuery(String query) {
        if (query.contains("phone")) return "PHONE";
        if (query.contains("email")) return "EMAIL";
        if (query.contains("telegram")) return "TELEGRAM";
        if (query.contains("breach")) return "BREACH";
        if (query.contains("social")) return "SOCIAL";
        if (query.contains("crypto")) return "CRYPTO";
        return "UNKNOWN";
    }
    
    private long estimateRecordsFromContext(String html, String apiUrl) {
        // Простая оценка количества записей на основе контекста
        if (html.contains("million")) return 1000000;
        if (html.contains("billion")) return 1000000000;
        if (html.contains("thousand")) return 1000;
        return 0;
    }
    
    private String extractUrlFromJson(String line) {
        int start = line.indexOf("\"") + 1;
        int end = line.lastIndexOf("\"");
        if (start > 0 && end > start) {
            return line.substring(start, end);
        }
        return null;
    }
    
    private String extractRepoName(String url) {
        String[] parts = url.split("/");
        return parts.length > 0 ? parts[parts.length - 1] : url;
    }
    
    public void shutdown() {
        executor.shutdown();
    }
}
