package com.warden.osint.api;

import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.time.Duration;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * МОЩНЫЙ API Scanner для поиска баз данных
 * Быстрый поиск по множеству источников с показом количества записей
 */
public class APIScanner {
    
    private static final String[] API_ENDPOINTS = {
        // Telegram боты и каналы
        "https://api.telegram.org/bot{TOKEN}/getUpdates",
        "https://t.me/getcontact_real_bot",
        "https://t.me/phone_avito_bot",
        "https://t.me/ShowContactBot",
        "https://t.me/leaked_databases",
        "https://t.me/phone_search_db",
        "https://t.me/osint_databases",
        
        // OSINT API сервисы
        "https://api.usersbox.net/v1/search",
        "https://api.truecaller.com/v1/search",
        "https://api.numverify.com/v1/validate",
        "https://api.phonevalidator.com/api/v1/phonenumber",
        "https://api.veriphone.io/v2/verify",
        
        // Социальные сети API
        "https://api.vk.com/method/users.search",
        "https://graph.facebook.com/v18.0/search",
        "https://api.instagram.com/v1/users/search",
        "https://api.twitter.com/2/users/by/username",
        
        // Email и домены
        "https://api.hunter.io/v2/email-finder",
        "https://api.clearbit.com/v2/enrichment/find",
        "https://api.emailrep.io/v1",
        "https://haveibeenpwned.com/api/v3/breachedaccount",
        
        // IP и сети
        "https://api.shodan.io/shodan/host",
        "https://api.virustotal.com/vtapi/v2/ip-address/report",
        "https://api.abuseipdb.com/api/v2/check",
        "https://api.greynoise.io/v3/community",
        
        // Криптовалюты
        "https://api.blockchain.info/rawaddr",
        "https://api.blockcypher.com/v1/btc/main/addrs",
        "https://api.etherscan.io/api",
        
        // Поисковые системы
        "https://www.googleapis.com/customsearch/v1",
        "https://api.bing.microsoft.com/v7.0/search",
        "https://api.duckduckgo.com",
        
        // Специализированные OSINT
        "https://api.pipl.com/search",
        "https://api.whitepages.com/phone",
        "https://api.spokeo.com/v1/phone",
        "https://api.intelius.com/v1/person"
    };
    
    private static final String[] DATABASE_KEYWORDS = {
        "records", "entries", "total", "count", "size", "items", 
        "results", "hits", "matches", "found", "database",
        "записей", "найдено", "результатов", "база", "данных"
    };
    
    private final HttpClient httpClient;
    private final ExecutorService executor;
    
    public APIScanner() {
        this.httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(5))
            .build();
        this.executor = Executors.newFixedThreadPool(20); // 20 потоков для быстрого поиска
    }
    
    /**
     * ОСНОВНОЙ МЕТОД - Сканирование всех API
     */
    public CompletableFuture<APIScanResult> scanAllAPIs(String query) {
        return CompletableFuture.supplyAsync(() -> {
            APIScanResult result = new APIScanResult();
            result.setQuery(query);
            result.setStartTime(System.currentTimeMillis());
            
            List<CompletableFuture<APIEndpointResult>> futures = new ArrayList<>();
            
            // Запускаем параллельное сканирование всех API
            for (String endpoint : API_ENDPOINTS) {
                CompletableFuture<APIEndpointResult> future = scanSingleAPI(endpoint, query);
                futures.add(future);
            }
            
            // Ждем завершения всех запросов
            List<APIEndpointResult> endpointResults = new ArrayList<>();
            for (CompletableFuture<APIEndpointResult> future : futures) {
                try {
                    APIEndpointResult endpointResult = future.get();
                    if (endpointResult != null) {
                        endpointResults.add(endpointResult);
                    }
                } catch (Exception e) {
                    // Игнорируем ошибки отдельных API
                }
            }
            
            result.setEndpointResults(endpointResults);
            result.setEndTime(System.currentTimeMillis());
            result.calculateStatistics();
            
            return result;
        }, executor);
    }
    
    /**
     * Сканирование одного API endpoint
     */
    private CompletableFuture<APIEndpointResult> scanSingleAPI(String endpoint, String query) {
        return CompletableFuture.supplyAsync(() -> {
            APIEndpointResult result = new APIEndpointResult();
            result.setEndpoint(endpoint);
            result.setStartTime(System.currentTimeMillis());
            
            try {
                // Формируем URL для запроса
                String requestUrl = buildRequestUrl(endpoint, query);
                
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(requestUrl))
                    .timeout(Duration.ofSeconds(10))
                    .header("User-Agent", "WARDEN-OSINT-Scanner/3.0")
                    .GET()
                    .build();
                
                HttpResponse<String> response = httpClient.send(request, 
                    HttpResponse.BodyHandlers.ofString());
                
                result.setStatusCode(response.statusCode());
                result.setResponseBody(response.body());
                result.setSuccess(response.statusCode() == 200);
                
                if (result.isSuccess()) {
                    // Анализируем ответ
                    analyzeResponse(result, response.body());
                }
                
            } catch (Exception e) {
                result.setSuccess(false);
                result.setError(e.getMessage());
            }
            
            result.setEndTime(System.currentTimeMillis());
            return result;
        }, executor);
    }
    
    /**
     * Формирование URL для запроса
     */
    private String buildRequestUrl(String endpoint, String query) {
        // Простая логика формирования URL
        if (endpoint.contains("search")) {
            return endpoint + "?q=" + query;
        } else if (endpoint.contains("phone")) {
            return endpoint + "/" + query;
        } else if (endpoint.contains("email")) {
            return endpoint + "?email=" + query;
        } else {
            return endpoint + "?query=" + query;
        }
    }
    
    /**
     * Анализ ответа API для извлечения информации о базе данных
     */
    private void analyzeResponse(APIEndpointResult result, String responseBody) {
        // Поиск количества записей в ответе
        long recordCount = extractRecordCount(responseBody);
        result.setRecordCount(recordCount);
        
        // Определение типа базы данных
        String dbType = detectDatabaseType(responseBody);
        result.setDatabaseType(dbType);
        
        // Поиск полезной информации
        List<String> extractedData = extractUsefulData(responseBody);
        result.setExtractedData(extractedData);
        
        // Оценка качества API
        int qualityScore = calculateQualityScore(result);
        result.setQualityScore(qualityScore);
    }
    
    /**
     * Извлечение количества записей из ответа
     */
    private long extractRecordCount(String responseBody) {
        // Паттерны для поиска количества записей
        String[] patterns = {
            "\"total\":(\\d+)",
            "\"count\":(\\d+)",
            "\"records\":(\\d+)",
            "\"size\":(\\d+)",
            "\"hits\":(\\d+)",
            "\"results\":(\\d+)",
            "\"entries\":(\\d+)",
            "total:\\s*(\\d+)",
            "найдено:\\s*(\\d+)",
            "записей:\\s*(\\d+)"
        };
        
        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(responseBody);
            if (matcher.find()) {
                try {
                    return Long.parseLong(matcher.group(1));
                } catch (NumberFormatException e) {
                    // Продолжаем поиск
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Определение типа базы данных
     */
    private String detectDatabaseType(String responseBody) {
        String lowerBody = responseBody.toLowerCase();
        
        if (lowerBody.contains("phone") || lowerBody.contains("телефон")) {
            return "PHONE_DATABASE";
        } else if (lowerBody.contains("email") || lowerBody.contains("почта")) {
            return "EMAIL_DATABASE";
        } else if (lowerBody.contains("social") || lowerBody.contains("соцсети")) {
            return "SOCIAL_DATABASE";
        } else if (lowerBody.contains("breach") || lowerBody.contains("утечка")) {
            return "BREACH_DATABASE";
        } else if (lowerBody.contains("telegram")) {
            return "TELEGRAM_DATABASE";
        } else if (lowerBody.contains("crypto") || lowerBody.contains("bitcoin")) {
            return "CRYPTO_DATABASE";
        } else {
            return "UNKNOWN_DATABASE";
        }
    }
    
    /**
     * Извлечение полезных данных из ответа
     */
    private List<String> extractUsefulData(String responseBody) {
        List<String> data = new ArrayList<>();
        
        // Поиск имен, номеров, email и другой полезной информации
        String[] patterns = {
            "\"name\":\\s*\"([^\"]+)\"",
            "\"phone\":\\s*\"([^\"]+)\"",
            "\"email\":\\s*\"([^\"]+)\"",
            "\"username\":\\s*\"([^\"]+)\"",
            "\"address\":\\s*\"([^\"]+)\""
        };
        
        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(responseBody);
            while (matcher.find() && data.size() < 10) { // Ограничиваем до 10 результатов
                data.add(matcher.group(1));
            }
        }
        
        return data;
    }
    
    /**
     * Расчет оценки качества API
     */
    private int calculateQualityScore(APIEndpointResult result) {
        int score = 0;
        
        // Базовые баллы за успешный ответ
        if (result.isSuccess()) {
            score += 30;
        }
        
        // Баллы за количество записей
        if (result.getRecordCount() > 0) {
            score += 20;
            if (result.getRecordCount() > 1000) score += 10;
            if (result.getRecordCount() > 10000) score += 10;
            if (result.getRecordCount() > 100000) score += 10;
        }
        
        // Баллы за извлеченные данные
        if (result.getExtractedData() != null && !result.getExtractedData().isEmpty()) {
            score += result.getExtractedData().size() * 2;
        }
        
        // Баллы за скорость ответа
        long responseTime = result.getEndTime() - result.getStartTime();
        if (responseTime < 1000) score += 10;
        else if (responseTime < 3000) score += 5;
        
        // Баллы за тип базы данных
        if (!"UNKNOWN_DATABASE".equals(result.getDatabaseType())) {
            score += 10;
        }
        
        return Math.min(score, 100); // Максимум 100 баллов
    }
    
    /**
     * Быстрое сканирование только доступности API
     */
    public CompletableFuture<List<String>> quickScanAvailableAPIs() {
        return CompletableFuture.supplyAsync(() -> {
            List<String> availableAPIs = new ArrayList<>();
            List<CompletableFuture<Boolean>> futures = new ArrayList<>();
            
            for (String endpoint : API_ENDPOINTS) {
                CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(endpoint))
                            .timeout(Duration.ofSeconds(3))
                            .method("HEAD", HttpRequest.BodyPublishers.noBody())
                            .build();
                        
                        HttpResponse<Void> response = httpClient.send(request, 
                            HttpResponse.BodyHandlers.discarding());
                        
                        return response.statusCode() < 500;
                    } catch (Exception e) {
                        return false;
                    }
                }, executor);
                
                futures.add(future);
            }
            
            // Проверяем результаты
            for (int i = 0; i < futures.size(); i++) {
                try {
                    if (futures.get(i).get()) {
                        availableAPIs.add(API_ENDPOINTS[i]);
                    }
                } catch (Exception e) {
                    // Игнорируем ошибки
                }
            }
            
            return availableAPIs;
        }, executor);
    }
    
    /**
     * Закрытие ресурсов
     */
    public void shutdown() {
        executor.shutdown();
    }
}
