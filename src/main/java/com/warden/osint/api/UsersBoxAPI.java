package com.warden.osint.api;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.warden.osint.utils.Logger;
import com.warden.osint.utils.Localization;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;

public class UsersBoxAPI {
    
    private static final String API_BASE_URL = "https://api.usersbox.ru";
    private static final String API_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVhdGVkX2F0IjoxNzUwNDQ1MzQzLCJhcHBfaWQiOjE3NTAyMzIxMTl9.Bq9aR-0YKI6NJndoir_1kwD9N9s6z6wH6Kr2kOa-eG8";
    
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final Logger logger;
    
    public UsersBoxAPI() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.objectMapper = new ObjectMapper();
        this.logger = Logger.getInstance();
        
        logger.api("UsersBox API initialized");
    }
    
    /**
     * Поиск информации по номеру телефона
     */
    public CompletableFuture<APIResult> searchPhone(String phoneNumber) {
        logger.api("Searching phone: " + phoneNumber);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Очищаем номер телефона от лишних символов
                String cleanPhone = phoneNumber.replaceAll("[^0-9+]", "");
                
                // Используем правильный API endpoint согласно документации
                String apiUrl = API_BASE_URL + "/v1/search?q=" + cleanPhone;
                logger.api("Making request to: " + apiUrl);

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(apiUrl))
                        .header("Authorization", API_TOKEN)
                        .header("User-Agent", "WARDEN-OSINT-Tool/3.0")
                        .header("Accept", "application/json")
                        .GET()
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

                logger.api("API Response Status: " + response.statusCode());
                logger.api("API Response Body: " + response.body());

                if (response.statusCode() == 200) {
                    JsonNode jsonResponse = objectMapper.readTree(response.body());

                    if ("success".equals(jsonResponse.path("status").asText())) {
                        JsonNode data = jsonResponse.path("data");
                        int totalCount = data.path("count").asInt();
                        JsonNode items = data.path("items");

                        StringBuilder result = new StringBuilder();
                        result.append("╔══════════════════════════════════════════════════════════════════════════════╗\n");
                        result.append("║                           ").append(Localization.get("phone_lookup_results")).append("                           ║\n");
                        result.append("╚══════════════════════════════════════════════════════════════════════════════╝\n\n");

                        result.append("🔍 ").append(Localization.get("phone_number")).append(": ").append(cleanPhone).append("\n");
                        result.append("📊 ").append(Localization.get("total_sources_found")).append(": ").append(totalCount).append("\n");
                        result.append("📊 ").append(Localization.get("search_status")).append(": ").append(Localization.get("success")).append("\n\n");
                        
                        if (totalCount == 0) {
                            result.append(Localization.get("no_data_found")).append("\n");
                            result.append(Localization.get("not_in_databases")).append("\n");
                        } else {
                            // Обрабатываем каждый источник данных
                            for (JsonNode item : items) {
                                JsonNode source = item.path("source");
                                JsonNode hits = item.path("hits");

                                String database = source.path("database").asText();
                                String collection = source.path("collection").asText();
                                int hitsCount = hits.path("hits_count").asInt();
                                JsonNode documents = hits.path("items");

                                result.append(Localization.get("source")).append(": ").append(database).append("/").append(collection).append("\n");
                                result.append(Localization.get("records_found")).append(": ").append(hitsCount).append("\n");

                                if (documents.isArray() && documents.size() > 0) {
                                    result.append(Localization.get("full_data_found")).append("\n");

                                    // Показываем ВСЕ записи
                                    for (int i = 0; i < documents.size(); i++) {
                                        JsonNode doc = documents.get(i);
                                        result.append("  ╔═══════════════════════════════════════════════════════════════════════════════╗\n");
                                        result.append("  ║                              📋 ").append(Localization.get("record")).append(" #").append(i + 1).append("                                ║\n");
                                        result.append("  ╚═══════════════════════════════════════════════════════════════════════════════╝\n");

                                        // ПЕРСОНАЛЬНЫЕ ДАННЫЕ
                                        if (doc.has("surname") || doc.has("name") || doc.has("middle_name")) {
                                            result.append("  ").append(Localization.get("personal_info")).append("\n");
                                            if (doc.has("surname")) {
                                                result.append("     • ").append(Localization.get("surname")).append(": ").append(doc.path("surname").asText()).append("\n");
                                            }
                                            if (doc.has("name")) {
                                                result.append("     • ").append(Localization.get("name")).append(": ").append(doc.path("name").asText()).append("\n");
                                            }
                                            if (doc.has("middle_name")) {
                                                result.append("     • ").append(Localization.get("middle_name")).append(": ").append(doc.path("middle_name").asText()).append("\n");
                                            }
                                            if (doc.has("full_name")) {
                                                result.append("     • ").append(Localization.get("full_name")).append(": ").append(doc.path("full_name").asText()).append("\n");
                                            }
                                            result.append("\n");
                                        }

                                        // ДАТА РОЖДЕНИЯ И ЛИЧНЫЕ ДАННЫЕ
                                        if (doc.has("birth_date") || doc.has("gender") || doc.has("nationality")) {
                                            result.append("  ").append(Localization.get("birth_personal")).append("\n");
                                            if (doc.has("birth_date")) {
                                                result.append("     • ").append(Localization.get("birth_date")).append(": ").append(doc.path("birth_date").asText()).append("\n");
                                            }
                                            if (doc.has("gender")) {
                                                result.append("     • ").append(Localization.get("gender")).append(": ").append(doc.path("gender").asText()).append("\n");
                                            }
                                            if (doc.has("nationality")) {
                                                result.append("     • ").append(Localization.get("nationality")).append(": ").append(doc.path("nationality").asText()).append("\n");
                                            }
                                            result.append("\n");
                                        }

                                        // ДОКУМЕНТЫ И ID
                                        if (doc.has("inn") || doc.has("ecb_id") || doc.has("passport")) {
                                            result.append("  ").append(Localization.get("documents_ids")).append("\n");
                                            if (doc.has("inn")) {
                                                result.append("     • ").append(Localization.get("inn")).append(": ").append(doc.path("inn").asText()).append("\n");
                                            }
                                            if (doc.has("ecb_id")) {
                                                result.append("     • ").append(Localization.get("ecb_id")).append(": ").append(doc.path("ecb_id").asText()).append("\n");
                                            }
                                            if (doc.has("passport")) {
                                                result.append("     • ").append(Localization.get("passport")).append(": ").append(doc.path("passport").asText()).append("\n");
                                            }
                                            result.append("\n");
                                        }

                                        // КОНТАКТНАЯ ИНФОРМАЦИЯ
                                        if (doc.has("phones") || doc.has("login") || doc.has("email")) {
                                            result.append("  ").append(Localization.get("contact_info")).append("\n");
                                            if (doc.has("login")) {
                                                result.append("     • ").append(Localization.get("login")).append(": ").append(doc.path("login").asText()).append("\n");
                                            }
                                            if (doc.has("phones") && doc.path("phones").isArray()) {
                                                result.append("     • ").append(Localization.get("phone_numbers")).append(":\n");
                                                for (JsonNode phone : doc.path("phones")) {
                                                    result.append("       - ").append(phone.asText()).append("\n");
                                                }
                                            }
                                            if (doc.has("email")) {
                                                result.append("     • ").append(Localization.get("email")).append(": ").append(doc.path("email").asText()).append("\n");
                                            }
                                            result.append("\n");
                                        }

                                        // АДРЕС И МЕСТОПОЛОЖЕНИЕ
                                        if (doc.has("address") || doc.has("city") || doc.has("region")) {
                                            result.append("  ").append(Localization.get("address_info")).append("\n");
                                            if (doc.has("address")) {
                                                result.append("     • ").append(Localization.get("address")).append(": ").append(doc.path("address").asText()).append("\n");
                                            }
                                            if (doc.has("city")) {
                                                result.append("     • ").append(Localization.get("city")).append(": ").append(doc.path("city").asText()).append("\n");
                                            }
                                            if (doc.has("region")) {
                                                result.append("     • ").append(Localization.get("region")).append(": ").append(doc.path("region").asText()).append("\n");
                                            }
                                            result.append("\n");
                                        }

                                        // Показываем остальные важные поля (без технических данных)
                                        boolean hasOtherFields = false;
                                        StringBuilder otherFields = new StringBuilder();

                                        doc.fieldNames().forEachRemaining(fieldName -> {
                                            if (!fieldName.equals("surname") && !fieldName.equals("name") &&
                                                !fieldName.equals("middle_name") && !fieldName.equals("birth_date") &&
                                                !fieldName.equals("gender") && !fieldName.equals("nationality") &&
                                                !fieldName.equals("inn") && !fieldName.equals("ecb_id") &&
                                                !fieldName.equals("login") && !fieldName.equals("phones") &&
                                                !fieldName.equals("email") && !fieldName.equals("address") &&
                                                !fieldName.equals("city") && !fieldName.equals("region") &&
                                                !fieldName.equals("_score") && !fieldName.equals("_id") &&
                                                !fieldName.equals("passport")) {

                                                JsonNode value = doc.path(fieldName);
                                                if (!value.isNull() && !value.asText().isEmpty()) {
                                                    otherFields.append("     • ").append(fieldName).append(": ").append(value.asText()).append("\n");
                                                }
                                            }
                                        });

                                        if (otherFields.length() > 0) {
                                            result.append("  ").append(Localization.get("other_info")).append("\n");
                                            result.append(otherFields.toString());
                                            result.append("\n");
                                        }

                                        result.append("  ═══════════════════════════════════════════════════════════════════════════════\n\n");
                                    }
                                }
                                result.append("\n");
                            }
                        }
                        
                        result.append("\n").append("═".repeat(80)).append("\n");
                        result.append(Localization.get("search_completed")).append("\n");
                        
                        logger.api("Phone search successful for: " + cleanPhone);
                        return new APIResult(true, result.toString());
                        
                    } else {
                        // API вернул ошибку
                        JsonNode error = jsonResponse.path("error");
                        String errorMessage = error.path("message").asText("Unknown error");
                        int errorCode = error.path("code").asInt(0);

                        logger.api("Phone search failed: " + errorMessage + " (code: " + errorCode + ")");
                        return new APIResult(false, "❌ Search failed: " + errorMessage + " (Error code: " + errorCode + ")");
                    }
                } else {
                    String errorMessage = "❌ API request failed. Status: " + response.statusCode();

                    // Обрабатываем разные типы ошибок
                    switch (response.statusCode()) {
                        case 401:
                            errorMessage = "❌ Authentication failed. Invalid API token.";
                            break;
                        case 403:
                            errorMessage = "❌ Access forbidden. Check API permissions.";
                            break;
                        case 404:
                            errorMessage = "❌ API endpoint not found. The service may be unavailable.";
                            break;
                        case 429:
                            errorMessage = "❌ Rate limit exceeded. Please try again later.";
                            break;
                        case 500:
                            errorMessage = "❌ Server error. The API service is experiencing issues.";
                            break;
                    }

                    logger.error("API request failed: " + errorMessage);
                    logger.error("Response body: " + response.body());

                    return new APIResult(false, errorMessage + "\n\nResponse: " + response.body());
                }
                
            } catch (Exception e) {
                logger.error("Phone search error", e);
                return new APIResult(false, "❌ Search error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Получение информации о доступных методах API
     */
    public CompletableFuture<APIResult> getAPIInfo() {
        logger.api("Getting API information");

        return CompletableFuture.supplyAsync(() -> {
            try {
                // Проверяем getMe endpoint для получения информации о приложении
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(API_BASE_URL + "/v1/getMe"))
                        .header("Authorization", API_TOKEN)
                        .header("User-Agent", "WARDEN-OSINT-Tool/3.0")
                        .header("Accept", "application/json")
                        .GET()
                        .build();

                logger.api("Testing API connection to: " + API_BASE_URL);
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

                logger.api("API Info Response Status: " + response.statusCode());
                logger.api("API Info Response Body: " + response.body());
                
                if (response.statusCode() == 200) {
                    JsonNode jsonResponse = objectMapper.readTree(response.body());

                    StringBuilder result = new StringBuilder();
                    result.append("╔══════════════════════════════════════════════════════════════════════════════╗\n");
                    result.append("║                            🔧 API INFORMATION                               ║\n");
                    result.append("╚══════════════════════════════════════════════════════════════════════════════╝\n\n");

                    if ("success".equals(jsonResponse.path("status").asText())) {
                        JsonNode data = jsonResponse.path("data");

                        result.append("🌐 API Status: ONLINE\n");
                        result.append("🔗 Base URL: ").append(API_BASE_URL).append("\n");
                        result.append("📊 Service: Intelligence Platform\n\n");

                        result.append("📱 Application Info:\n");
                        result.append("  • Title: ").append(data.path("title").asText("Unknown")).append("\n");
                        result.append("  • Created: ").append(data.path("created_at").asText("Unknown")).append("\n");
                        result.append("  • Status: ").append(data.path("is_active").asBoolean() ? "Active" : "Inactive").append("\n");
                        result.append("  • Balance: ").append(String.format("%.2f ₽", data.path("balance").asDouble())).append("\n");
                    } else {
                        result.append("🌐 API Status: ERROR\n");
                        result.append("❌ Failed to get application info\n");
                    }
                    
                    result.append("\n🛠️ Available Methods:\n");
                    result.append("  • Phone Lookup - Search information by phone number\n");
                    result.append("  • Email Lookup - Search information by email address\n");
                    result.append("  • Username Lookup - Search across social platforms\n");
                    result.append("  • IP Lookup - Geolocation and ISP information\n");
                    
                    result.append("\n").append("─".repeat(80)).append("\n");
                    result.append("⚠️  All searches are logged and monitored for security purposes.\n");
                    
                    logger.api("API info retrieved successfully");
                    return new APIResult(true, result.toString());
                    
                } else {
                    logger.error("API info request failed with status: " + response.statusCode());
                    return new APIResult(false, "❌ Failed to get API information. Status: " + response.statusCode());
                }
                
            } catch (Exception e) {
                logger.error("API info error", e);
                return new APIResult(false, "❌ API info error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Результат API запроса
     */
    public static class APIResult {
        private final boolean success;
        private final String data;
        
        public APIResult(boolean success, String data) {
            this.success = success;
            this.data = data;
        }
        
        public boolean isSuccess() { return success; }
        public String getData() { return data; }
    }
}
