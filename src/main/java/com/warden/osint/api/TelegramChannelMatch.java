package com.warden.osint.api;

/**
 * Совпадение в канале
 */
public class TelegramChannelMatch {
    private String channelName;
    private String channelId;
    private String matchType;
    private String matchText;
    private String messageId;
    private String timestamp;
    private float confidence;
    
    // Конструкторы
    public TelegramChannelMatch() {}
    
    // Геттеры и сеттеры
    public String getChannelName() { return channelName; }
    public void setChannelName(String channelName) { this.channelName = channelName; }
    
    public String getChannelId() { return channelId; }
    public void setChannelId(String channelId) { this.channelId = channelId; }
    
    public String getMatchType() { return matchType; }
    public void setMatchType(String matchType) { this.matchType = matchType; }
    
    public String getMatchText() { return matchText; }
    public void setMatchText(String matchText) { this.matchText = matchText; }
    
    public String getMessageId() { return messageId; }
    public void setMessageId(String messageId) { this.messageId = messageId; }
    
    public String getTimestamp() { return timestamp; }
    public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
    
    public float getConfidence() { return confidence; }
    public void setConfidence(float confidence) { this.confidence = confidence; }
}
