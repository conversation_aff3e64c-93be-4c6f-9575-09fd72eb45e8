package com.warden.osint.api;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Совпадение в канале
 */
public class TelegramChannelMatch {
    @JsonProperty("channel_name") private String channelName;
    @JsonProperty("channel_id") private String channelId;
    @JsonProperty("match_type") private String matchType;
    @JsonProperty("match_text") private String matchText;
    @JsonProperty("message_id") private String messageId;
    @JsonProperty("timestamp") private String timestamp;
    @JsonProperty("confidence") private float confidence;
    
    // Конструкторы
    public TelegramChannelMatch() {}
    
    // Геттеры и сеттеры
    public String getChannelName() { return channelName; }
    public void setChannelName(String channelName) { this.channelName = channelName; }
    
    public String getChannelId() { return channelId; }
    public void setChannelId(String channelId) { this.channelId = channelId; }
    
    public String getMatchType() { return matchType; }
    public void setMatchType(String matchType) { this.matchType = matchType; }
    
    public String getMatchText() { return matchText; }
    public void setMatchText(String matchText) { this.matchText = matchText; }
    
    public String getMessageId() { return messageId; }
    public void setMessageId(String messageId) { this.messageId = messageId; }
    
    public String getTimestamp() { return timestamp; }
    public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
    
    public float getConfidence() { return confidence; }
    public void setConfidence(float confidence) { this.confidence = confidence; }
}
