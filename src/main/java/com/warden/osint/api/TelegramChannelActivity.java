package com.warden.osint.api;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Активность в канале
 */
public class TelegramChannelActivity {
    @JsonProperty("channel_name") private String channelName;
    @JsonProperty("channel_id") private String channelId;
    @JsonProperty("last_message") private String lastMessage;
    @JsonProperty("message_date") private String messageDate;
    @JsonProperty("message_count") private int messageCount;
    
    // Конструкторы
    public TelegramChannelActivity() {}
    
    // Геттеры и сеттеры
    public String getChannelName() { return channelName; }
    public void setChannelName(String channelName) { this.channelName = channelName; }
    
    public String getChannelId() { return channelId; }
    public void setChannelId(String channelId) { this.channelId = channelId; }
    
    public String getLastMessage() { return lastMessage; }
    public void setLastMessage(String lastMessage) { this.lastMessage = lastMessage; }
    
    public String getMessageDate() { return messageDate; }
    public void setMessageDate(String messageDate) { this.messageDate = messageDate; }
    
    public int getMessageCount() { return messageCount; }
    public void setMessageCount(int messageCount) { this.messageCount = messageCount; }
}
