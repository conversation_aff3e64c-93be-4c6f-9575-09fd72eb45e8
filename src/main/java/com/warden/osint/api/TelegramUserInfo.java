package com.warden.osint.api;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Информация о пользователе Telegram
 */
public class TelegramUserInfo {
    @JsonProperty("user_id") private String userId;
    @JsonProperty("username") private String username;
    @JsonProperty("first_name") private String firstName;
    @JsonProperty("last_name") private String lastName;
    @JsonProperty("phone") private String phone;
    @JsonProperty("bio") private String bio;
    @JsonProperty("profile_photo") private String profilePhoto;
    @JsonProperty("last_seen") private String lastSeen;
    @JsonProperty("is_verified") private boolean isVerified;
    @JsonProperty("is_premium") private boolean isPremium;
    @JsonProperty("language_code") private String languageCode;
    
    // Конструкторы
    public TelegramUserInfo() {}
    
    // Геттеры и сеттеры
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }
    
    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getBio() { return bio; }
    public void setBio(String bio) { this.bio = bio; }
    
    public String getProfilePhoto() { return profilePhoto; }
    public void setProfilePhoto(String profilePhoto) { this.profilePhoto = profilePhoto; }
    
    public String getLastSeen() { return lastSeen; }
    public void setLastSeen(String lastSeen) { this.lastSeen = lastSeen; }
    
    public boolean isVerified() { return isVerified; }
    public void setVerified(boolean verified) { isVerified = verified; }
    
    public boolean isPremium() { return isPremium; }
    public void setPremium(boolean premium) { isPremium = premium; }
    
    public String getLanguageCode() { return languageCode; }
    public void setLanguageCode(String languageCode) { this.languageCode = languageCode; }
}
