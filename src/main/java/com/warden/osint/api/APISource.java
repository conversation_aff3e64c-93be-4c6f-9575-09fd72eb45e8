package com.warden.osint.api;

/**
 * Информация об API источнике с базой данных
 */
public class APISource {
    
    private String url;
    private String name;
    private String type;
    private String source;
    private long estimatedRecords;
    private int qualityScore;
    private boolean verified;
    private String description;
    private String documentation;
    private boolean requiresAuth;
    private String pricing;
    private String lastUpdated;
    
    public APISource() {}
    
    /**
     * Получение краткого описания API
     */
    public String getShortDescription() {
        StringBuilder desc = new StringBuilder();
        
        if (verified) {
            desc.append("✅ ");
        } else {
            desc.append("❓ ");
        }
        
        desc.append(name);
        
        if (estimatedRecords > 0) {
            desc.append(" - ").append(formatRecordCount(estimatedRecords));
        }
        
        if (type != null && !"UNKNOWN".equals(type)) {
            desc.append(" (").append(getTypeDescription()).append(")");
        }
        
        if (qualityScore > 0) {
            desc.append(" - ⭐").append(qualityScore).append("/100");
        }
        
        return desc.toString();
    }
    
    /**
     * Форматирование количества записей
     */
    private String formatRecordCount(long count) {
        if (count >= 1000000000) {
            return String.format("%.1fB записей", count / 1000000000.0);
        } else if (count >= 1000000) {
            return String.format("%.1fM записей", count / 1000000.0);
        } else if (count >= 1000) {
            return String.format("%.1fK записей", count / 1000.0);
        } else {
            return count + " записей";
        }
    }
    
    /**
     * Получение описания типа базы данных
     */
    public String getTypeDescription() {
        switch (type) {
            case "PHONE": return "Телефоны";
            case "EMAIL": return "Email";
            case "TELEGRAM": return "Telegram";
            case "BREACH": return "Утечки";
            case "SOCIAL": return "Соцсети";
            case "CRYPTO": return "Криптовалюты";
            case "IP": return "IP/Сети";
            case "PEOPLE": return "Люди";
            case "GITHUB_REPO": return "GitHub репо";
            default: return "Неизвестно";
        }
    }
    
    /**
     * Получение цвета для отображения
     */
    public String getStatusColor() {
        if (verified && qualityScore >= 90) return "GREEN";
        if (verified && qualityScore >= 70) return "BLUE";
        if (qualityScore >= 50) return "YELLOW";
        if (!verified) return "ORANGE";
        return "GRAY";
    }
    
    /**
     * Получение иконки по типу
     */
    public String getTypeIcon() {
        switch (type) {
            case "PHONE": return "📱";
            case "EMAIL": return "📧";
            case "TELEGRAM": return "📱";
            case "BREACH": return "🔒";
            case "SOCIAL": return "👥";
            case "CRYPTO": return "💰";
            case "IP": return "🌍";
            case "PEOPLE": return "👤";
            case "GITHUB_REPO": return "📁";
            default: return "❓";
        }
    }
    
    /**
     * Проверка, является ли API ценным
     */
    public boolean isValuable() {
        return (verified && qualityScore >= 70) || 
               (estimatedRecords >= 100000) ||
               ("BREACH".equals(type) && estimatedRecords > 0);
    }
    
    /**
     * Получение полного описания для детального просмотра
     */
    public String getFullDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("🔗 URL: ").append(url).append("\n");
        desc.append("📝 Название: ").append(name).append("\n");
        desc.append("🎯 Тип: ").append(getTypeDescription()).append("\n");
        desc.append("📊 Записей: ").append(estimatedRecords > 0 ? formatRecordCount(estimatedRecords) : "Неизвестно").append("\n");
        desc.append("⭐ Качество: ").append(qualityScore).append("/100\n");
        desc.append("✅ Проверен: ").append(verified ? "Да" : "Нет").append("\n");
        desc.append("📍 Источник: ").append(getSourceDescription()).append("\n");
        
        if (description != null) {
            desc.append("📄 Описание: ").append(description).append("\n");
        }
        
        if (documentation != null) {
            desc.append("📚 Документация: ").append(documentation).append("\n");
        }
        
        if (pricing != null) {
            desc.append("💰 Цены: ").append(pricing).append("\n");
        }
        
        return desc.toString();
    }
    
    /**
     * Получение описания источника
     */
    private String getSourceDescription() {
        switch (source) {
            case "SEARCH_ENGINE": return "Поисковая система";
            case "GITHUB": return "GitHub";
            case "API_CATALOG": return "Каталог API";
            case "TELEGRAM": return "Telegram канал";
            case "KNOWN_API": return "Известный API";
            default: return source;
        }
    }
    
    // Геттеры и сеттеры
    public String getUrl() { return url; }
    public void setUrl(String url) { this.url = url; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public long getEstimatedRecords() { return estimatedRecords; }
    public void setEstimatedRecords(long estimatedRecords) { this.estimatedRecords = estimatedRecords; }
    
    public int getQualityScore() { return qualityScore; }
    public void setQualityScore(int qualityScore) { this.qualityScore = qualityScore; }
    
    public boolean isVerified() { return verified; }
    public void setVerified(boolean verified) { this.verified = verified; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getDocumentation() { return documentation; }
    public void setDocumentation(String documentation) { this.documentation = documentation; }
    
    public boolean isRequiresAuth() { return requiresAuth; }
    public void setRequiresAuth(boolean requiresAuth) { this.requiresAuth = requiresAuth; }
    
    public String getPricing() { return pricing; }
    public void setPricing(String pricing) { this.pricing = pricing; }
    
    public String getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(String lastUpdated) { this.lastUpdated = lastUpdated; }
}
