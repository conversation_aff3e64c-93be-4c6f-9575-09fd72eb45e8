package com.warden.osint.api;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * Результат поиска API с базами данных
 */
public class APIFinderResult {
    
    private long startTime;
    private long endTime;
    private List<APISource> apiSources;
    
    // Статистика
    private int totalAPIs;
    private int verifiedAPIs;
    private int unverifiedAPIs;
    private long totalEstimatedRecords;
    private Map<String, Integer> typeStatistics;
    private Map<String, Integer> sourceStatistics;
    
    public APIFinderResult() {
        this.apiSources = new ArrayList<>();
        this.typeStatistics = new HashMap<>();
        this.sourceStatistics = new HashMap<>();
    }
    
    /**
     * Анализ результатов поиска
     */
    public void analyzeResults() {
        totalAPIs = apiSources.size();
        verifiedAPIs = 0;
        unverifiedAPIs = 0;
        totalEstimatedRecords = 0;
        typeStatistics.clear();
        sourceStatistics.clear();
        
        for (APISource source : apiSources) {
            if (source.isVerified()) {
                verifiedAPIs++;
            } else {
                unverifiedAPIs++;
            }
            
            totalEstimatedRecords += source.getEstimatedRecords();
            
            // Статистика по типам
            String type = source.getType() != null ? source.getType() : "UNKNOWN";
            typeStatistics.put(type, typeStatistics.getOrDefault(type, 0) + 1);
            
            // Статистика по источникам
            String sourceType = source.getSource() != null ? source.getSource() : "UNKNOWN";
            sourceStatistics.put(sourceType, sourceStatistics.getOrDefault(sourceType, 0) + 1);
        }
    }
    
    /**
     * Получение топ API по качеству
     */
    public List<APISource> getTopQualityAPIs(int limit) {
        return apiSources.stream()
            .filter(api -> api.getQualityScore() > 0)
            .sorted((a, b) -> Integer.compare(b.getQualityScore(), a.getQualityScore()))
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    /**
     * Получение API с наибольшим количеством записей
     */
    public List<APISource> getLargestDatabaseAPIs(int limit) {
        return apiSources.stream()
            .filter(api -> api.getEstimatedRecords() > 0)
            .sorted((a, b) -> Long.compare(b.getEstimatedRecords(), a.getEstimatedRecords()))
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    /**
     * Получение только проверенных API
     */
    public List<APISource> getVerifiedAPIs() {
        return apiSources.stream()
            .filter(APISource::isVerified)
            .collect(Collectors.toList());
    }
    
    /**
     * Получение API по типу
     */
    public List<APISource> getAPIsByType(String type) {
        return apiSources.stream()
            .filter(api -> type.equals(api.getType()))
            .collect(Collectors.toList());
    }
    
    /**
     * Получение ценных API (высокое качество или много записей)
     */
    public List<APISource> getValuableAPIs() {
        return apiSources.stream()
            .filter(APISource::isValuable)
            .sorted((a, b) -> {
                // Сортировка по качеству, затем по количеству записей
                int qualityCompare = Integer.compare(b.getQualityScore(), a.getQualityScore());
                if (qualityCompare != 0) return qualityCompare;
                return Long.compare(b.getEstimatedRecords(), a.getEstimatedRecords());
            })
            .collect(Collectors.toList());
    }
    
    /**
     * Получение времени выполнения в секундах
     */
    public double getExecutionTimeSeconds() {
        return (endTime - startTime) / 1000.0;
    }
    
    /**
     * Получение краткого отчета
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("🔍 Поиск API завершен за ").append(String.format("%.1f", getExecutionTimeSeconds())).append(" сек\n");
        summary.append("📊 Всего найдено API: ").append(totalAPIs).append("\n");
        summary.append("✅ Проверенных: ").append(verifiedAPIs).append("\n");
        summary.append("❓ Непроверенных: ").append(unverifiedAPIs).append("\n");
        summary.append("📈 Общее количество записей: ").append(String.format("%,d", totalEstimatedRecords)).append("\n");
        summary.append("💎 Ценных API: ").append(getValuableAPIs().size());
        
        return summary.toString();
    }
    
    /**
     * Получение детальной статистики по типам
     */
    public String getTypeStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("📊 СТАТИСТИКА ПО ТИПАМ БД:\n");
        
        for (Map.Entry<String, Integer> entry : typeStatistics.entrySet()) {
            String type = entry.getKey();
            int count = entry.getValue();
            
            // Подсчитываем записи для этого типа
            long recordsForType = apiSources.stream()
                .filter(api -> type.equals(api.getType()))
                .mapToLong(APISource::getEstimatedRecords)
                .sum();
            
            stats.append("  ").append(getTypeIcon(type)).append(" ")
                 .append(getTypeDescription(type)).append(": ")
                 .append(count).append(" API");
            
            if (recordsForType > 0) {
                stats.append(", ").append(formatRecordCount(recordsForType));
            }
            
            stats.append("\n");
        }
        
        return stats.toString();
    }
    
    /**
     * Получение статистики по источникам
     */
    public String getSourceStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("📍 СТАТИСТИКА ПО ИСТОЧНИКАМ:\n");
        
        for (Map.Entry<String, Integer> entry : sourceStatistics.entrySet()) {
            String source = entry.getKey();
            int count = entry.getValue();
            
            stats.append("  📌 ").append(getSourceDescription(source))
                 .append(": ").append(count).append(" API\n");
        }
        
        return stats.toString();
    }
    
    // Вспомогательные методы
    private String getTypeIcon(String type) {
        switch (type) {
            case "PHONE": return "📱";
            case "EMAIL": return "📧";
            case "TELEGRAM": return "📱";
            case "BREACH": return "🔒";
            case "SOCIAL": return "👥";
            case "CRYPTO": return "💰";
            case "IP": return "🌍";
            case "PEOPLE": return "👤";
            case "GITHUB_REPO": return "📁";
            default: return "❓";
        }
    }
    
    private String getTypeDescription(String type) {
        switch (type) {
            case "PHONE": return "Телефоны";
            case "EMAIL": return "Email";
            case "TELEGRAM": return "Telegram";
            case "BREACH": return "Утечки";
            case "SOCIAL": return "Соцсети";
            case "CRYPTO": return "Криптовалюты";
            case "IP": return "IP/Сети";
            case "PEOPLE": return "Люди";
            case "GITHUB_REPO": return "GitHub репо";
            default: return "Неизвестно";
        }
    }
    
    private String getSourceDescription(String source) {
        switch (source) {
            case "SEARCH_ENGINE": return "Поисковые системы";
            case "GITHUB": return "GitHub";
            case "API_CATALOG": return "Каталоги API";
            case "TELEGRAM": return "Telegram каналы";
            case "KNOWN_API": return "Известные API";
            default: return source;
        }
    }
    
    private String formatRecordCount(long count) {
        if (count >= 1000000000) {
            return String.format("%.1fB записей", count / 1000000000.0);
        } else if (count >= 1000000) {
            return String.format("%.1fM записей", count / 1000000.0);
        } else if (count >= 1000) {
            return String.format("%.1fK записей", count / 1000.0);
        } else {
            return count + " записей";
        }
    }
    
    // Геттеры и сеттеры
    public long getStartTime() { return startTime; }
    public void setStartTime(long startTime) { this.startTime = startTime; }
    
    public long getEndTime() { return endTime; }
    public void setEndTime(long endTime) { this.endTime = endTime; }
    
    public List<APISource> getApiSources() { return apiSources; }
    public void setApiSources(List<APISource> apiSources) { this.apiSources = apiSources; }
    
    public int getTotalAPIs() { return totalAPIs; }
    public int getVerifiedAPIsCount() { return verifiedAPIs; }
    public int getUnverifiedAPIs() { return unverifiedAPIs; }
    public long getTotalEstimatedRecords() { return totalEstimatedRecords; }
    public Map<String, Integer> getTypeStats() { return typeStatistics; }
    public Map<String, Integer> getSourceStats() { return sourceStatistics; }
}
