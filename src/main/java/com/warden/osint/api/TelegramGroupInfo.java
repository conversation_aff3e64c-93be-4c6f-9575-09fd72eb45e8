package com.warden.osint.api;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Информация о группе
 */
public class TelegramGroupInfo {
    @JsonProperty("group_id") private String groupId;
    @JsonProperty("group_name") private String groupName;
    @JsonProperty("group_type") private String groupType;
    @JsonProperty("member_count") private int memberCount;
    @JsonProperty("join_date") private String joinDate;
    @JsonProperty("last_activity") private String lastActivity;
    @JsonProperty("role") private String role;
    
    // Конструкторы
    public TelegramGroupInfo() {}
    
    // Геттеры и сеттеры
    public String getGroupId() { return groupId; }
    public void setGroupId(String groupId) { this.groupId = groupId; }
    
    public String getGroupName() { return groupName; }
    public void setGroupName(String groupName) { this.groupName = groupName; }
    
    public String getGroupType() { return groupType; }
    public void setGroupType(String groupType) { this.groupType = groupType; }
    
    public int getMemberCount() { return memberCount; }
    public void setMemberCount(int memberCount) { this.memberCount = memberCount; }
    
    public String getJoinDate() { return joinDate; }
    public void setJoinDate(String joinDate) { this.joinDate = joinDate; }
    
    public String getLastActivity() { return lastActivity; }
    public void setLastActivity(String lastActivity) { this.lastActivity = lastActivity; }
    
    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }
}
