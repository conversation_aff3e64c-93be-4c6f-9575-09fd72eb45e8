package com.warden.osint.api;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Связанный аккаунт
 */
public class TelegramAccount {
    @JsonProperty("user_id") private String userId;
    @JsonProperty("username") private String username;
    @JsonProperty("first_name") private String firstName;
    @JsonProperty("last_name") private String lastName;
    @JsonProperty("connection_type") private String connectionType;
    @JsonProperty("last_activity") private String lastActivity;
    
    // Конструкторы
    public TelegramAccount() {}
    
    // Геттеры и сеттеры
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }
    
    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }
    
    public String getConnectionType() { return connectionType; }
    public void setConnectionType(String connectionType) { this.connectionType = connectionType; }
    
    public String getLastActivity() { return lastActivity; }
    public void setLastActivity(String lastActivity) { this.lastActivity = lastActivity; }
}
