package com.warden.osint.api;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * Результат сканирования всех API
 */
public class APIScanResult {
    
    private String query;
    private long startTime;
    private long endTime;
    private List<APIEndpointResult> endpointResults;
    
    // Статистика
    private int totalAPIs;
    private int successfulAPIs;
    private int failedAPIs;
    private long totalRecords;
    private String bestAPI;
    private int averageQualityScore;
    
    public APIScanResult() {
        this.endpointResults = new ArrayList<>();
    }
    
    /**
     * Расчет статистики по результатам сканирования
     */
    public void calculateStatistics() {
        totalAPIs = endpointResults.size();
        successfulAPIs = 0;
        failedAPIs = 0;
        totalRecords = 0;
        int totalQualityScore = 0;
        int bestScore = 0;
        
        for (APIEndpointResult result : endpointResults) {
            if (result.isSuccess()) {
                successfulAPIs++;
                totalRecords += result.getRecordCount();
            } else {
                failedAPIs++;
            }
            
            totalQualityScore += result.getQualityScore();
            
            if (result.getQualityScore() > bestScore) {
                bestScore = result.getQualityScore();
                bestAPI = result.getEndpoint();
            }
        }
        
        averageQualityScore = totalAPIs > 0 ? totalQualityScore / totalAPIs : 0;
    }
    
    /**
     * Получение топ API по качеству
     */
    public List<APIEndpointResult> getTopAPIs(int limit) {
        return endpointResults.stream()
            .filter(APIEndpointResult::isSuccess)
            .sorted((a, b) -> Integer.compare(b.getQualityScore(), a.getQualityScore()))
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    /**
     * Получение API с наибольшим количеством записей
     */
    public List<APIEndpointResult> getAPIsWithMostRecords(int limit) {
        return endpointResults.stream()
            .filter(result -> result.getRecordCount() > 0)
            .sorted((a, b) -> Long.compare(b.getRecordCount(), a.getRecordCount()))
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    /**
     * Получение самых быстрых API
     */
    public List<APIEndpointResult> getFastestAPIs(int limit) {
        return endpointResults.stream()
            .filter(APIEndpointResult::isSuccess)
            .sorted((a, b) -> Long.compare(
                a.getEndTime() - a.getStartTime(),
                b.getEndTime() - b.getStartTime()
            ))
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    /**
     * Получение времени выполнения в секундах
     */
    public double getExecutionTimeSeconds() {
        return (endTime - startTime) / 1000.0;
    }
    
    /**
     * Получение краткого отчета
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("🔍 Сканирование завершено за ").append(String.format("%.1f", getExecutionTimeSeconds())).append(" сек\n");
        summary.append("📊 Всего API: ").append(totalAPIs).append("\n");
        summary.append("✅ Успешных: ").append(successfulAPIs).append("\n");
        summary.append("❌ Неудачных: ").append(failedAPIs).append("\n");
        summary.append("📈 Всего записей найдено: ").append(String.format("%,d", totalRecords)).append("\n");
        summary.append("🏆 Лучший API: ").append(bestAPI != null ? bestAPI : "Не найден").append("\n");
        summary.append("⭐ Средняя оценка качества: ").append(averageQualityScore).append("/100");
        
        return summary.toString();
    }
    
    // Геттеры и сеттеры
    public String getQuery() { return query; }
    public void setQuery(String query) { this.query = query; }
    
    public long getStartTime() { return startTime; }
    public void setStartTime(long startTime) { this.startTime = startTime; }
    
    public long getEndTime() { return endTime; }
    public void setEndTime(long endTime) { this.endTime = endTime; }
    
    public List<APIEndpointResult> getEndpointResults() { return endpointResults; }
    public void setEndpointResults(List<APIEndpointResult> endpointResults) { this.endpointResults = endpointResults; }
    
    public int getTotalAPIs() { return totalAPIs; }
    public int getSuccessfulAPIs() { return successfulAPIs; }
    public int getFailedAPIs() { return failedAPIs; }
    public long getTotalRecords() { return totalRecords; }
    public String getBestAPI() { return bestAPI; }
    public int getAverageQualityScore() { return averageQualityScore; }
}
