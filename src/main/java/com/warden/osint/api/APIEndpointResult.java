package com.warden.osint.api;

import java.util.List;

/**
 * Результат сканирования одного API endpoint
 */
public class APIEndpointResult {
    
    private String endpoint;
    private boolean success;
    private int statusCode;
    private String error;
    private long startTime;
    private long endTime;
    private String responseBody;
    
    // Анализ данных
    private long recordCount;
    private String databaseType;
    private List<String> extractedData;
    private int qualityScore;
    
    public APIEndpointResult() {}
    
    /**
     * Получение времени ответа в миллисекундах
     */
    public long getResponseTime() {
        return endTime - startTime;
    }
    
    /**
     * Получение времени ответа в секундах
     */
    public double getResponseTimeSeconds() {
        return getResponseTime() / 1000.0;
    }
    
    /**
     * Проверка, содержит ли API полезные данные
     */
    public boolean hasUsefulData() {
        return recordCount > 0 || (extractedData != null && !extractedData.isEmpty());
    }
    
    /**
     * Получение краткого описания результата
     */
    public String getShortDescription() {
        if (!success) {
            return "❌ Недоступен (" + (error != null ? error : "HTTP " + statusCode) + ")";
        }
        
        StringBuilder desc = new StringBuilder();
        desc.append("✅ ");
        
        if (recordCount > 0) {
            desc.append(String.format("%,d записей", recordCount));
        } else {
            desc.append("Доступен");
        }
        
        if (databaseType != null && !"UNKNOWN_DATABASE".equals(databaseType)) {
            desc.append(" (").append(getDatabaseTypeDescription()).append(")");
        }
        
        desc.append(" - ").append(String.format("%.1fs", getResponseTimeSeconds()));
        desc.append(" - ⭐").append(qualityScore).append("/100");
        
        return desc.toString();
    }
    
    /**
     * Получение описания типа базы данных
     */
    public String getDatabaseTypeDescription() {
        switch (databaseType) {
            case "PHONE_DATABASE": return "Телефоны";
            case "EMAIL_DATABASE": return "Email";
            case "SOCIAL_DATABASE": return "Соцсети";
            case "BREACH_DATABASE": return "Утечки";
            case "TELEGRAM_DATABASE": return "Telegram";
            case "CRYPTO_DATABASE": return "Криптовалюты";
            default: return "Неизвестно";
        }
    }
    
    /**
     * Получение оценки качества в виде звезд
     */
    public String getQualityStars() {
        int stars = qualityScore / 20; // 0-100 -> 0-5 звезд
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            if (i < stars) {
                sb.append("⭐");
            } else {
                sb.append("☆");
            }
        }
        return sb.toString();
    }
    
    /**
     * Получение цвета для отображения в UI
     */
    public String getStatusColor() {
        if (!success) return "RED";
        if (qualityScore >= 80) return "GREEN";
        if (qualityScore >= 60) return "YELLOW";
        if (qualityScore >= 40) return "ORANGE";
        return "GRAY";
    }
    
    // Геттеры и сеттеры
    public String getEndpoint() { return endpoint; }
    public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public int getStatusCode() { return statusCode; }
    public void setStatusCode(int statusCode) { this.statusCode = statusCode; }
    
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    
    public long getStartTime() { return startTime; }
    public void setStartTime(long startTime) { this.startTime = startTime; }
    
    public long getEndTime() { return endTime; }
    public void setEndTime(long endTime) { this.endTime = endTime; }
    
    public String getResponseBody() { return responseBody; }
    public void setResponseBody(String responseBody) { this.responseBody = responseBody; }
    
    public long getRecordCount() { return recordCount; }
    public void setRecordCount(long recordCount) { this.recordCount = recordCount; }
    
    public String getDatabaseType() { return databaseType; }
    public void setDatabaseType(String databaseType) { this.databaseType = databaseType; }
    
    public List<String> getExtractedData() { return extractedData; }
    public void setExtractedData(List<String> extractedData) { this.extractedData = extractedData; }
    
    public int getQualityScore() { return qualityScore; }
    public void setQualityScore(int qualityScore) { this.qualityScore = qualityScore; }
}
