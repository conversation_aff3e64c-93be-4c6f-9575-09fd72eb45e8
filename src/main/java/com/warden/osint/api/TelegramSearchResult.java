package com.warden.osint.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * Результат поиска в Telegram
 */
public class TelegramSearchResult {
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("error")
    private String error;
    
    @JsonProperty("phone")
    private String phone;
    
    @JsonProperty("username")
    private String username;
    
    @JsonProperty("user_id")
    private String userId;
    
    @JsonProperty("user_info")
    private TelegramUserInfo userInfo;
    
    @JsonProperty("channel_matches")
    private List<TelegramChannelMatch> channelMatches;
    
    @JsonProperty("linked_accounts")
    private List<TelegramAccount> linkedAccounts;
    
    @JsonProperty("group_memberships")
    private List<TelegramGroupInfo> groupMemberships;
    
    @JsonProperty("channel_activities")
    private List<TelegramChannelActivity> channelActivities;
    
    @JsonProperty("search_timestamp")
    private String searchTimestamp;
    
    // Конструкторы
    public TelegramSearchResult() {
        this.searchTimestamp = java.time.Instant.now().toString();
    }
    
    // Геттеры и сеттеры
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public TelegramUserInfo getUserInfo() { return userInfo; }
    public void setUserInfo(TelegramUserInfo userInfo) { this.userInfo = userInfo; }
    
    public List<TelegramChannelMatch> getChannelMatches() { return channelMatches; }
    public void setChannelMatches(List<TelegramChannelMatch> channelMatches) { this.channelMatches = channelMatches; }
    
    public List<TelegramAccount> getLinkedAccounts() { return linkedAccounts; }
    public void setLinkedAccounts(List<TelegramAccount> linkedAccounts) { this.linkedAccounts = linkedAccounts; }
    
    public List<TelegramGroupInfo> getGroupMemberships() { return groupMemberships; }
    public void setGroupMemberships(List<TelegramGroupInfo> groupMemberships) { this.groupMemberships = groupMemberships; }
    
    public List<TelegramChannelActivity> getChannelActivities() { return channelActivities; }
    public void setChannelActivities(List<TelegramChannelActivity> channelActivities) { this.channelActivities = channelActivities; }
    
    public String getSearchTimestamp() { return searchTimestamp; }
    public void setSearchTimestamp(String searchTimestamp) { this.searchTimestamp = searchTimestamp; }
    
    /**
     * Проверяет, найдена ли какая-либо информация
     */
    public boolean hasResults() {
        return userInfo != null || 
               (channelMatches != null && !channelMatches.isEmpty()) ||
               (linkedAccounts != null && !linkedAccounts.isEmpty()) ||
               (groupMemberships != null && !groupMemberships.isEmpty());
    }
    
    /**
     * Возвращает краткое описание результатов
     */
    public String getSummary() {
        if (!success) {
            return "Поиск не удался: " + error;
        }
        
        if (!hasResults()) {
            return "Информация не найдена";
        }
        
        StringBuilder summary = new StringBuilder();
        
        if (userInfo != null) {
            summary.append("Пользователь найден");
            if (userInfo.getFirstName() != null) {
                summary.append(": ").append(userInfo.getFirstName());
                if (userInfo.getLastName() != null) {
                    summary.append(" ").append(userInfo.getLastName());
                }
            }
            summary.append("; ");
        }
        
        if (channelMatches != null && !channelMatches.isEmpty()) {
            summary.append("Найдено в ").append(channelMatches.size()).append(" каналах; ");
        }
        
        if (linkedAccounts != null && !linkedAccounts.isEmpty()) {
            summary.append("Связанных аккаунтов: ").append(linkedAccounts.size()).append("; ");
        }
        
        if (groupMemberships != null && !groupMemberships.isEmpty()) {
            summary.append("Участие в группах: ").append(groupMemberships.size());
        }
        
        return summary.toString();
    }
}

/**
 * Информация о пользователе Telegram
 */
class TelegramUserInfo {
    @JsonProperty("user_id") private String userId;
    @JsonProperty("username") private String username;
    @JsonProperty("first_name") private String firstName;
    @JsonProperty("last_name") private String lastName;
    @JsonProperty("phone") private String phone;
    @JsonProperty("bio") private String bio;
    @JsonProperty("profile_photo") private String profilePhoto;
    @JsonProperty("last_seen") private String lastSeen;
    @JsonProperty("is_verified") private boolean isVerified;
    @JsonProperty("is_premium") private boolean isPremium;
    @JsonProperty("language_code") private String languageCode;
    
    // Геттеры и сеттеры
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }
    
    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getBio() { return bio; }
    public void setBio(String bio) { this.bio = bio; }
    
    public String getProfilePhoto() { return profilePhoto; }
    public void setProfilePhoto(String profilePhoto) { this.profilePhoto = profilePhoto; }
    
    public String getLastSeen() { return lastSeen; }
    public void setLastSeen(String lastSeen) { this.lastSeen = lastSeen; }
    
    public boolean isVerified() { return isVerified; }
    public void setVerified(boolean verified) { isVerified = verified; }
    
    public boolean isPremium() { return isPremium; }
    public void setPremium(boolean premium) { isPremium = premium; }
    
    public String getLanguageCode() { return languageCode; }
    public void setLanguageCode(String languageCode) { this.languageCode = languageCode; }
}

/**
 * Совпадение в канале
 */
class TelegramChannelMatch {
    @JsonProperty("channel_name") private String channelName;
    @JsonProperty("channel_id") private String channelId;
    @JsonProperty("match_type") private String matchType;
    @JsonProperty("match_text") private String matchText;
    @JsonProperty("message_id") private String messageId;
    @JsonProperty("timestamp") private String timestamp;
    @JsonProperty("confidence") private float confidence;
    
    // Геттеры и сеттеры
    public String getChannelName() { return channelName; }
    public void setChannelName(String channelName) { this.channelName = channelName; }
    
    public String getChannelId() { return channelId; }
    public void setChannelId(String channelId) { this.channelId = channelId; }
    
    public String getMatchType() { return matchType; }
    public void setMatchType(String matchType) { this.matchType = matchType; }
    
    public String getMatchText() { return matchText; }
    public void setMatchText(String matchText) { this.matchText = matchText; }
    
    public String getMessageId() { return messageId; }
    public void setMessageId(String messageId) { this.messageId = messageId; }
    
    public String getTimestamp() { return timestamp; }
    public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
    
    public float getConfidence() { return confidence; }
    public void setConfidence(float confidence) { this.confidence = confidence; }
}

/**
 * Связанный аккаунт
 */
class TelegramAccount {
    @JsonProperty("user_id") private String userId;
    @JsonProperty("username") private String username;
    @JsonProperty("first_name") private String firstName;
    @JsonProperty("last_name") private String lastName;
    @JsonProperty("connection_type") private String connectionType;
    @JsonProperty("last_activity") private String lastActivity;
    
    // Геттеры и сеттеры
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }
    
    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }
    
    public String getConnectionType() { return connectionType; }
    public void setConnectionType(String connectionType) { this.connectionType = connectionType; }
    
    public String getLastActivity() { return lastActivity; }
    public void setLastActivity(String lastActivity) { this.lastActivity = lastActivity; }
}

/**
 * Информация о группе
 */
class TelegramGroupInfo {
    @JsonProperty("group_id") private String groupId;
    @JsonProperty("group_name") private String groupName;
    @JsonProperty("group_type") private String groupType;
    @JsonProperty("member_count") private int memberCount;
    @JsonProperty("join_date") private String joinDate;
    @JsonProperty("last_activity") private String lastActivity;
    @JsonProperty("role") private String role;
    
    // Геттеры и сеттеры
    public String getGroupId() { return groupId; }
    public void setGroupId(String groupId) { this.groupId = groupId; }
    
    public String getGroupName() { return groupName; }
    public void setGroupName(String groupName) { this.groupName = groupName; }
    
    public String getGroupType() { return groupType; }
    public void setGroupType(String groupType) { this.groupType = groupType; }
    
    public int getMemberCount() { return memberCount; }
    public void setMemberCount(int memberCount) { this.memberCount = memberCount; }
    
    public String getJoinDate() { return joinDate; }
    public void setJoinDate(String joinDate) { this.joinDate = joinDate; }
    
    public String getLastActivity() { return lastActivity; }
    public void setLastActivity(String lastActivity) { this.lastActivity = lastActivity; }
    
    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }
}

/**
 * Активность в канале
 */
class TelegramChannelActivity {
    @JsonProperty("channel_name") private String channelName;
    @JsonProperty("channel_id") private String channelId;
    @JsonProperty("last_message") private String lastMessage;
    @JsonProperty("message_date") private String messageDate;
    @JsonProperty("message_count") private int messageCount;
    
    // Геттеры и сеттеры
    public String getChannelName() { return channelName; }
    public void setChannelName(String channelName) { this.channelName = channelName; }
    
    public String getChannelId() { return channelId; }
    public void setChannelId(String channelId) { this.channelId = channelId; }
    
    public String getLastMessage() { return lastMessage; }
    public void setLastMessage(String lastMessage) { this.lastMessage = lastMessage; }
    
    public String getMessageDate() { return messageDate; }
    public void setMessageDate(String messageDate) { this.messageDate = messageDate; }
    
    public int getMessageCount() { return messageCount; }
    public void setMessageCount(int messageCount) { this.messageCount = messageCount; }
}
