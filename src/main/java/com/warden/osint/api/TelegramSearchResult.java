package com.warden.osint.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * Результат поиска в Telegram
 */
public class TelegramSearchResult {
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("error")
    private String error;
    
    @JsonProperty("phone")
    private String phone;
    
    @JsonProperty("username")
    private String username;
    
    @JsonProperty("user_id")
    private String userId;
    
    @JsonProperty("user_info")
    private TelegramUserInfo userInfo;
    
    @JsonProperty("channel_matches")
    private List<TelegramChannelMatch> channelMatches;
    
    @JsonProperty("linked_accounts")
    private List<TelegramAccount> linkedAccounts;
    
    @JsonProperty("group_memberships")
    private List<TelegramGroupInfo> groupMemberships;
    
    @JsonProperty("channel_activities")
    private List<TelegramChannelActivity> channelActivities;
    
    @JsonProperty("search_timestamp")
    private String searchTimestamp;
    
    // Конструкторы
    public TelegramSearchResult() {
        this.searchTimestamp = java.time.Instant.now().toString();
    }
    
    // Геттеры и сеттеры
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public TelegramUserInfo getUserInfo() { return userInfo; }
    public void setUserInfo(TelegramUserInfo userInfo) { this.userInfo = userInfo; }
    
    public List<TelegramChannelMatch> getChannelMatches() { return channelMatches; }
    public void setChannelMatches(List<TelegramChannelMatch> channelMatches) { this.channelMatches = channelMatches; }
    
    public List<TelegramAccount> getLinkedAccounts() { return linkedAccounts; }
    public void setLinkedAccounts(List<TelegramAccount> linkedAccounts) { this.linkedAccounts = linkedAccounts; }
    
    public List<TelegramGroupInfo> getGroupMemberships() { return groupMemberships; }
    public void setGroupMemberships(List<TelegramGroupInfo> groupMemberships) { this.groupMemberships = groupMemberships; }
    
    public List<TelegramChannelActivity> getChannelActivities() { return channelActivities; }
    public void setChannelActivities(List<TelegramChannelActivity> channelActivities) { this.channelActivities = channelActivities; }
    
    public String getSearchTimestamp() { return searchTimestamp; }
    public void setSearchTimestamp(String searchTimestamp) { this.searchTimestamp = searchTimestamp; }
    
    /**
     * Проверяет, найдена ли какая-либо информация
     */
    public boolean hasResults() {
        return userInfo != null || 
               (channelMatches != null && !channelMatches.isEmpty()) ||
               (linkedAccounts != null && !linkedAccounts.isEmpty()) ||
               (groupMemberships != null && !groupMemberships.isEmpty());
    }
    
    /**
     * Возвращает краткое описание результатов
     */
    public String getSummary() {
        if (!success) {
            return "Поиск не удался: " + error;
        }
        
        if (!hasResults()) {
            return "Информация не найдена";
        }
        
        StringBuilder summary = new StringBuilder();
        
        if (userInfo != null) {
            summary.append("Пользователь найден");
            if (userInfo.getFirstName() != null) {
                summary.append(": ").append(userInfo.getFirstName());
                if (userInfo.getLastName() != null) {
                    summary.append(" ").append(userInfo.getLastName());
                }
            }
            summary.append("; ");
        }
        
        if (channelMatches != null && !channelMatches.isEmpty()) {
            summary.append("Найдено в ").append(channelMatches.size()).append(" каналах; ");
        }
        
        if (linkedAccounts != null && !linkedAccounts.isEmpty()) {
            summary.append("Связанных аккаунтов: ").append(linkedAccounts.size()).append("; ");
        }
        
        if (groupMemberships != null && !groupMemberships.isEmpty()) {
            summary.append("Участие в группах: ").append(groupMemberships.size());
        }
        
        return summary.toString();
    }
}
