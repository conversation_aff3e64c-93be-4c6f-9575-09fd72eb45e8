package com.warden.osint.auth;

// import com.fasterxml.jackson.databind.JsonNode;
// import com.fasterxml.jackson.databind.ObjectMapper;
import com.warden.osint.utils.HWIDGenerator;
import com.warden.osint.utils.Logger;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class AuthManager {
    
    // Firebase конфигурация
    private static final String FIREBASE_API_KEY = "AIzaSyCYzHSiKOI4W4Y5XoBahrVMBBw21VPY2jE";
    private static final String FIREBASE_DATABASE_URL = "https://doxz-c751f-default-rtdb.firebaseio.com";
    private static final String FIREBASE_AUTH_URL = "https://identitytoolkit.googleapis.com/v1/accounts";
    
    private final HttpClient httpClient;
    // private final ObjectMapper objectMapper;
    private final Logger logger;
    private final String currentHWID;
    
    private User currentUser;
    private String idToken;
    
    public AuthManager() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        // this.objectMapper = new ObjectMapper();
        this.logger = Logger.getInstance();
        this.currentHWID = HWIDGenerator.generateHWID();
        
        logger.system("AuthManager initialized with HWID: " + HWIDGenerator.getShortHWID(currentHWID));
    }
    
    /**
     * Регистрация нового пользователя
     */
    public CompletableFuture<AuthResult> registerUser(String email, String password, String username) {
        logger.auth("Attempting to register user: " + email);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Шаг 1: Регистрация в Firebase Auth
                Map<String, Object> authData = new HashMap<>();
                authData.put("email", email);
                authData.put("password", password);
                authData.put("returnSecureToken", true);
                
                String authJson = objectMapper.writeValueAsString(authData);
                
                HttpRequest authRequest = HttpRequest.newBuilder()
                        .uri(URI.create(FIREBASE_AUTH_URL + ":signUp?key=" + FIREBASE_API_KEY))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(authJson))
                        .build();
                
                HttpResponse<String> authResponse = httpClient.send(authRequest, HttpResponse.BodyHandlers.ofString());
                
                if (authResponse.statusCode() != 200) {
                    JsonNode errorNode = objectMapper.readTree(authResponse.body());
                    String errorMessage = errorNode.path("error").path("message").asText("Registration failed");
                    logger.error("Firebase Auth registration failed: " + errorMessage);
                    return new AuthResult(false, errorMessage, null);
                }
                
                JsonNode authResult = objectMapper.readTree(authResponse.body());
                String uid = authResult.path("localId").asText();
                this.idToken = authResult.path("idToken").asText();
                
                // Шаг 2: Создание пользователя в базе данных
                User user = new User(email, username, currentHWID);
                user.setUid(uid);
                
                String userJson = objectMapper.writeValueAsString(user);
                
                HttpRequest dbRequest = HttpRequest.newBuilder()
                        .uri(URI.create(FIREBASE_DATABASE_URL + "/users/" + uid + ".json?auth=" + idToken))
                        .header("Content-Type", "application/json")
                        .PUT(HttpRequest.BodyPublishers.ofString(userJson))
                        .build();
                
                HttpResponse<String> dbResponse = httpClient.send(dbRequest, HttpResponse.BodyHandlers.ofString());
                
                if (dbResponse.statusCode() != 200) {
                    logger.error("Failed to save user to database: " + dbResponse.body());
                    return new AuthResult(false, "Failed to save user data", null);
                }
                
                this.currentUser = user;
                logger.auth("User registered successfully: " + email + " (Admin: " + user.isAdmin() + ", HWID Approved: " + user.isHwidApproved() + ")");
                
                // Проверяем нужно ли подтверждение HWID
                if (!user.isAdmin() && !user.isHwidApproved()) {
                    String message = String.format(
                        "Your account has been created successfully!\n\n" +
                        "However, your hardware ID requires administrator approval before you can access the system.\n\n" +
                        "Please contact administrators:\n" +
                        "• Telegram: @InfernoSoulAttack\n" +
                        "• Telegram: @Svuanstvo\n\n" +
                        "Your HWID: %s\n" +
                        "Registration date: %s\n\n" +
                        "You will receive access once an administrator approves your hardware fingerprint.",
                        HWIDGenerator.getShortHWID(currentHWID),
                        user.getCreatedAt()
                    );
                    return new AuthResult(false, message, user);
                }
                
                return new AuthResult(true, "Registration successful", user);
                
            } catch (Exception e) {
                logger.error("Registration error", e);
                return new AuthResult(false, "Registration failed: " + e.getMessage(), null);
            }
        });
    }
    
    /**
     * Вход пользователя
     */
    public CompletableFuture<AuthResult> loginUser(String email, String password) {
        logger.auth("Attempting to login user: " + email);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Шаг 1: Аутентификация в Firebase Auth
                Map<String, Object> authData = new HashMap<>();
                authData.put("email", email);
                authData.put("password", password);
                authData.put("returnSecureToken", true);
                
                String authJson = objectMapper.writeValueAsString(authData);
                
                HttpRequest authRequest = HttpRequest.newBuilder()
                        .uri(URI.create(FIREBASE_AUTH_URL + ":signInWithPassword?key=" + FIREBASE_API_KEY))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(authJson))
                        .build();
                
                HttpResponse<String> authResponse = httpClient.send(authRequest, HttpResponse.BodyHandlers.ofString());
                
                if (authResponse.statusCode() != 200) {
                    JsonNode errorNode = objectMapper.readTree(authResponse.body());
                    String errorMessage = errorNode.path("error").path("message").asText("Login failed");
                    logger.error("Firebase Auth login failed: " + errorMessage);
                    return new AuthResult(false, errorMessage, null);
                }
                
                JsonNode authResult = objectMapper.readTree(authResponse.body());
                String uid = authResult.path("localId").asText();
                this.idToken = authResult.path("idToken").asText();
                
                // Шаг 2: Получение данных пользователя из базы
                HttpRequest userRequest = HttpRequest.newBuilder()
                        .uri(URI.create(FIREBASE_DATABASE_URL + "/users/" + uid + ".json?auth=" + idToken))
                        .GET()
                        .build();
                
                HttpResponse<String> userResponse = httpClient.send(userRequest, HttpResponse.BodyHandlers.ofString());
                
                if (userResponse.statusCode() != 200) {
                    logger.error("Failed to get user data: " + userResponse.body());
                    return new AuthResult(false, "Failed to get user data", null);
                }
                
                User user = objectMapper.readValue(userResponse.body(), User.class);
                user.setUid(uid);
                
                // Шаг 3: Проверка HWID
                if (!currentHWID.equals(user.getHwid())) {
                    logger.error("HWID mismatch for user: " + email);
                    return new AuthResult(false, "Hardware ID mismatch. This account is bound to different hardware.", null);
                }
                
                // Шаг 4: Проверка статуса аккаунта
                if (!user.canLogin()) {
                    if (!user.isActive()) {
                        return new AuthResult(false, "Account is blocked. Contact administrators for support.", null);
                    }
                    if (!user.isHwidApproved()) {
                        String message = String.format(
                            "Your hardware ID is pending approval.\n\n" +
                            "Please contact administrators:\n" +
                            "• Telegram: @InfernoSoulAttack\n" +
                            "• Telegram: @Svuanstvo\n\n" +
                            "Your HWID: %s",
                            HWIDGenerator.getShortHWID(currentHWID)
                        );
                        return new AuthResult(false, message, null);
                    }
                }
                
                // Шаг 5: Обновление времени последнего входа
                user.updateLastLogin();
                updateUserInDatabase(user);
                
                this.currentUser = user;
                logger.auth("User logged in successfully: " + email + " (Admin: " + user.isAdmin() + ")");
                
                return new AuthResult(true, "Login successful", user);
                
            } catch (Exception e) {
                logger.error("Login error", e);
                return new AuthResult(false, "Login failed: " + e.getMessage(), null);
            }
        });
    }
    
    /**
     * Обновление пользователя в базе данных
     */
    private void updateUserInDatabase(User user) {
        try {
            String userJson = objectMapper.writeValueAsString(user);
            
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(FIREBASE_DATABASE_URL + "/users/" + user.getUid() + ".json?auth=" + idToken))
                    .header("Content-Type", "application/json")
                    .PUT(HttpRequest.BodyPublishers.ofString(userJson))
                    .build();
            
            httpClient.sendAsync(request, HttpResponse.BodyHandlers.ofString());
            
        } catch (Exception e) {
            logger.error("Failed to update user in database", e);
        }
    }
    
    // Getters
    public User getCurrentUser() { return currentUser; }
    public String getCurrentHWID() { return currentHWID; }
    public boolean isLoggedIn() { return currentUser != null && idToken != null; }
    
    /**
     * Результат операции аутентификации
     */
    public static class AuthResult {
        private final boolean success;
        private final String message;
        private final User user;
        
        public AuthResult(boolean success, String message, User user) {
            this.success = success;
            this.message = message;
            this.user = user;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public User getUser() { return user; }
    }
}
