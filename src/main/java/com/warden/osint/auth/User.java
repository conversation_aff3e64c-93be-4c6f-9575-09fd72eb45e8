package com.warden.osint.auth;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@JsonIgnoreProperties(ignoreUnknown = true)
public class User {
    
    @JsonProperty("uid")
    private String uid;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("username")
    private String username;
    
    @JsonProperty("hwid")
    private String hwid;
    
    @JsonProperty("hwid_approved")
    private boolean hwidApproved;
    
    @JsonProperty("is_admin")
    private boolean isAdmin;
    
    @JsonProperty("is_active")
    private boolean isActive;
    
    @JsonProperty("created_at")
    private String createdAt;
    
    @JsonProperty("last_login")
    private String lastLogin;
    
    @JsonProperty("approved_by")
    private String approvedBy;
    
    @JsonProperty("approval_date")
    private String approvalDate;

    // Дополнительные поля для совместимости с Firebase
    @JsonProperty("active")
    private Boolean active;

    @JsonProperty("admin")
    private Boolean admin;

    @JsonProperty("displayName")
    private String displayName;

    @JsonProperty("statusDisplay")
    private String statusDisplay;

    // Конструкторы
    public User() {
        this.isActive = true;
        this.hwidApproved = false;
        this.isAdmin = false;
        this.createdAt = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    public User(String email, String username, String hwid) {
        this();
        this.email = email;
        this.username = username;
        this.hwid = hwid;
        
        // Автоматически делаем админом если это админский email
        if ("<EMAIL>".equals(email)) {
            this.isAdmin = true;
            this.hwidApproved = true;
            this.approvedBy = "SYSTEM";
            this.approvalDate = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }
    
    // Getters и Setters
    public String getUid() { return uid; }
    public void setUid(String uid) { this.uid = uid; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getHwid() { return hwid; }
    public void setHwid(String hwid) { this.hwid = hwid; }
    
    public boolean isHwidApproved() { return hwidApproved; }
    public void setHwidApproved(boolean hwidApproved) { this.hwidApproved = hwidApproved; }
    
    public boolean isAdmin() { return isAdmin; }
    public void setIsAdmin(boolean isAdmin) { this.isAdmin = isAdmin; }
    
    public boolean isActive() { return isActive; }
    public void setIsActive(boolean isActive) { this.isActive = isActive; }
    
    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    
    public String getLastLogin() { return lastLogin; }
    public void setLastLogin(String lastLogin) { this.lastLogin = lastLogin; }
    
    public String getApprovedBy() { return approvedBy; }
    public void setApprovedBy(String approvedBy) { this.approvedBy = approvedBy; }
    
    public String getApprovalDate() { return approvalDate; }
    public void setApprovalDate(String approvalDate) { this.approvalDate = approvalDate; }

    // Геттеры и сеттеры для дополнительных полей
    public Boolean getActive() { return active; }
    public void setActive(Boolean active) { this.active = active; }

    public Boolean getAdmin() { return admin; }
    public void setAdmin(Boolean admin) { this.admin = admin; }

    /**
     * Обновляет время последнего входа
     */
    public void updateLastLogin() {
        this.lastLogin = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    /**
     * Проверяет может ли пользователь войти в систему
     */
    public boolean canLogin() {
        return isActive && hwidApproved;
    }
    
    /**
     * Получает отображаемое имя пользователя (с логикой)
     */
    public String getDisplayName() {
        // Если есть поле displayName из Firebase, используем его
        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        }
        // Иначе используем username
        if (username != null && !username.trim().isEmpty()) {
            return username;
        }
        // В крайнем случае берем часть email
        return email != null ? email.split("@")[0] : "Unknown";
    }

    /**
     * Устанавливает отображаемое имя
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Получает статус пользователя для отображения (с логикой)
     */
    public String getStatusDisplay() {
        // Если есть поле statusDisplay из Firebase, используем его
        if (statusDisplay != null && !statusDisplay.trim().isEmpty()) {
            return statusDisplay;
        }
        // Иначе генерируем статус
        if (!isActive) return "🚫 BLOCKED";
        if (!hwidApproved) return "⏳ PENDING";
        if (isAdmin) return "👑 ADMIN";
        return "✅ ACTIVE";
    }

    /**
     * Устанавливает статус для отображения
     */
    public void setStatusDisplay(String statusDisplay) {
        this.statusDisplay = statusDisplay;
    }
    
    @Override
    public String toString() {
        return String.format("User{email='%s', username='%s', admin=%s, approved=%s, active=%s}", 
                           email, username, isAdmin, hwidApproved, isActive);
    }
}
