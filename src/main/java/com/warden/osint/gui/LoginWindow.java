package com.warden.osint.gui;

import com.warden.osint.auth.AuthManager;
import com.warden.osint.utils.Logger;
import com.warden.osint.utils.ThemeManager;
import javafx.animation.*;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.util.Duration;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.stage.Stage;

import java.util.concurrent.atomic.AtomicBoolean;

public class LoginWindow {
    
    private final Stage stage;
    private final AuthManager authManager;
    private final Logger logger;
    private final ThemeManager themeManager;
    
    private VBox root;
    private TextArea terminalArea;
    private TextField inputField;
    private TextField emailField;
    private PasswordField passwordField;
    private Button loginButton;
    private Button registerButton;
    private Button themeButton;
    
    private enum InputState {
        MENU, LOGIN_EMAIL, LOGIN_PASSWORD, REGISTER_EMAIL, REGISTER_PASSWORD, REGISTER_USERNAME
    }
    
    private InputState currentState = InputState.MENU;
    private String tempEmail = "";
    private String tempPassword = "";
    private String tempUsername = "";
    
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    
    public LoginWindow(Stage stage, AuthManager authManager) {
        this.stage = stage;
        this.authManager = authManager;
        this.logger = Logger.getInstance();
        this.themeManager = ThemeManager.getInstance();

        initializeUI();
        setupEventHandlers();
        showWelcomeMessage();
    }
    
    private void initializeUI() {
        // Адаптивность под разрешение экрана
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        javafx.geometry.Rectangle2D bounds = screen.getVisualBounds();

        double screenWidth = bounds.getWidth();
        double screenHeight = bounds.getHeight();

        root = new VBox(15);
        root.setAlignment(Pos.CENTER);
        root.setPadding(new Insets(30));
        themeManager.applyThemeToNode(root, "root", false);

        // Создаем центральную панель входа
        VBox loginPanel = createLoginPanel(screenWidth, screenHeight);

        root.getChildren().add(loginPanel);

        // Адаптивные размеры окна
        double windowWidth = Math.min(screenWidth * 0.7, 1000);
        double windowHeight = Math.min(screenHeight * 0.8, 800);

        Scene scene = new Scene(root, windowWidth, windowHeight);
        scene.setFill(Color.BLACK);

        // Принудительно очищаем стили для предотвращения конфликтов
        scene.getStylesheets().clear();

        // Простое решение - полагаемся только на индивидуальные стили элементов

        stage.setScene(scene);
        stage.setTitle("WARDEN v3.0 - Authentication");
        stage.setResizable(false);

        // Центрируем окно
        stage.setX((screenWidth - windowWidth) / 2);
        stage.setY((screenHeight - windowHeight) / 2);
    }

    private VBox createLoginPanel(double screenWidth, double screenHeight) {
        VBox loginPanel = new VBox(20);
        loginPanel.setAlignment(Pos.CENTER);
        loginPanel.setMaxWidth(Math.min(600, screenWidth * 0.5));
        themeManager.applyThemeToNode(loginPanel, "login_panel", false);

        // Анимированный заголовок
        double fontSize = Math.max(16, Math.min(24, screenWidth / 80));
        VBox animatedTitleBox = createAnimatedLoginTitle(fontSize);

        // Подзаголовок
        Label subtitleLabel = new Label("🔒 SECURE AUTHENTICATION PORTAL");
        subtitleLabel.setStyle(
            "-fx-font-size: " + (fontSize * 0.5) + "px;"
        );
        themeManager.applyThemeToNode(subtitleLabel, "subtitle", false);
        
        // Статус системы
        Label statusLabel = new Label("🟢 SYSTEM ONLINE | 🔒 SECURE CONNECTION | ⚡ READY");
        statusLabel.setStyle(
            "-fx-font-size: " + (fontSize * 0.4) + "px;"
        );
        themeManager.applyThemeToNode(statusLabel, "status", false);

        // Форма входа
        VBox formBox = createLoginForm(screenWidth, fontSize);

        // Терминальная область (компактная)
        terminalArea = new TextArea();
        terminalArea.setEditable(false);
        terminalArea.setPrefHeight(Math.min(200, screenHeight * 0.25));
        terminalArea.setMaxHeight(250);
        terminalArea.setStyle("-fx-font-size: " + (fontSize * 0.4) + "px;");
        themeManager.applyThemeToNode(terminalArea, "terminal", false);

        // Скрытое поле ввода для команд
        inputField = new TextField();
        inputField.setVisible(false);
        inputField.setManaged(false);

        loginPanel.getChildren().addAll(animatedTitleBox, subtitleLabel, statusLabel, formBox, terminalArea, inputField);
        return loginPanel;
    }

    private VBox createLoginForm(double screenWidth, double fontSize) {
        VBox formBox = new VBox(15);
        formBox.setAlignment(Pos.CENTER);

        // Email поле
        VBox emailBox = new VBox(5);
        Label emailLabel = new Label("📧 EMAIL ADDRESS:");
        emailLabel.setStyle("-fx-font-size: " + (fontSize * 0.5) + "px;");
        themeManager.applyThemeToNode(emailLabel, "subtitle", false);

        TextField emailField = new TextField();
        emailField.setPromptText("<EMAIL>");
        emailField.setPrefWidth(Math.min(400, screenWidth * 0.4));
        emailField.setStyle("-fx-font-size: " + (fontSize * 0.5) + "px; -fx-padding: 12px;");
        themeManager.applyThemeToNode(emailField, "input", false);

        emailBox.getChildren().addAll(emailLabel, emailField);

        // Password поле
        VBox passwordBox = new VBox(5);
        Label passwordLabel = new Label("🔐 PASSWORD:");
        passwordLabel.setStyle("-fx-font-size: " + (fontSize * 0.5) + "px;");
        themeManager.applyThemeToNode(passwordLabel, "subtitle", false);

        PasswordField passwordField = new PasswordField();
        passwordField.setPromptText("Enter your password");
        passwordField.setPrefWidth(Math.min(400, screenWidth * 0.4));
        passwordField.setStyle("-fx-font-size: " + (fontSize * 0.5) + "px; -fx-padding: 12px;");
        themeManager.applyThemeToNode(passwordField, "input", false);

        passwordBox.getChildren().addAll(passwordLabel, passwordField);

        // Кнопки
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);

        loginButton = createModernButton("🚀 LOGIN", "Sign in to your account", fontSize);
        registerButton = createModernButton("📝 REGISTER", "Create new account", fontSize);
        themeButton = createThemeButton(fontSize);

        buttonBox.getChildren().addAll(loginButton, registerButton);

        // Добавляем кнопку темы отдельно
        VBox themeBox = new VBox(10);
        themeBox.setAlignment(Pos.CENTER);
        themeBox.getChildren().add(themeButton);

        // Сохраняем ссылки на поля для использования в обработчиках
        this.emailField = emailField;
        this.passwordField = passwordField;

        formBox.getChildren().addAll(emailBox, passwordBox, buttonBox, themeBox);
        return formBox;
    }
    
    private VBox createAnimatedLoginTitle(double baseFontSize) {
        VBox titleContainer = new VBox(8);
        titleContainer.setAlignment(Pos.CENTER);

        // Главный заголовок
        Label mainTitle = new Label();
        mainTitle.setStyle("-fx-font-size: " + (baseFontSize * 1.2) + "px;");
        themeManager.applyThemeToNode(mainTitle, "title", false);

        // Версия
        Label versionLabel = new Label();
        versionLabel.setStyle("-fx-font-size: " + (baseFontSize * 0.8) + "px;");
        themeManager.applyThemeToNode(versionLabel, "subtitle", false);

        titleContainer.getChildren().addAll(mainTitle, versionLabel);

        // Запускаем печатную машинку
        startLoginTypewriter(mainTitle, "W A R D E N", 0);

        Timeline timeline = new Timeline(new KeyFrame(Duration.seconds(2), e ->
            startLoginTypewriter(versionLabel, "v3.0", 0)
        ));
        timeline.play();

        return titleContainer;
    }

    private void startLoginTypewriter(Label label, String text, int index) {
        if (index <= text.length()) {
            String currentText = text.substring(0, index);

            // Добавляем мигающий курсор
            if (index < text.length()) {
                currentText += "█";
            }

            label.setText(currentText);

            // Следующий символ через 100ms
            Timeline nextChar = new Timeline(new KeyFrame(Duration.millis(100), e ->
                startLoginTypewriter(label, text, index + 1)
            ));
            nextChar.play();
        } else {
            // Убираем курсор и добавляем легкое свечение
            label.setText(text);

            FadeTransition glow = new FadeTransition(Duration.seconds(0.8), label);
            glow.setFromValue(0.8);
            glow.setToValue(1.0);
            glow.setCycleCount(2);
            glow.setAutoReverse(true);
            glow.play();
        }
    }

    private Button createModernButton(String title, String description, double baseFontSize) {
        VBox buttonContent = new VBox(3);
        buttonContent.setAlignment(Pos.CENTER);

        Label titleLabel = new Label(title);
        titleLabel.setStyle(
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: " + (baseFontSize * 0.6) + "px;" +
            "-fx-font-weight: bold;"
        );

        Label descLabel = new Label(description);
        descLabel.setStyle(
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: " + (baseFontSize * 0.4) + "px;"
        );

        buttonContent.getChildren().addAll(titleLabel, descLabel);

        Button button = new Button();
        button.setGraphic(buttonContent);
        button.setPrefWidth(180);
        button.setPrefHeight(60);

        // Применяем тему к кнопке
        themeManager.applyThemeToNode(button, "button", false);

        // Применяем тему к лейблам
        themeManager.applyThemeToNode(titleLabel, "title", false);
        themeManager.applyThemeToNode(descLabel, "subtitle", false);

        return button;
    }

    private Button createThemeButton(double baseFontSize) {
        Button button = new Button();
        updateThemeButtonText(button);
        button.setPrefWidth(200);
        button.setPrefHeight(40);
        button.setStyle(
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: " + (baseFontSize * 0.5) + "px;" +
            "-fx-font-weight: bold;"
        );

        themeManager.applyThemeToNode(button, "button", false);

        return button;
    }

    private void updateThemeButtonText(Button button) {
        ThemeManager.Theme currentTheme = themeManager.getCurrentTheme();
        if (currentTheme == ThemeManager.Theme.MATRIX) {
            button.setText("🌙 Switch to Midnight Theme");
        } else {
            button.setText("🟢 Switch to Matrix Theme");
        }
    }

    private Button createTerminalButton(String text, String bgColor) {
        Button button = new Button(text);
        button.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 8px 16px;"
        );
        
        button.setOnMouseEntered(e -> button.setStyle(
            "-fx-background-color: #00ff00;" +
            "-fx-text-fill: #000000;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 8px 16px;"
        ));
        
        button.setOnMouseExited(e -> button.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 8px 16px;"
        ));
        
        return button;
    }
    
    private void setupEventHandlers() {
        // Обработка Enter в полях формы
        emailField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                passwordField.requestFocus();
            }
        });

        passwordField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                performLogin();
            }
        });

        // Скрытое поле для команд
        inputField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                handleInput();
            }
        });

        // Кнопки
        loginButton.setOnAction(e -> performLogin());
        registerButton.setOnAction(e -> performRegister());
        themeButton.setOnAction(e -> toggleTheme());
    }

    private void performLogin() {
        String email = emailField.getText().trim();
        String password = passwordField.getText();

        if (email.isEmpty() || password.isEmpty()) {
            appendToTerminal("❌ Please fill in all fields");
            return;
        }

        appendToTerminal("");
        appendToTerminal("🔐 Authenticating user: " + email);
        appendToTerminal("🔒 Verifying credentials...");
        appendToTerminal("🔍 Checking hardware fingerprint...");

        isProcessing.set(true);

        authManager.loginUser(email, password).thenAccept(result -> {
            Platform.runLater(() -> {
                isProcessing.set(false);

                if (result.isSuccess()) {
                    appendToTerminal("✅ Authentication successful!");
                    appendToTerminal("🎉 Welcome, " + result.getUser().getDisplayName());
                    appendToTerminal("🚀 Loading main interface...");

                    // Переход к основному окну
                    openMainWindow(result.getUser());
                } else {
                    appendToTerminal("❌ Authentication failed:");
                    appendToTerminal("   " + result.getMessage());
                    passwordField.clear();
                    passwordField.requestFocus();
                }
            });
        });
    }

    private void performRegister() {
        String email = emailField.getText().trim();
        String password = passwordField.getText();

        if (email.isEmpty() || password.isEmpty()) {
            appendToTerminal("❌ Please fill in all fields");
            return;
        }

        if (password.length() < 6) {
            appendToTerminal("❌ Password must be at least 6 characters");
            return;
        }

        // Простое имя пользователя из email
        String username = email.split("@")[0];

        appendToTerminal("");
        appendToTerminal("📝 Creating account for: " + email);
        appendToTerminal("🔒 Generating secure credentials...");
        appendToTerminal("🔍 Registering hardware fingerprint...");

        isProcessing.set(true);

        authManager.registerUser(email, password, username).thenAccept(result -> {
            Platform.runLater(() -> {
                isProcessing.set(false);

                if (result.isSuccess()) {
                    appendToTerminal("✅ Account created successfully!");
                    appendToTerminal("🎉 Welcome, " + result.getUser().getDisplayName());
                    appendToTerminal("🚀 Loading main interface...");

                    // Переход к основному окну
                    openMainWindow(result.getUser());
                } else {
                    appendToTerminal("ℹ️ Registration result:");
                    appendToTerminal("   " + result.getMessage());
                    passwordField.clear();
                }
            });
        });
    }
    
    private void showWelcomeMessage() {
        appendToTerminal("╔══════════════════════════════════════════════════════════════════════════════╗");
        appendToTerminal("║                      🔍 WARDEN v3.0 - AUTHENTICATION                       ║");
        appendToTerminal("╚══════════════════════════════════════════════════════════════════════════════╝");
        appendToTerminal("");
        appendToTerminal("🔒 SECURE AUTHENTICATION PORTAL INITIALIZED");
        appendToTerminal("🔍 Hardware ID: " + authManager.getCurrentHWID().substring(0, 16) + "...");
        appendToTerminal("🛡️ All credentials encrypted during transmission");
        appendToTerminal("🌐 Connected to Firebase authentication service");
        appendToTerminal("");
        appendToTerminal("📋 AUTHENTICATION OPTIONS:");
        appendToTerminal("  🚀 LOGIN    - Sign in with existing credentials");
        appendToTerminal("  📝 REGISTER - Create new account");
        appendToTerminal("");
        appendToTerminal("💡 Use the form above or type commands here");
        appendToTerminal("⚠️  New accounts require administrator approval");
        appendToTerminal("");
        appendToTerminal("📞 Support: @InfernoSoulAttack, @Svuanstvo");
        appendToTerminal("");
    }
    
    private void appendToTerminal(String text) {
        Platform.runLater(() -> {
            terminalArea.appendText(text + "\n");
            terminalArea.setScrollTop(Double.MAX_VALUE);
        });
    }
    
    private void handleInput() {
        if (isProcessing.get()) return;
        
        String input = inputField.getText().trim();
        inputField.clear();
        
        if (input.isEmpty()) return;
        
        switch (currentState) {
            case MENU:
                handleMenuCommand(input);
                break;
            case LOGIN_EMAIL:
                handleLoginEmail(input);
                break;
            case LOGIN_PASSWORD:
                handleLoginPassword(input);
                break;
            case REGISTER_EMAIL:
                handleRegisterEmail(input);
                break;
            case REGISTER_PASSWORD:
                handleRegisterPassword(input);
                break;
            case REGISTER_USERNAME:
                handleRegisterUsername(input);
                break;
        }
    }
    
    private void handleMenuCommand(String command) {
        appendToTerminal(command);
        
        switch (command.toUpperCase()) {
            case "LOGIN":
                appendToTerminal("💡 Use the LOGIN button above or fill the form");
                emailField.requestFocus();
                break;
            case "REGISTER":
                appendToTerminal("💡 Use the REGISTER button above or fill the form");
                emailField.requestFocus();
                break;
            case "EXIT":
                Platform.exit();
                break;
            default:
                appendToTerminal("[ERROR] Unknown command: " + command);
                appendToTerminal("Available commands: LOGIN, REGISTER, EXIT");
        }
    }
    

    
    private void handleLoginEmail(String email) {
        appendToTerminal(email);
        tempEmail = email;
        currentState = InputState.LOGIN_PASSWORD;
        appendToTerminal("Enter password: ");
        inputField.setPromptText("password");
    }
    
    private void handleLoginPassword(String password) {
        appendToTerminal("*".repeat(password.length()));
        tempPassword = password;
        
        appendToTerminal("");
        appendToTerminal("[SYSTEM] Authenticating user...");
        
        isProcessing.set(true);
        
        authManager.loginUser(tempEmail, tempPassword).thenAccept(result -> {
            Platform.runLater(() -> {
                isProcessing.set(false);
                
                if (result.isSuccess()) {
                    appendToTerminal("[SUCCESS] Authentication successful");
                    appendToTerminal("[SYSTEM] Welcome, " + result.getUser().getDisplayName());
                    
                    // Переход к основному окну
                    openMainWindow(result.getUser());
                } else {
                    appendToTerminal("[ERROR] Authentication failed: " + result.getMessage());
                    appendToTerminal("[SYSTEM] Access denied");
                    resetToMenu();
                }
            });
        });
    }
    
    private void handleRegisterEmail(String email) {
        appendToTerminal(email);
        tempEmail = email;
        currentState = InputState.REGISTER_PASSWORD;
        appendToTerminal("Enter password: ");
        inputField.setPromptText("minimum 6 characters");
    }
    
    private void handleRegisterPassword(String password) {
        appendToTerminal("*".repeat(password.length()));
        tempPassword = password;
        currentState = InputState.REGISTER_USERNAME;
        appendToTerminal("Enter username: ");
        inputField.setPromptText("display name");
    }
    
    private void handleRegisterUsername(String username) {
        appendToTerminal(username);
        tempUsername = username;
        
        appendToTerminal("");
        appendToTerminal("[SYSTEM] Creating account...");
        
        isProcessing.set(true);
        
        authManager.registerUser(tempEmail, tempPassword, tempUsername).thenAccept(result -> {
            Platform.runLater(() -> {
                isProcessing.set(false);
                
                if (result.isSuccess()) {
                    appendToTerminal("[SUCCESS] Account created successfully");
                    appendToTerminal("[SYSTEM] Welcome, " + result.getUser().getDisplayName());
                    
                    // Переход к основному окну
                    openMainWindow(result.getUser());
                } else {
                    appendToTerminal("[INFO] " + result.getMessage());
                    resetToMenu();
                }
            });
        });
    }
    
    private void resetToMenu() {
        currentState = InputState.MENU;
        tempEmail = "";
        tempPassword = "";
        tempUsername = "";
        appendToTerminal("");
        appendToTerminal("warden@osint:~$ ");
        inputField.setPromptText("Enter command...");
    }
    
    private void openMainWindow(com.warden.osint.auth.User user) {
        appendToTerminal("[SYSTEM] Loading main interface...");
        appendToTerminal("[SYSTEM] Access granted. Welcome, " + user.getDisplayName());

        // Создаем основное окно
        MainWindow mainWindow = new MainWindow(stage, authManager, user);
        mainWindow.show();
    }
    
    private void toggleTheme() {
        themeManager.toggleTheme();
        updateThemeButtonText(themeButton);

        // Применяем новую тему ко всем элементам
        applyThemeToAllElements();

        appendToTerminal("🎨 Theme switched to: " + themeManager.getCurrentTheme().getDisplayName());
    }

    private void applyThemeToAllElements() {
        // Применяем тему к основным элементам
        themeManager.applyThemeToNode(root, "root", true);

        // Находим и обновляем все элементы
        updateAllNodesTheme(root);
    }

    private void updateAllNodesTheme(javafx.scene.Node node) {
        if (node instanceof VBox && node.getStyleClass().contains("login-panel")) {
            themeManager.applyThemeToNode(node, "login_panel", true);
        } else if (node instanceof Label) {
            // Определяем тип лейбла по тексту или стилю
            Label label = (Label) node;
            if (label.getText().contains("WARDEN") || label.getText().contains("v3.0")) {
                themeManager.applyThemeToNode(node, "title", true);
            } else if (label.getText().contains("SECURE") || label.getText().contains("EMAIL") || label.getText().contains("PASSWORD")) {
                themeManager.applyThemeToNode(node, "subtitle", true);
            } else if (label.getText().contains("SYSTEM ONLINE")) {
                themeManager.applyThemeToNode(node, "status", true);
            }
        } else if (node instanceof TextArea) {
            themeManager.applyThemeToNode(node, "terminal", true);
        } else if (node instanceof TextField || node instanceof PasswordField) {
            themeManager.applyThemeToNode(node, "input", true);
        } else if (node instanceof Button) {
            themeManager.applyThemeToNode(node, "button", true);
        }

        // Рекурсивно обрабатываем дочерние элементы
        if (node instanceof javafx.scene.Parent) {
            javafx.scene.Parent parent = (javafx.scene.Parent) node;
            for (javafx.scene.Node child : parent.getChildrenUnmodifiable()) {
                updateAllNodesTheme(child);
            }
        }
    }

    public void show() {
        stage.show();
        inputField.requestFocus();
    }
}
