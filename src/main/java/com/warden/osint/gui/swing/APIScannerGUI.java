package com.warden.osint.gui.swing;

import com.warden.osint.api.APIScanner;
import com.warden.osint.api.APIScanResult;
import com.warden.osint.api.APIEndpointResult;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellRenderer;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * GUI для API Scanner - поиск баз данных
 */
public class APIScannerGUI extends JFrame {
    
    private APIScanner apiScanner;
    private SwingThemeManager themeManager;
    
    // UI компоненты
    private JTextField queryField;
    private JButton scanButton;
    private JButton quickScanButton;
    private JTextArea logArea;
    private JTable resultsTable;
    private DefaultTableModel tableModel;
    private JLabel statusLabel;
    private JProgressBar progressBar;
    
    public APIScannerGUI() {
        this.apiScanner = new APIScanner();
        this.themeManager = SwingThemeManager.getInstance();
        
        initializeUI();
        applyTheme();
    }
    
    private void initializeUI() {
        setTitle("🔍 WARDEN API Scanner - Database Hunter");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        // Основная панель
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // Верхняя панель с поиском
        JPanel searchPanel = createSearchPanel();
        
        // Центральная панель с результатами
        JPanel centerPanel = createCenterPanel();
        
        // Нижняя панель со статусом
        JPanel statusPanel = createStatusPanel();
        
        mainPanel.add(searchPanel, BorderLayout.NORTH);
        mainPanel.add(centerPanel, BorderLayout.CENTER);
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // Обработчики событий
        setupEventHandlers();
    }
    
    private JPanel createSearchPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        
        // Заголовок
        JLabel titleLabel = new JLabel("🔍 API Database Scanner", JLabel.CENTER);
        themeManager.applyThemeToTitleLabel(titleLabel);
        
        JLabel subtitleLabel = new JLabel("Поиск баз данных по множеству API источников", JLabel.CENTER);
        themeManager.applyThemeToSubtitleLabel(subtitleLabel);
        
        // Поле ввода
        JPanel inputPanel = new JPanel(new BorderLayout(10, 0));
        
        JLabel queryLabel = new JLabel("Запрос:");
        themeManager.applyThemeToLabel(queryLabel);
        
        queryField = new JTextField();
        queryField.setToolTipText("Введите номер телефона, email, username или другие данные для поиска");
        themeManager.applyThemeToTextField(queryField);
        
        // Кнопки
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        scanButton = new JButton("🚀 Полное сканирование");
        scanButton.setToolTipText("Сканирование всех API с анализом данных");
        themeManager.applyThemeToButton(scanButton);
        
        quickScanButton = new JButton("⚡ Быстрая проверка");
        quickScanButton.setToolTipText("Быстрая проверка доступности API");
        themeManager.applyThemeToButton(quickScanButton);
        
        JButton themeToggleButton = themeManager.createThemeToggleButton();
        
        buttonPanel.add(scanButton);
        buttonPanel.add(quickScanButton);
        buttonPanel.add(themeToggleButton);
        
        inputPanel.add(queryLabel, BorderLayout.WEST);
        inputPanel.add(queryField, BorderLayout.CENTER);
        inputPanel.add(buttonPanel, BorderLayout.EAST);
        
        // Прогресс бар
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("Готов к сканированию");
        progressBar.setVisible(false);
        
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.add(titleLabel, BorderLayout.NORTH);
        headerPanel.add(subtitleLabel, BorderLayout.CENTER);
        
        panel.add(headerPanel, BorderLayout.NORTH);
        panel.add(inputPanel, BorderLayout.CENTER);
        panel.add(progressBar, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        
        // Таблица результатов
        String[] columnNames = {"API Endpoint", "Статус", "Записей", "Тип БД", "Время", "Качество"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        resultsTable = new JTable(tableModel);
        resultsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultsTable.setRowHeight(25);
        
        // Настройка колонок
        resultsTable.getColumnModel().getColumn(0).setPreferredWidth(300);
        resultsTable.getColumnModel().getColumn(1).setPreferredWidth(150);
        resultsTable.getColumnModel().getColumn(2).setPreferredWidth(80);
        resultsTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        resultsTable.getColumnModel().getColumn(4).setPreferredWidth(80);
        resultsTable.getColumnModel().getColumn(5).setPreferredWidth(100);
        
        JScrollPane tableScrollPane = new JScrollPane(resultsTable);
        themeManager.applyThemeToScrollPane(tableScrollPane);
        
        // Область логов
        logArea = new JTextArea(8, 0);
        logArea.setEditable(false);
        logArea.setFont(new Font("Consolas", Font.PLAIN, 12));
        themeManager.applyThemeToTextArea(logArea);
        
        JScrollPane logScrollPane = new JScrollPane(logArea);
        themeManager.applyThemeToScrollPane(logScrollPane);
        
        // Разделитель
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tableScrollPane, logScrollPane);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.7);
        
        panel.add(splitPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        statusLabel = new JLabel("Готов к работе");
        themeManager.applyThemeToLabel(statusLabel);
        
        panel.add(statusLabel, BorderLayout.WEST);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        scanButton.addActionListener(e -> performFullScan());
        quickScanButton.addActionListener(e -> performQuickScan());
        
        queryField.addActionListener(e -> performFullScan());
        
        // Обработчик выбора строки в таблице
        resultsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = resultsTable.getSelectedRow();
                if (selectedRow >= 0) {
                    showEndpointDetails(selectedRow);
                }
            }
        });
    }
    
    private void performFullScan() {
        String query = queryField.getText().trim();
        if (query.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Введите запрос для поиска", "Ошибка", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Подготовка UI
        scanButton.setEnabled(false);
        quickScanButton.setEnabled(false);
        progressBar.setVisible(true);
        progressBar.setIndeterminate(true);
        progressBar.setString("Сканирование API...");
        tableModel.setRowCount(0);
        logArea.setText("");
        
        appendLog("🚀 Начинаю полное сканирование для: " + query);
        appendLog("📡 Проверяю " + getAPICount() + " API источников...");
        
        statusLabel.setText("Выполняется сканирование...");
        
        // Асинхронное сканирование
        CompletableFuture<APIScanResult> scanFuture = apiScanner.scanAllAPIs(query);
        
        scanFuture.thenAccept(result -> {
            SwingUtilities.invokeLater(() -> {
                displayScanResults(result);
                
                // Восстановление UI
                scanButton.setEnabled(true);
                quickScanButton.setEnabled(true);
                progressBar.setVisible(false);
                statusLabel.setText("Сканирование завершено");
            });
        }).exceptionally(throwable -> {
            SwingUtilities.invokeLater(() -> {
                appendLog("❌ Ошибка сканирования: " + throwable.getMessage());
                
                scanButton.setEnabled(true);
                quickScanButton.setEnabled(true);
                progressBar.setVisible(false);
                statusLabel.setText("Ошибка сканирования");
            });
            return null;
        });
    }
    
    private void performQuickScan() {
        scanButton.setEnabled(false);
        quickScanButton.setEnabled(false);
        progressBar.setVisible(true);
        progressBar.setIndeterminate(true);
        progressBar.setString("Быстрая проверка...");
        
        appendLog("⚡ Начинаю быструю проверку доступности API...");
        
        CompletableFuture<List<String>> quickScanFuture = apiScanner.quickScanAvailableAPIs();
        
        quickScanFuture.thenAccept(availableAPIs -> {
            SwingUtilities.invokeLater(() -> {
                appendLog("✅ Быстрая проверка завершена!");
                appendLog("📊 Доступных API: " + availableAPIs.size() + " из " + getAPICount());
                
                for (String api : availableAPIs) {
                    appendLog("  ✅ " + api);
                }
                
                scanButton.setEnabled(true);
                quickScanButton.setEnabled(true);
                progressBar.setVisible(false);
                statusLabel.setText("Быстрая проверка завершена");
            });
        });
    }
    
    private void displayScanResults(APIScanResult result) {
        // Отображение общей статистики
        appendLog("📊 " + result.getSummary());
        appendLog("");
        
        // Заполнение таблицы
        for (APIEndpointResult endpointResult : result.getEndpointResults()) {
            Object[] row = {
                shortenURL(endpointResult.getEndpoint()),
                endpointResult.getShortDescription(),
                endpointResult.getRecordCount() > 0 ? String.format("%,d", endpointResult.getRecordCount()) : "-",
                endpointResult.getDatabaseType() != null ? endpointResult.getDatabaseTypeDescription() : "-",
                String.format("%.1fs", endpointResult.getResponseTimeSeconds()),
                endpointResult.getQualityStars()
            };
            tableModel.addRow(row);
        }
        
        // Отображение топ результатов
        appendLog("🏆 ТОП-5 ЛУЧШИХ API:");
        List<APIEndpointResult> topAPIs = result.getTopAPIs(5);
        for (int i = 0; i < topAPIs.size(); i++) {
            APIEndpointResult api = topAPIs.get(i);
            appendLog(String.format("  %d. %s - %s", i + 1, shortenURL(api.getEndpoint()), api.getShortDescription()));
        }
    }
    
    private void showEndpointDetails(int row) {
        // Показать детали выбранного API endpoint
        String endpoint = (String) tableModel.getValueAt(row, 0);
        appendLog("🔍 Детали API: " + endpoint);
        // Здесь можно добавить больше деталей
    }
    
    private void appendLog(String message) {
        logArea.append(message + "\n");
        logArea.setCaretPosition(logArea.getDocument().getLength());
    }
    
    private String shortenURL(String url) {
        if (url.length() > 50) {
            return url.substring(0, 47) + "...";
        }
        return url;
    }
    
    private int getAPICount() {
        return 40; // Примерное количество API в списке
    }
    
    private void applyTheme() {
        themeManager.applyThemeToFrame(this);
        themeManager.applyThemeToContainer(this);
        repaint();
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // Используем стандартный Look and Feel

            new APIScannerGUI().setVisible(true);
        });
    }
}
