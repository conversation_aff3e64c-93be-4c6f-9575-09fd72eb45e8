package com.warden.osint.gui.swing;

import com.warden.osint.api.APIFinder;
import com.warden.osint.api.APIFinderResult;
import com.warden.osint.api.APISource;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * GUI для поиска API с базами данных
 */
public class APIFinderGUI extends JFrame {
    
    private APIFinder apiFinder;
    private SwingThemeManager themeManager;
    
    // UI компоненты
    private JButton searchButton;
    private JTextArea logArea;
    private JTable resultsTable;
    private DefaultTableModel tableModel;
    private JLabel statusLabel;
    private JProgressBar progressBar;
    private JTabbedPane tabbedPane;
    private JTextArea detailsArea;
    
    public APIFinderGUI() {
        this.apiFinder = new APIFinder();
        this.themeManager = SwingThemeManager.getInstance();
        
        initializeUI();
        applyTheme();
    }
    
    private void initializeUI() {
        setTitle("🔍 WARDEN API Database Finder - Поиск API с БД");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1400, 900);
        setLocationRelativeTo(null);
        
        // Основная панель
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // Верхняя панель
        JPanel topPanel = createTopPanel();
        
        // Центральная панель с вкладками
        tabbedPane = createTabbedPane();
        
        // Нижняя панель со статусом
        JPanel statusPanel = createStatusPanel();
        
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(tabbedPane, BorderLayout.CENTER);
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // Обработчики событий
        setupEventHandlers();
    }
    
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        
        // Заголовок
        JLabel titleLabel = new JLabel("🔍 API Database Finder", JLabel.CENTER);
        themeManager.applyThemeToTitleLabel(titleLabel);
        
        JLabel subtitleLabel = new JLabel("Поиск API источников с базами данных для OSINT", JLabel.CENTER);
        themeManager.applyThemeToSubtitleLabel(subtitleLabel);
        
        // Кнопки
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        searchButton = new JButton("🚀 Найти API с базами данных");
        searchButton.setToolTipText("Поиск API источников через поисковики, GitHub, каталоги");
        themeManager.applyThemeToButton(searchButton);
        
        JButton themeToggleButton = themeManager.createThemeToggleButton();
        
        buttonPanel.add(searchButton);
        buttonPanel.add(themeToggleButton);
        
        // Прогресс бар
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("Готов к поиску API");
        progressBar.setVisible(false);
        
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.add(titleLabel, BorderLayout.NORTH);
        headerPanel.add(subtitleLabel, BorderLayout.CENTER);
        
        panel.add(headerPanel, BorderLayout.NORTH);
        panel.add(buttonPanel, BorderLayout.CENTER);
        panel.add(progressBar, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JTabbedPane createTabbedPane() {
        JTabbedPane pane = new JTabbedPane();
        
        // Вкладка с результатами
        JPanel resultsPanel = createResultsPanel();
        pane.addTab("📊 Найденные API", resultsPanel);
        
        // Вкладка с логами
        JPanel logsPanel = createLogsPanel();
        pane.addTab("📝 Логи поиска", logsPanel);
        
        // Вкладка с деталями
        JPanel detailsPanel = createDetailsPanel();
        pane.addTab("🔍 Детали API", detailsPanel);
        
        return pane;
    }
    
    private JPanel createResultsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // Таблица результатов
        String[] columnNames = {"Тип", "Название API", "URL", "Записей", "Качество", "Статус"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        resultsTable = new JTable(tableModel);
        resultsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultsTable.setRowHeight(30);
        
        // Настройка колонок
        resultsTable.getColumnModel().getColumn(0).setPreferredWidth(60);
        resultsTable.getColumnModel().getColumn(1).setPreferredWidth(200);
        resultsTable.getColumnModel().getColumn(2).setPreferredWidth(300);
        resultsTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        resultsTable.getColumnModel().getColumn(4).setPreferredWidth(80);
        resultsTable.getColumnModel().getColumn(5).setPreferredWidth(100);
        
        JScrollPane tableScrollPane = new JScrollPane(resultsTable);
        themeManager.applyThemeToScrollPane(tableScrollPane);
        
        panel.add(tableScrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createLogsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        logArea = new JTextArea();
        logArea.setEditable(false);
        logArea.setFont(new Font("Consolas", Font.PLAIN, 12));
        themeManager.applyThemeToTextArea(logArea);
        
        JScrollPane logScrollPane = new JScrollPane(logArea);
        themeManager.applyThemeToScrollPane(logScrollPane);
        
        panel.add(logScrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createDetailsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        detailsArea = new JTextArea();
        detailsArea.setEditable(false);
        detailsArea.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        detailsArea.setText("Выберите API в таблице для просмотра деталей");
        themeManager.applyThemeToTextArea(detailsArea);
        
        JScrollPane detailsScrollPane = new JScrollPane(detailsArea);
        themeManager.applyThemeToScrollPane(detailsScrollPane);
        
        panel.add(detailsScrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        statusLabel = new JLabel("Готов к поиску API");
        themeManager.applyThemeToLabel(statusLabel);
        
        panel.add(statusLabel, BorderLayout.WEST);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        searchButton.addActionListener(e -> performAPISearch());
        
        // Обработчик выбора строки в таблице
        resultsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = resultsTable.getSelectedRow();
                if (selectedRow >= 0) {
                    showAPIDetails(selectedRow);
                }
            }
        });
        
        // Двойной клик для копирования URL
        resultsTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    int row = resultsTable.getSelectedRow();
                    if (row >= 0) {
                        String url = (String) tableModel.getValueAt(row, 2);
                        copyToClipboard(url);
                        appendLog("📋 URL скопирован в буфер обмена: " + url);
                    }
                }
            }
        });
    }
    
    private void performAPISearch() {
        // Подготовка UI
        searchButton.setEnabled(false);
        progressBar.setVisible(true);
        progressBar.setIndeterminate(true);
        progressBar.setString("Поиск API источников...");
        tableModel.setRowCount(0);
        logArea.setText("");
        detailsArea.setText("Поиск API...");
        
        appendLog("🚀 Начинаю поиск API с базами данных...");
        appendLog("🔍 Ищу через поисковые системы...");
        appendLog("📁 Сканирую GitHub репозитории...");
        appendLog("📚 Проверяю каталоги API...");
        appendLog("📱 Анализирую Telegram каналы...");
        appendLog("");
        
        statusLabel.setText("Выполняется поиск API...");
        
        // Асинхронный поиск
        CompletableFuture<APIFinderResult> searchFuture = apiFinder.findAPIsWithDatabases();
        
        searchFuture.thenAccept(result -> {
            SwingUtilities.invokeLater(() -> {
                displaySearchResults(result);
                
                // Восстановление UI
                searchButton.setEnabled(true);
                progressBar.setVisible(false);
                statusLabel.setText("Поиск завершен");
            });
        }).exceptionally(throwable -> {
            SwingUtilities.invokeLater(() -> {
                appendLog("❌ Ошибка поиска: " + throwable.getMessage());
                
                searchButton.setEnabled(true);
                progressBar.setVisible(false);
                statusLabel.setText("Ошибка поиска");
            });
            return null;
        });
    }
    
    private void displaySearchResults(APIFinderResult result) {
        // Отображение общей статистики
        appendLog("📊 " + result.getSummary());
        appendLog("");
        appendLog(result.getTypeStatistics());
        appendLog(result.getSourceStatistics());
        appendLog("");
        
        // Заполнение таблицы
        for (APISource source : result.getApiSources()) {
            Object[] row = {
                source.getTypeIcon(),
                source.getName(),
                source.getUrl(),
                source.getEstimatedRecords() > 0 ? formatRecordCount(source.getEstimatedRecords()) : "-",
                source.getQualityScore() > 0 ? source.getQualityScore() + "/100" : "-",
                source.isVerified() ? "✅ Проверен" : "❓ Не проверен"
            };
            tableModel.addRow(row);
        }
        
        // Отображение топ результатов
        appendLog("🏆 ТОП-10 ЛУЧШИХ API:");
        List<APISource> topAPIs = result.getTopQualityAPIs(10);
        for (int i = 0; i < topAPIs.size(); i++) {
            APISource api = topAPIs.get(i);
            appendLog(String.format("  %d. %s - %s", i + 1, api.getName(), api.getShortDescription()));
        }
        
        appendLog("");
        appendLog("💎 САМЫЕ БОЛЬШИЕ БАЗЫ ДАННЫХ:");
        List<APISource> largestAPIs = result.getLargestDatabaseAPIs(10);
        for (int i = 0; i < largestAPIs.size(); i++) {
            APISource api = largestAPIs.get(i);
            appendLog(String.format("  %d. %s - %s", i + 1, api.getName(), formatRecordCount(api.getEstimatedRecords())));
        }
    }
    
    private void showAPIDetails(int row) {
        String url = (String) tableModel.getValueAt(row, 2);
        
        // Найти соответствующий APISource
        // Для простоты показываем базовую информацию
        StringBuilder details = new StringBuilder();
        details.append("🔗 URL: ").append(url).append("\n");
        details.append("📝 Название: ").append(tableModel.getValueAt(row, 1)).append("\n");
        details.append("🎯 Тип: ").append(tableModel.getValueAt(row, 0)).append("\n");
        details.append("📊 Записей: ").append(tableModel.getValueAt(row, 3)).append("\n");
        details.append("⭐ Качество: ").append(tableModel.getValueAt(row, 4)).append("\n");
        details.append("✅ Статус: ").append(tableModel.getValueAt(row, 5)).append("\n");
        details.append("\n");
        details.append("💡 Двойной клик по строке скопирует URL в буфер обмена");
        
        detailsArea.setText(details.toString());
        tabbedPane.setSelectedIndex(2); // Переключаемся на вкладку деталей
    }
    
    private void copyToClipboard(String text) {
        try {
            java.awt.datatransfer.StringSelection stringSelection = 
                new java.awt.datatransfer.StringSelection(text);
            java.awt.datatransfer.Clipboard clipboard = 
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard();
            clipboard.setContents(stringSelection, null);
        } catch (Exception e) {
            // Игнорируем ошибки копирования
        }
    }
    
    private void appendLog(String message) {
        logArea.append(message + "\n");
        logArea.setCaretPosition(logArea.getDocument().getLength());
    }
    
    private String formatRecordCount(long count) {
        if (count >= 1000000000) {
            return String.format("%.1fB", count / 1000000000.0);
        } else if (count >= 1000000) {
            return String.format("%.1fM", count / 1000000.0);
        } else if (count >= 1000) {
            return String.format("%.1fK", count / 1000.0);
        } else {
            return String.valueOf(count);
        }
    }
    
    private void applyTheme() {
        themeManager.applyThemeToFrame(this);
        themeManager.applyThemeToContainer(this);
        repaint();
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new APIFinderGUI().setVisible(true);
        });
    }
}
