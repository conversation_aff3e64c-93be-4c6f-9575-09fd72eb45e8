package com.warden.osint.gui.swing;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Демонстрация WARDEN с системой тем на Swing
 * Работает БЕЗ JavaFX - только стандартная Java!
 */
public class SwingWardenDemo extends JFrame {
    
    private SwingThemeManager themeManager;
    private JTextArea terminalArea;
    private JTextField inputField;
    private JLabel titleLabel;
    private JLabel statusLabel;
    private JButton themeButton;
    private JButton searchButton;
    private JButton clearButton;
    
    public SwingWardenDemo() {
        themeManager = SwingThemeManager.getInstance();
        initializeUI();
        applyTheme();
    }
    
    private void initializeUI() {
        setTitle("🔍 WARDEN v3.0 - Intelligence Platform (Swing Edition)");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        // Основная панель
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // Верхняя панель с заголовком
        JPanel headerPanel = createHeaderPanel();
        
        // Центральная панель с терминалом
        JPanel centerPanel = createCenterPanel();
        
        // Левая панель с кнопками
        JPanel leftPanel = createLeftPanel();
        
        // Нижняя панель с вводом
        JPanel bottomPanel = createBottomPanel();
        
        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(centerPanel, BorderLayout.CENTER);
        mainPanel.add(leftPanel, BorderLayout.WEST);
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // Добавляем приветственное сообщение
        showWelcomeMessage();
    }
    
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        titleLabel = new JLabel("🔍 WARDEN v3.0 - INTELLIGENCE PLATFORM", JLabel.CENTER);
        
        statusLabel = new JLabel("🟢 SYSTEM ONLINE | 🎨 THEME SYSTEM ACTIVE | ⚡ READY", JLabel.CENTER);
        
        panel.add(titleLabel, BorderLayout.CENTER);
        panel.add(statusLabel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("🖥️ TERMINAL OUTPUT"));
        
        terminalArea = new JTextArea(20, 60);
        terminalArea.setEditable(false);
        terminalArea.setFont(new Font("Consolas", Font.PLAIN, 12));
        
        JScrollPane scrollPane = new JScrollPane(terminalArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createLeftPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(BorderFactory.createTitledBorder("🛠️ FUNCTIONS"));
        panel.setPreferredSize(new Dimension(200, 0));
        
        // Кнопка переключения темы
        themeButton = themeManager.createThemeToggleButton();
        themeButton.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        // Кнопка поиска
        searchButton = new JButton("📱 Phone Search");
        searchButton.setAlignmentX(Component.CENTER_ALIGNMENT);
        searchButton.addActionListener(e -> performPhoneSearch());
        
        // Кнопка очистки
        clearButton = new JButton("🗑️ Clear Terminal");
        clearButton.setAlignmentX(Component.CENTER_ALIGNMENT);
        clearButton.addActionListener(e -> clearTerminal());
        
        // Кнопка выхода
        JButton exitButton = new JButton("🚪 Exit");
        exitButton.setAlignmentX(Component.CENTER_ALIGNMENT);
        exitButton.addActionListener(e -> System.exit(0));
        
        panel.add(Box.createVerticalStrut(10));
        panel.add(new JLabel("🎨 THEMES", JLabel.CENTER));
        panel.add(Box.createVerticalStrut(5));
        panel.add(themeButton);
        
        panel.add(Box.createVerticalStrut(20));
        panel.add(new JLabel("🔍 OSINT", JLabel.CENTER));
        panel.add(Box.createVerticalStrut(5));
        panel.add(searchButton);
        
        panel.add(Box.createVerticalStrut(20));
        panel.add(new JLabel("🛠️ SYSTEM", JLabel.CENTER));
        panel.add(Box.createVerticalStrut(5));
        panel.add(clearButton);
        panel.add(Box.createVerticalStrut(10));
        panel.add(exitButton);
        
        panel.add(Box.createVerticalGlue());
        
        return panel;
    }
    
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 0));
        panel.setBorder(BorderFactory.createTitledBorder("💬 COMMAND INPUT"));
        
        inputField = new JTextField();
        inputField.setFont(new Font("Consolas", Font.PLAIN, 12));
        inputField.addActionListener(e -> processCommand());
        
        JButton sendButton = new JButton("📤 Send");
        sendButton.addActionListener(e -> processCommand());
        
        panel.add(new JLabel("warden@osint:~$ "), BorderLayout.WEST);
        panel.add(inputField, BorderLayout.CENTER);
        panel.add(sendButton, BorderLayout.EAST);
        
        return panel;
    }
    
    private void showWelcomeMessage() {
        appendToTerminal("╔══════════════════════════════════════════════════════════════════════════════╗");
        appendToTerminal("║                    🔍 WARDEN v3.0 - INTELLIGENCE PLATFORM                  ║");
        appendToTerminal("║                        🎨 Advanced Theme System Edition                     ║");
        appendToTerminal("╚══════════════════════════════════════════════════════════════════════════════╝");
        appendToTerminal("");
        appendToTerminal("🎉 Welcome to WARDEN v3.0 Swing Edition!");
        appendToTerminal("✨ This version works WITHOUT JavaFX - just pure Java Swing!");
        appendToTerminal("");
        appendToTerminal("🎨 NEW THEME SYSTEM:");
        appendToTerminal("  🟢 Matrix Theme - Classic green hacker aesthetic");
        appendToTerminal("  🌙 Midnight Theme - Elegant purple-blue design");
        appendToTerminal("");
        appendToTerminal("🔧 FEATURES:");
        appendToTerminal("  📱 Phone number OSINT search");
        appendToTerminal("  🎨 Real-time theme switching");
        appendToTerminal("  🖥️ Terminal-style interface");
        appendToTerminal("  ⚡ No JavaFX dependencies!");
        appendToTerminal("");
        appendToTerminal("💡 TIP: Click the theme button to switch between Matrix and Midnight themes!");
        appendToTerminal("🚀 Try typing 'help' for available commands.");
        appendToTerminal("");
        appendToTerminal("warden@osint:~$ ");
    }
    
    private void appendToTerminal(String text) {
        terminalArea.append(text + "\n");
        terminalArea.setCaretPosition(terminalArea.getDocument().getLength());
    }
    
    private void processCommand() {
        String command = inputField.getText().trim();
        if (command.isEmpty()) return;
        
        appendToTerminal("warden@osint:~$ " + command);
        
        switch (command.toLowerCase()) {
            case "help":
                showHelp();
                break;
            case "theme":
                toggleTheme();
                break;
            case "clear":
                clearTerminal();
                break;
            case "status":
                showStatus();
                break;
            case "search":
                performPhoneSearch();
                break;
            case "exit":
                System.exit(0);
                break;
            default:
                appendToTerminal("❌ Unknown command: " + command);
                appendToTerminal("💡 Type 'help' for available commands");
        }
        
        appendToTerminal("");
        inputField.setText("");
    }
    
    private void showHelp() {
        appendToTerminal("📋 AVAILABLE COMMANDS:");
        appendToTerminal("  help     - Show this help message");
        appendToTerminal("  theme    - Toggle between Matrix and Midnight themes");
        appendToTerminal("  search   - Perform phone number search");
        appendToTerminal("  clear    - Clear terminal output");
        appendToTerminal("  status   - Show system status");
        appendToTerminal("  exit     - Exit application");
        appendToTerminal("");
        appendToTerminal("🎨 You can also use the buttons on the left panel!");
    }
    
    private void toggleTheme() {
        SwingThemeManager.Theme oldTheme = themeManager.getCurrentTheme();
        themeManager.toggleTheme();
        SwingThemeManager.Theme newTheme = themeManager.getCurrentTheme();
        
        appendToTerminal("🎨 Theme switched from " + oldTheme.getDisplayName() + " to " + newTheme.getDisplayName());
        appendToTerminal("✨ Applying new color scheme...");
        
        applyTheme();
        
        appendToTerminal("🎉 Theme applied successfully!");
        
        // Обновляем текст кнопки
        themeButton.setText(themeManager.getCurrentTheme() == SwingThemeManager.Theme.MATRIX ? 
            "🌙 Switch to Midnight" : "🟢 Switch to Matrix");
    }
    
    private void performPhoneSearch() {
        String phone = JOptionPane.showInputDialog(this, 
            "Enter phone number to search:", 
            "📱 Phone Search", 
            JOptionPane.QUESTION_MESSAGE);
        
        if (phone != null && !phone.trim().isEmpty()) {
            appendToTerminal("🔍 Searching for phone number: " + phone);
            appendToTerminal("📡 Connecting to OSINT databases...");
            appendToTerminal("⏳ Please wait...");
            
            // Симуляция поиска
            Timer timer = new Timer(2000, e -> {
                appendToTerminal("✅ Search completed!");
                appendToTerminal("📋 Results for " + phone + ":");
                appendToTerminal("  📍 Location: Moscow, Russia");
                appendToTerminal("  📱 Carrier: MTS");
                appendToTerminal("  🏢 Type: Mobile");
                appendToTerminal("  ⚠️ Note: This is a demo - real OSINT integration coming soon!");
            });
            timer.setRepeats(false);
            timer.start();
        }
    }
    
    private void clearTerminal() {
        terminalArea.setText("");
        appendToTerminal("🗑️ Terminal cleared");
        appendToTerminal("warden@osint:~$ ");
    }
    
    private void showStatus() {
        appendToTerminal("📊 SYSTEM STATUS:");
        appendToTerminal("  🎨 Current Theme: " + themeManager.getCurrentTheme().getDisplayName());
        appendToTerminal("  ☕ Java Version: " + System.getProperty("java.version"));
        appendToTerminal("  💻 OS: " + System.getProperty("os.name"));
        appendToTerminal("  🖥️ GUI Framework: Java Swing (No JavaFX required!)");
        appendToTerminal("  🟢 Status: All systems operational");
    }
    
    private void applyTheme() {
        // Применяем тему к основному окну
        themeManager.applyThemeToFrame(this);
        
        // Применяем тему ко всем компонентам
        themeManager.applyThemeToContainer(this);
        
        // Специальная обработка для заголовков
        themeManager.applyThemeToTitleLabel(titleLabel);
        themeManager.applyThemeToSubtitleLabel(statusLabel);
        
        // Обновляем отображение
        repaint();
    }
    
    public static void main(String[] args) {
        // Устанавливаем Look and Feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        SwingUtilities.invokeLater(() -> {
            System.out.println("🔍 Starting WARDEN v3.0 Swing Edition...");
            System.out.println("🎨 Advanced Theme System - No JavaFX required!");
            
            new SwingWardenDemo().setVisible(true);
            
            System.out.println("✅ WARDEN started successfully!");
            System.out.println("🎉 Try switching themes with the button!");
        });
    }
}
