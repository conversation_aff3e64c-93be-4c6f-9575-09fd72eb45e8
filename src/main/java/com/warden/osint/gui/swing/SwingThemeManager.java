package com.warden.osint.gui.swing;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.Map;

/**
 * Менеджер тем для Swing версии WARDEN
 * Поддерживает Matrix и Midnight темы без JavaFX
 */
public class SwingThemeManager {
    
    private static SwingThemeManager instance;
    private Theme currentTheme = Theme.MATRIX;
    
    public enum Theme {
        MATRIX("Matrix", "🟢 Matrix Theme", "Классическая зеленая тема в стиле хакера"),
        MIDNIGHT("Midnight", "🌙 Midnight Theme", "Элегантная полуночная тема с фиолетово-синими оттенками");
        
        private final String name;
        private final String displayName;
        private final String description;
        
        Theme(String name, String displayName, String description) {
            this.name = name;
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    private SwingThemeManager() {}
    
    public static SwingThemeManager getInstance() {
        if (instance == null) {
            instance = new SwingThemeManager();
        }
        return instance;
    }
    
    public void toggleTheme() {
        currentTheme = (currentTheme == Theme.MATRIX) ? Theme.MIDNIGHT : Theme.MATRIX;
        System.out.println("🎨 Theme switched to: " + currentTheme.getDisplayName());
    }
    
    public void setTheme(Theme theme) {
        if (theme != currentTheme) {
            currentTheme = theme;
            System.out.println("🎨 Theme set to: " + currentTheme.getDisplayName());
        }
    }
    
    public Theme getCurrentTheme() {
        return currentTheme;
    }
    
    // ==================== ЦВЕТОВЫЕ СХЕМЫ ====================
    
    public Color getPrimaryColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый
            new Color(187, 134, 252);   // #bb86fc - фиолетовый
    }
    
    public Color getSecondaryColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 204, 255) :    // #00ccff - голубой
            new Color(3, 218, 198);     // #03dac6 - бирюзовый
    }
    
    public Color getAccentColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(255, 255, 0) :    // #ffff00 - желтый
            new Color(207, 102, 121);   // #cf6679 - розовый
    }
    
    public Color getBackgroundColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 0, 0) :        // #000000 - черный
            new Color(18, 18, 18);      // #121212 - темно-серый
    }
    
    public Color getPanelBackgroundColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 17, 0) :       // #001100 - темно-зеленый
            new Color(30, 30, 46);      // #1e1e2e - темно-фиолетовый
    }
    
    public Color getTerminalBackgroundColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 0, 0) :        // #000000 - черный
            new Color(13, 17, 23);      // #0d1117 - очень темный
    }
    
    public Color getInputBackgroundColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 0, 0) :        // #000000 - черный
            new Color(22, 27, 34);      // #161b22 - темно-серый
    }
    
    public Color getButtonBackgroundColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 51, 0) :       // #003300 - темно-зеленый
            new Color(45, 45, 66);      // #2d2d42 - темно-фиолетовый
    }
    
    public Color getButtonHoverBackgroundColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый
            new Color(187, 134, 252);   // #bb86fc - фиолетовый
    }
    
    public Color getTextColor() {
        return getPrimaryColor();
    }
    
    public Color getDimmedTextColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 102, 0) :      // #006600 - приглушенный зеленый
            new Color(108, 112, 134);   // #6c7086 - приглушенный серый
    }
    
    public Color getBorderColor() {
        return getPrimaryColor();
    }
    
    public Color getErrorColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(255, 0, 0) :      // #ff0000 - красный
            new Color(243, 139, 168);   // #f38ba8 - розовый
    }
    
    public Color getSuccessColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 255, 0) :      // #00ff00 - зеленый
            new Color(166, 227, 161);   // #a6e3a1 - светло-зеленый
    }
    
    // ==================== ПРИМЕНЕНИЕ ТЕМ К КОМПОНЕНТАМ ====================
    
    public void applyThemeToFrame(JFrame frame) {
        frame.getContentPane().setBackground(getBackgroundColor());
        frame.setForeground(getTextColor());
    }
    
    public void applyThemeToPanel(JPanel panel) {
        panel.setBackground(getPanelBackgroundColor());
        panel.setForeground(getTextColor());
        panel.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
    }
    
    public void applyThemeToLabel(JLabel label) {
        label.setForeground(getTextColor());
        label.setOpaque(false);
    }
    
    public void applyThemeToTitleLabel(JLabel label) {
        label.setForeground(getPrimaryColor());
        label.setFont(new Font("Consolas", Font.BOLD, 24));
        label.setOpaque(false);
    }
    
    public void applyThemeToSubtitleLabel(JLabel label) {
        label.setForeground(getSecondaryColor());
        label.setFont(new Font("Consolas", Font.BOLD, 14));
        label.setOpaque(false);
    }
    
    public void applyThemeToButton(JButton button) {
        button.setBackground(getButtonBackgroundColor());
        button.setForeground(getTextColor());
        button.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        button.setFont(new Font("Consolas", Font.BOLD, 12));
        button.setFocusPainted(false);
        
        // Добавляем эффекты наведения
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(getButtonHoverBackgroundColor());
                button.setForeground(Color.BLACK);
            }
            
            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(getButtonBackgroundColor());
                button.setForeground(getTextColor());
            }
        });
    }
    
    public void applyThemeToTextField(JTextField field) {
        field.setBackground(getInputBackgroundColor());
        field.setForeground(getTextColor());
        field.setCaretColor(getTextColor());
        field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        field.setFont(new Font("Consolas", Font.PLAIN, 12));
    }
    
    public void applyThemeToPasswordField(JPasswordField field) {
        field.setBackground(getInputBackgroundColor());
        field.setForeground(getTextColor());
        field.setCaretColor(getTextColor());
        field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        field.setFont(new Font("Consolas", Font.PLAIN, 12));
    }
    
    public void applyThemeToTextArea(JTextArea area) {
        area.setBackground(getTerminalBackgroundColor());
        area.setForeground(getTextColor());
        area.setCaretColor(getTextColor());
        area.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        area.setFont(new Font("Consolas", Font.PLAIN, 12));
    }
    
    public void applyThemeToScrollPane(JScrollPane scrollPane) {
        scrollPane.getViewport().setBackground(getTerminalBackgroundColor());
        scrollPane.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        
        // Стилизуем скроллбары
        JScrollBar vScrollBar = scrollPane.getVerticalScrollBar();
        JScrollBar hScrollBar = scrollPane.getHorizontalScrollBar();
        
        vScrollBar.setBackground(getPanelBackgroundColor());
        vScrollBar.setForeground(getBorderColor());
        
        hScrollBar.setBackground(getPanelBackgroundColor());
        hScrollBar.setForeground(getBorderColor());
    }
    
    /**
     * Применяет тему ко всем компонентам в контейнере рекурсивно
     */
    public void applyThemeToContainer(Container container) {
        for (Component component : container.getComponents()) {
            if (component instanceof JPanel) {
                applyThemeToPanel((JPanel) component);
            } else if (component instanceof JLabel) {
                applyThemeToLabel((JLabel) component);
            } else if (component instanceof JButton) {
                applyThemeToButton((JButton) component);
            } else if (component instanceof JTextField) {
                applyThemeToTextField((JTextField) component);
            } else if (component instanceof JPasswordField) {
                applyThemeToPasswordField((JPasswordField) component);
            } else if (component instanceof JTextArea) {
                applyThemeToTextArea((JTextArea) component);
            } else if (component instanceof JScrollPane) {
                applyThemeToScrollPane((JScrollPane) component);
            }
            
            // Рекурсивно применяем к дочерним контейнерам
            if (component instanceof Container) {
                applyThemeToContainer((Container) component);
            }
        }
    }
    
    /**
     * Создает стилизованную кнопку переключения темы
     */
    public JButton createThemeToggleButton() {
        JButton button = new JButton(getThemeToggleText());
        applyThemeToButton(button);
        
        button.addActionListener(e -> {
            toggleTheme();
            button.setText(getThemeToggleText());
            
            // Находим родительское окно и обновляем тему
            Window window = SwingUtilities.getWindowAncestor(button);
            if (window != null) {
                applyThemeToContainer(window);
                window.repaint();
            }
        });
        
        return button;
    }
    
    private String getThemeToggleText() {
        return currentTheme == Theme.MATRIX ? 
            "🌙 Switch to Midnight" : 
            "🟢 Switch to Matrix";
    }
}
