package com.warden.osint.gui.swing;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.util.HashMap;
import java.util.Map;

/**
 * Менеджер тем для Swing версии WARDEN
 * Поддерживает Matrix и Midnight темы без JavaFX
 */
public class SwingThemeManager {
    
    private static SwingThemeManager instance;
    private Theme currentTheme = Theme.MATRIX;
    
    public enum Theme {
        MATRIX("Matrix", "🟢 Matrix Theme", "Классическая зеленая тема в стиле хакера"),
        MIDNIGHT("Midnight", "🌙 Midnight Theme", "Элегантная полуночная тема с фиолетово-синими оттенками");
        
        private final String name;
        private final String displayName;
        private final String description;
        
        Theme(String name, String displayName, String description) {
            this.name = name;
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    private SwingThemeManager() {}
    
    public static SwingThemeManager getInstance() {
        if (instance == null) {
            instance = new SwingThemeManager();
        }
        return instance;
    }
    
    public void toggleTheme() {
        currentTheme = (currentTheme == Theme.MATRIX) ? Theme.MIDNIGHT : Theme.MATRIX;
        System.out.println("🎨 Theme switched to: " + currentTheme.getDisplayName());
    }
    
    public void setTheme(Theme theme) {
        if (theme != currentTheme) {
            currentTheme = theme;
            System.out.println("🎨 Theme set to: " + currentTheme.getDisplayName());
        }
    }
    
    public Theme getCurrentTheme() {
        return currentTheme;
    }
    
    // ==================== ЦВЕТОВЫЕ СХЕМЫ ====================
    
    public Color getPrimaryColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый
            new Color(187, 134, 252);   // #bb86fc - фиолетовый
    }
    
    public Color getSecondaryColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 204, 255) :    // #00ccff - голубой
            new Color(3, 218, 198);     // #03dac6 - бирюзовый
    }
    
    public Color getAccentColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(255, 255, 0) :    // #ffff00 - желтый
            new Color(207, 102, 121);   // #cf6679 - розовый
    }
    
    public Color getBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(25, 28, 35);      // #191c23 - современный темный (Midnight)
    }

    public Color getPanelBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 17, 0) :       // #001100 - темно-зеленый (Matrix)
            new Color(35, 39, 47);      // #23272f - современный серый (Midnight)
    }

    public Color getTerminalBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(40, 44, 52);      // #282c34 - светлее для Midnight (НЕ терминальный!)
    }

    public Color getInputBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(45, 49, 57);      // #2d3139 - современный (Midnight)
    }

    public Color getButtonBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 51, 0) :       // #003300 - темно-зеленый (Matrix)
            new Color(88, 101, 242);    // #5865f2 - Discord синий (Midnight)
    }

    public Color getButtonHoverBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый (Matrix)
            new Color(114, 137, 218);   // #7289da - светлее синий (Midnight)
    }
    
    public Color getTextColor() {
        return getPrimaryColor();
    }
    
    public Color getDimmedTextColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 102, 0) :      // #006600 - приглушенный зеленый
            new Color(108, 112, 134);   // #6c7086 - приглушенный серый
    }
    
    public Color getBorderColor() {
        return getPrimaryColor();
    }
    
    public Color getErrorColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(255, 0, 0) :      // #ff0000 - красный
            new Color(243, 139, 168);   // #f38ba8 - розовый
    }
    
    public Color getSuccessColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 255, 0) :      // #00ff00 - зеленый
            new Color(166, 227, 161);   // #a6e3a1 - светло-зеленый
    }
    
    // ==================== ПРИМЕНЕНИЕ ТЕМ К КОМПОНЕНТАМ ====================
    
    public void applyThemeToFrame(JFrame frame) {
        frame.getContentPane().setBackground(getBackgroundColor());
        frame.setForeground(getTextColor());
    }
    
    public void applyThemeToPanel(JPanel panel) {
        panel.setBackground(getPanelBackgroundColor());
        panel.setForeground(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            panel.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        } else {
            // Midnight - современный стиль с закругленными углами
            panel.setBorder(new RoundedBorder(20));
        }
    }
    
    public void applyThemeToLabel(JLabel label) {
        label.setForeground(getTextColor());
        label.setOpaque(false);
    }
    
    public void applyThemeToTitleLabel(JLabel label) {
        label.setForeground(getPrimaryColor());
        if (currentTheme == Theme.MATRIX) {
            label.setFont(new Font("Consolas", Font.BOLD, 24));
        } else {
            label.setFont(new Font("Segoe UI", Font.BOLD, 26));
        }
        label.setOpaque(false);
    }

    public void applyThemeToSubtitleLabel(JLabel label) {
        label.setForeground(getSecondaryColor());
        if (currentTheme == Theme.MATRIX) {
            label.setFont(new Font("Consolas", Font.BOLD, 14));
        } else {
            label.setFont(new Font("Segoe UI", Font.PLAIN, 15));
        }
        label.setOpaque(false);
    }
    
    public void applyThemeToButton(JButton button) {
        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            button.setBackground(getButtonBackgroundColor());
            button.setForeground(getTextColor());
            button.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            button.setFont(new Font("Consolas", Font.BOLD, 12));
            button.setFocusPainted(false);
        } else {
            // Midnight - современный стиль с круглыми кнопками
            button.setBackground(getButtonBackgroundColor());
            button.setForeground(getTextColor());
            button.setFont(new Font("Segoe UI", Font.BOLD, 13));
            button.setFocusPainted(false);
            button.setBorderPainted(false);
            button.setContentAreaFilled(false);
            button.setOpaque(true);

            // Создаем круглую кнопку
            button.setBorder(new RoundedBorder(25));
            button.setPreferredSize(new Dimension(120, 40));
        }

        // Добавляем эффекты наведения
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(getButtonHoverBackgroundColor());
                if (currentTheme == Theme.MATRIX) {
                    button.setForeground(Color.BLACK);
                } else {
                    button.setForeground(Color.WHITE);
                }
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(getButtonBackgroundColor());
                button.setForeground(getTextColor());
            }
        });
    }
    
    public void applyThemeToTextField(JTextField field) {
        field.setBackground(getInputBackgroundColor());
        field.setForeground(getTextColor());
        field.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            field.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - современный стиль
            field.setBorder(new RoundedBorder(15));
            field.setFont(new Font("Segoe UI", Font.PLAIN, 13));
            field.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(15),
                BorderFactory.createEmptyBorder(8, 15, 8, 15)
            ));
        }
    }
    
    public void applyThemeToPasswordField(JPasswordField field) {
        field.setBackground(getInputBackgroundColor());
        field.setForeground(getTextColor());
        field.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            field.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - современный стиль
            field.setFont(new Font("Segoe UI", Font.PLAIN, 13));
            field.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(15),
                BorderFactory.createEmptyBorder(8, 15, 8, 15)
            ));
        }
    }
    
    public void applyThemeToTextArea(JTextArea area) {
        area.setBackground(getTerminalBackgroundColor());
        area.setForeground(getTextColor());
        area.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            area.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            area.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - современный стиль (НЕ терминальный!)
            area.setBorder(new RoundedBorder(15));
            area.setFont(new Font("Segoe UI", Font.PLAIN, 13));
            area.setBackground(new Color(40, 44, 52)); // Более светлый фон
        }
    }
    
    public void applyThemeToScrollPane(JScrollPane scrollPane) {
        scrollPane.getViewport().setBackground(getTerminalBackgroundColor());
        scrollPane.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        
        // Стилизуем скроллбары
        JScrollBar vScrollBar = scrollPane.getVerticalScrollBar();
        JScrollBar hScrollBar = scrollPane.getHorizontalScrollBar();
        
        vScrollBar.setBackground(getPanelBackgroundColor());
        vScrollBar.setForeground(getBorderColor());
        
        hScrollBar.setBackground(getPanelBackgroundColor());
        hScrollBar.setForeground(getBorderColor());
    }
    
    /**
     * Применяет тему ко всем компонентам в контейнере рекурсивно
     */
    public void applyThemeToContainer(Container container) {
        for (Component component : container.getComponents()) {
            if (component instanceof JPanel) {
                applyThemeToPanel((JPanel) component);
            } else if (component instanceof JLabel) {
                applyThemeToLabel((JLabel) component);
            } else if (component instanceof JButton) {
                applyThemeToButton((JButton) component);
            } else if (component instanceof JTextField) {
                applyThemeToTextField((JTextField) component);
            } else if (component instanceof JPasswordField) {
                applyThemeToPasswordField((JPasswordField) component);
            } else if (component instanceof JTextArea) {
                applyThemeToTextArea((JTextArea) component);
            } else if (component instanceof JScrollPane) {
                applyThemeToScrollPane((JScrollPane) component);
            }
            
            // Рекурсивно применяем к дочерним контейнерам
            if (component instanceof Container) {
                applyThemeToContainer((Container) component);
            }
        }
    }
    
    /**
     * Создает стилизованную кнопку переключения темы
     */
    public JButton createThemeToggleButton() {
        JButton button = new JButton(getThemeToggleText());
        applyThemeToButton(button);
        
        button.addActionListener(e -> {
            toggleTheme();
            button.setText(getThemeToggleText());
            
            // Находим родительское окно и обновляем тему
            Window window = SwingUtilities.getWindowAncestor(button);
            if (window != null) {
                applyThemeToContainer(window);
                window.repaint();
            }
        });
        
        return button;
    }
    
    private String getThemeToggleText() {
        return currentTheme == Theme.MATRIX ?
            "🌙 Switch to Midnight" :
            "🟢 Switch to Matrix";
    }

    /**
     * Класс для создания круглых границ в Midnight теме
     */
    public static class RoundedBorder implements javax.swing.border.Border {
        private int radius;

        public RoundedBorder(int radius) {
            this.radius = radius;
        }

        @Override
        public Insets getBorderInsets(Component c) {
            return new Insets(radius/2, radius/2, radius/2, radius/2);
        }

        @Override
        public boolean isBorderOpaque() {
            return false;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            SwingThemeManager manager = SwingThemeManager.getInstance();
            if (manager.getCurrentTheme() == Theme.MIDNIGHT) {
                g2d.setColor(manager.getPrimaryColor());
                g2d.setStroke(new BasicStroke(2));
                g2d.drawRoundRect(x + 1, y + 1, width - 3, height - 3, radius, radius);
            } else {
                // Matrix тема - обычная граница
                g2d.setColor(manager.getBorderColor());
                g2d.setStroke(new BasicStroke(2));
                g2d.drawRect(x + 1, y + 1, width - 3, height - 3);
            }

            g2d.dispose();
        }
    }
}
