package com.warden.osint.gui.swing;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.util.HashMap;
import java.util.Map;

/**
 * Менеджер тем для Swing версии WARDEN
 * Поддерживает Matrix и Midnight темы без JavaFX
 */
public class SwingThemeManager {
    
    private static SwingThemeManager instance;
    private Theme currentTheme = Theme.MATRIX;
    
    public enum Theme {
        MATRIX("Matrix", "🟢 Matrix Theme", "Классическая зеленая тема в стиле хакера"),
        MIDNIGHT("Midnight", "🌙 Midnight Theme", "Элегантная полуночная тема с фиолетово-синими оттенками");
        
        private final String name;
        private final String displayName;
        private final String description;
        
        Theme(String name, String displayName, String description) {
            this.name = name;
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    private SwingThemeManager() {}
    
    public static SwingThemeManager getInstance() {
        if (instance == null) {
            instance = new SwingThemeManager();
        }
        return instance;
    }
    
    public void toggleTheme() {
        currentTheme = (currentTheme == Theme.MATRIX) ? Theme.MIDNIGHT : Theme.MATRIX;
        System.out.println("🎨 Theme switched to: " + currentTheme.getDisplayName());
    }
    
    public void setTheme(Theme theme) {
        if (theme != currentTheme) {
            currentTheme = theme;
            System.out.println("🎨 Theme set to: " + currentTheme.getDisplayName());
        }
    }
    
    public Theme getCurrentTheme() {
        return currentTheme;
    }
    
    // ==================== ЦВЕТОВЫЕ СХЕМЫ ====================
    
    public Color getPrimaryColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый (Matrix)
            new Color(255, 255, 255);   // #ffffff - белый текст (Midnight)
    }

    public Color getSecondaryColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 204, 255) :    // #00ccff - голубой (Matrix)
            new Color(156, 163, 175);   // #9ca3af - серый текст (Midnight)
    }

    public Color getAccentColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(255, 255, 0) :    // #ffff00 - желтый (Matrix)
            new Color(99, 102, 241);    // #6366f1 - индиго акцент (Midnight)
    }
    
    public Color getBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(15, 23, 42);      // #0f172a - slate-900 (Midnight)
    }

    public Color getPanelBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 17, 0) :       // #001100 - темно-зеленый (Matrix)
            new Color(30, 41, 59);      // #1e293b - slate-800 (Midnight)
    }

    public Color getTerminalBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(51, 65, 85);      // #334155 - slate-700 (Midnight)
    }

    public Color getInputBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(71, 85, 105);     // #475569 - slate-600 (Midnight)
    }

    public Color getButtonBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 51, 0) :       // #003300 - темно-зеленый (Matrix)
            new Color(99, 102, 241);    // #6366f1 - индиго-500 (Midnight)
    }

    public Color getButtonHoverBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый (Matrix)
            new Color(79, 70, 229);     // #4f46e5 - индиго-600 (Midnight)
    }
    
    public Color getTextColor() {
        return getPrimaryColor();
    }
    
    public Color getDimmedTextColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 102, 0) :      // #006600 - приглушенный зеленый
            new Color(108, 112, 134);   // #6c7086 - приглушенный серый
    }
    
    public Color getBorderColor() {
        return getPrimaryColor();
    }
    
    public Color getErrorColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(255, 0, 0) :      // #ff0000 - красный
            new Color(243, 139, 168);   // #f38ba8 - розовый
    }
    
    public Color getSuccessColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 255, 0) :      // #00ff00 - зеленый
            new Color(166, 227, 161);   // #a6e3a1 - светло-зеленый
    }
    
    // ==================== ПРИМЕНЕНИЕ ТЕМ К КОМПОНЕНТАМ ====================
    
    public void applyThemeToFrame(JFrame frame) {
        frame.getContentPane().setBackground(getBackgroundColor());
        frame.setForeground(getTextColor());
    }
    
    public void applyThemeToPanel(JPanel panel) {
        panel.setBackground(getPanelBackgroundColor());
        panel.setForeground(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            panel.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        } else {
            // Midnight - современный стиль с закругленными углами
            panel.setBorder(new RoundedBorder(20));
        }
    }
    
    public void applyThemeToLabel(JLabel label) {
        label.setForeground(getTextColor());
        label.setOpaque(false);
    }
    
    public void applyThemeToTitleLabel(JLabel label) {
        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            label.setForeground(getPrimaryColor());
            label.setFont(new Font("Consolas", Font.BOLD, 24));
        } else {
            // Midnight - Современные заголовки в стиле AI сайтов
            label.setFont(new Font("Segoe UI", Font.BOLD, 32));
            label.setForeground(getPrimaryColor());
        }
        label.setOpaque(false);
    }

    public void applyThemeToSubtitleLabel(JLabel label) {
        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            label.setForeground(getSecondaryColor());
            label.setFont(new Font("Consolas", Font.BOLD, 14));
        } else {
            // Midnight - Современные подзаголовки
            label.setForeground(getSecondaryColor());
            label.setFont(new Font("Segoe UI", Font.PLAIN, 16));
        }
        label.setOpaque(false);
    }
    
    public void applyThemeToButton(JButton button) {
        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            button.setBackground(getButtonBackgroundColor());
            button.setForeground(getTextColor());
            button.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            button.setFont(new Font("Consolas", Font.BOLD, 12));
            button.setFocusPainted(false);
            button.setContentAreaFilled(true);
            button.setOpaque(true);
        } else {
            // Midnight - Современные чистые кнопки в стиле AI сайтов
            button.setFont(new Font("Segoe UI", Font.PLAIN, 14));
            button.setForeground(Color.WHITE);
            button.setFocusPainted(false);
            button.setBorderPainted(false);
            button.setContentAreaFilled(false);
            button.setOpaque(false);
            button.setPreferredSize(new Dimension(140, 44));

            // Создаем современную кнопку
            button.setUI(new ModernButtonUI());
        }

        // Добавляем эффекты наведения
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                if (currentTheme == Theme.MATRIX) {
                    button.setBackground(getButtonHoverBackgroundColor());
                    button.setForeground(Color.BLACK);
                } else {
                    button.repaint(); // Для градиентных кнопок
                }
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                if (currentTheme == Theme.MATRIX) {
                    button.setBackground(getButtonBackgroundColor());
                    button.setForeground(getTextColor());
                } else {
                    button.repaint(); // Для градиентных кнопок
                }
            }
        });
    }
    
    public void applyThemeToTextField(JTextField field) {
        field.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            field.setBackground(getInputBackgroundColor());
            field.setForeground(getTextColor());
            field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            field.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - Современные поля ввода в стиле AI сайтов
            field.setBackground(getInputBackgroundColor());
            field.setForeground(getPrimaryColor());
            field.setFont(new Font("Segoe UI", Font.PLAIN, 14));
            field.setSelectionColor(getAccentColor());
            field.setSelectedTextColor(Color.WHITE);
            field.setCaretColor(getPrimaryColor());
            field.setBorder(BorderFactory.createCompoundBorder(
                new ModernBorder(12),
                BorderFactory.createEmptyBorder(12, 16, 12, 16)
            ));
        }
    }
    
    public void applyThemeToPasswordField(JPasswordField field) {
        field.setBackground(getInputBackgroundColor());
        field.setForeground(getTextColor());
        field.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            field.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - современный стиль
            field.setFont(new Font("Segoe UI", Font.PLAIN, 13));
            field.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(15),
                BorderFactory.createEmptyBorder(8, 15, 8, 15)
            ));
        }
    }
    
    public void applyThemeToTextArea(JTextArea area) {
        area.setForeground(getTextColor());
        area.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            area.setBackground(getTerminalBackgroundColor());
            area.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            area.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - Современная текстовая область в стиле AI сайтов
            area.setBackground(getTerminalBackgroundColor());
            area.setBorder(new ModernBorder(12));
            area.setFont(new Font("Segoe UI", Font.PLAIN, 14));
            area.setForeground(getPrimaryColor());
            area.setSelectionColor(getAccentColor());
            area.setSelectedTextColor(Color.WHITE);
            area.setCaretColor(getPrimaryColor());
        }
    }
    
    public void applyThemeToScrollPane(JScrollPane scrollPane) {
        scrollPane.getViewport().setBackground(getTerminalBackgroundColor());
        scrollPane.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        
        // Стилизуем скроллбары
        JScrollBar vScrollBar = scrollPane.getVerticalScrollBar();
        JScrollBar hScrollBar = scrollPane.getHorizontalScrollBar();
        
        vScrollBar.setBackground(getPanelBackgroundColor());
        vScrollBar.setForeground(getBorderColor());
        
        hScrollBar.setBackground(getPanelBackgroundColor());
        hScrollBar.setForeground(getBorderColor());
    }
    
    /**
     * Применяет тему ко всем компонентам в контейнере рекурсивно
     */
    public void applyThemeToContainer(Container container) {
        for (Component component : container.getComponents()) {
            if (component instanceof JPanel) {
                applyThemeToPanel((JPanel) component);
            } else if (component instanceof JLabel) {
                applyThemeToLabel((JLabel) component);
            } else if (component instanceof JButton) {
                applyThemeToButton((JButton) component);
            } else if (component instanceof JTextField) {
                applyThemeToTextField((JTextField) component);
            } else if (component instanceof JPasswordField) {
                applyThemeToPasswordField((JPasswordField) component);
            } else if (component instanceof JTextArea) {
                applyThemeToTextArea((JTextArea) component);
            } else if (component instanceof JScrollPane) {
                applyThemeToScrollPane((JScrollPane) component);
            }
            
            // Рекурсивно применяем к дочерним контейнерам
            if (component instanceof Container) {
                applyThemeToContainer((Container) component);
            }
        }
    }
    
    /**
     * Создает стилизованную кнопку переключения темы
     */
    public JButton createThemeToggleButton() {
        JButton button = new JButton(getThemeToggleText());
        applyThemeToButton(button);
        
        button.addActionListener(e -> {
            toggleTheme();
            button.setText(getThemeToggleText());
            
            // Находим родительское окно и обновляем тему
            Window window = SwingUtilities.getWindowAncestor(button);
            if (window != null) {
                applyThemeToContainer(window);
                window.repaint();
            }
        });
        
        return button;
    }
    
    private String getThemeToggleText() {
        return currentTheme == Theme.MATRIX ?
            "🌙 Switch to Midnight" :
            "🟢 Switch to Matrix";
    }

    /**
     * Класс для создания круглых границ в Midnight теме
     */
    public static class RoundedBorder implements javax.swing.border.Border {
        private int radius;

        public RoundedBorder(int radius) {
            this.radius = radius;
        }

        @Override
        public Insets getBorderInsets(Component c) {
            return new Insets(radius/2, radius/2, radius/2, radius/2);
        }

        @Override
        public boolean isBorderOpaque() {
            return false;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            SwingThemeManager manager = SwingThemeManager.getInstance();
            if (manager.getCurrentTheme() == Theme.MIDNIGHT) {
                g2d.setColor(manager.getPrimaryColor());
                g2d.setStroke(new BasicStroke(2));
                g2d.drawRoundRect(x + 1, y + 1, width - 3, height - 3, radius, radius);
            } else {
                // Matrix тема - обычная граница
                g2d.setColor(manager.getBorderColor());
                g2d.setStroke(new BasicStroke(2));
                g2d.drawRect(x + 1, y + 1, width - 3, height - 3);
            }

            g2d.dispose();
        }
    }

    /**
     * Современный UI для кнопок в стиле AI сайтов
     */
    public static class ModernButtonUI extends javax.swing.plaf.basic.BasicButtonUI {
        @Override
        public void paint(Graphics g, JComponent c) {
            JButton button = (JButton) c;
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

            int width = button.getWidth();
            int height = button.getHeight();

            SwingThemeManager manager = SwingThemeManager.getInstance();

            // Определяем цвет кнопки
            Color bgColor;
            if (button.getModel().isPressed()) {
                bgColor = new Color(67, 56, 202); // индиго-700
            } else if (button.getModel().isRollover()) {
                bgColor = manager.getButtonHoverBackgroundColor();
            } else {
                bgColor = manager.getButtonBackgroundColor();
            }

            // Рисуем основную кнопку
            g2d.setColor(bgColor);
            g2d.fillRoundRect(0, 0, width, height, 12, 12);

            // Добавляем тонкую границу
            g2d.setColor(new Color(255, 255, 255, 20));
            g2d.setStroke(new BasicStroke(1));
            g2d.drawRoundRect(0, 0, width - 1, height - 1, 12, 12);

            // Добавляем тень при наведении
            if (button.getModel().isRollover()) {
                g2d.setColor(new Color(0, 0, 0, 20));
                g2d.fillRoundRect(0, 2, width, height, 12, 12);
            }

            g2d.dispose();

            // Рисуем текст
            super.paint(g, c);
        }

        @Override
        protected void paintText(Graphics g, JComponent c, Rectangle textRect, String text) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            JButton button = (JButton) c;
            FontMetrics fm = g2d.getFontMetrics();
            int x = (button.getWidth() - fm.stringWidth(text)) / 2;
            int y = (button.getHeight() + fm.getAscent()) / 2 - 2;

            // Простой белый текст
            g2d.setColor(Color.WHITE);
            g2d.drawString(text, x, y);

            g2d.dispose();
        }
    }

    /**
     * Современная граница в стиле AI сайтов
     */
    public static class ModernBorder implements javax.swing.border.Border {
        private int radius;
        private boolean focused = false;

        public ModernBorder(int radius) {
            this.radius = radius;
        }

        @Override
        public Insets getBorderInsets(Component c) {
            return new Insets(2, 2, 2, 2);
        }

        @Override
        public boolean isBorderOpaque() {
            return false;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            SwingThemeManager manager = SwingThemeManager.getInstance();
            boolean hasFocus = c.hasFocus();

            // Простая современная граница
            if (hasFocus) {
                g2d.setColor(manager.getAccentColor());
                g2d.setStroke(new BasicStroke(2));
            } else {
                g2d.setColor(new Color(71, 85, 105)); // slate-600
                g2d.setStroke(new BasicStroke(1));
            }

            g2d.drawRoundRect(x, y, width - 1, height - 1, radius, radius);
            g2d.dispose();
        }
    }


}
