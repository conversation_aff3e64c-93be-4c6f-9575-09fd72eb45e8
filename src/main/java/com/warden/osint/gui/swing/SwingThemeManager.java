package com.warden.osint.gui.swing;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.util.HashMap;
import java.util.Map;

/**
 * Менеджер тем для Swing версии WARDEN
 * Поддерживает Matrix и Midnight темы без JavaFX
 */
public class SwingThemeManager {
    
    private static SwingThemeManager instance;
    private Theme currentTheme = Theme.MATRIX;
    
    public enum Theme {
        MATRIX("Matrix", "🟢 Matrix Theme", "Классическая зеленая тема в стиле хакера"),
        MIDNIGHT("Midnight", "🌙 Midnight Theme", "Элегантная полуночная тема с фиолетово-синими оттенками");
        
        private final String name;
        private final String displayName;
        private final String description;
        
        Theme(String name, String displayName, String description) {
            this.name = name;
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    private SwingThemeManager() {}
    
    public static SwingThemeManager getInstance() {
        if (instance == null) {
            instance = new SwingThemeManager();
        }
        return instance;
    }
    
    public void toggleTheme() {
        currentTheme = (currentTheme == Theme.MATRIX) ? Theme.MIDNIGHT : Theme.MATRIX;
        System.out.println("🎨 Theme switched to: " + currentTheme.getDisplayName());
    }
    
    public void setTheme(Theme theme) {
        if (theme != currentTheme) {
            currentTheme = theme;
            System.out.println("🎨 Theme set to: " + currentTheme.getDisplayName());
        }
    }
    
    public Theme getCurrentTheme() {
        return currentTheme;
    }
    
    // ==================== ЦВЕТОВЫЕ СХЕМЫ ====================
    
    public Color getPrimaryColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый (Matrix)
            new Color(138, 43, 226);    // #8a2be2 - яркий фиолетовый (Midnight)
    }

    public Color getSecondaryColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 204, 255) :    // #00ccff - голубой (Matrix)
            new Color(255, 20, 147);    // #ff1493 - ярко-розовый (Midnight)
    }

    public Color getAccentColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(255, 255, 0) :    // #ffff00 - желтый (Matrix)
            new Color(0, 191, 255);     // #00bfff - неоново-синий (Midnight)
    }
    
    public Color getBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(15, 15, 25);      // #0f0f19 - глубокий темно-синий (Midnight)
    }

    public Color getPanelBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 17, 0) :       // #001100 - темно-зеленый (Matrix)
            new Color(25, 25, 40);      // #191928 - темно-фиолетовый (Midnight)
    }

    public Color getTerminalBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(30, 30, 50);      // #1e1e32 - элегантный темный (Midnight)
    }

    public Color getInputBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 0, 0) :        // #000000 - черный (Matrix)
            new Color(35, 35, 55);      // #232337 - современный темный (Midnight)
    }

    public Color getButtonBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 51, 0) :       // #003300 - темно-зеленый (Matrix)
            new Color(75, 0, 130);      // #4b0082 - индиго (Midnight)
    }

    public Color getButtonHoverBackgroundColor() {
        return currentTheme == Theme.MATRIX ?
            new Color(0, 255, 0) :      // #00ff00 - ярко-зеленый (Matrix)
            new Color(138, 43, 226);    // #8a2be2 - яркий фиолетовый (Midnight)
    }
    
    public Color getTextColor() {
        return getPrimaryColor();
    }
    
    public Color getDimmedTextColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 102, 0) :      // #006600 - приглушенный зеленый
            new Color(108, 112, 134);   // #6c7086 - приглушенный серый
    }
    
    public Color getBorderColor() {
        return getPrimaryColor();
    }
    
    public Color getErrorColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(255, 0, 0) :      // #ff0000 - красный
            new Color(243, 139, 168);   // #f38ba8 - розовый
    }
    
    public Color getSuccessColor() {
        return currentTheme == Theme.MATRIX ? 
            new Color(0, 255, 0) :      // #00ff00 - зеленый
            new Color(166, 227, 161);   // #a6e3a1 - светло-зеленый
    }
    
    // ==================== ПРИМЕНЕНИЕ ТЕМ К КОМПОНЕНТАМ ====================
    
    public void applyThemeToFrame(JFrame frame) {
        frame.getContentPane().setBackground(getBackgroundColor());
        frame.setForeground(getTextColor());
    }
    
    public void applyThemeToPanel(JPanel panel) {
        panel.setBackground(getPanelBackgroundColor());
        panel.setForeground(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            panel.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        } else {
            // Midnight - современный стиль с закругленными углами
            panel.setBorder(new RoundedBorder(20));
        }
    }
    
    public void applyThemeToLabel(JLabel label) {
        label.setForeground(getTextColor());
        label.setOpaque(false);
    }
    
    public void applyThemeToTitleLabel(JLabel label) {
        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            label.setForeground(getPrimaryColor());
            label.setFont(new Font("Consolas", Font.BOLD, 24));
        } else {
            // Midnight - ЭПИЧНЫЕ заголовки с градиентом!
            label.setFont(new Font("Segoe UI", Font.BOLD, 28));
            // Создаем кастомную отрисовку с градиентным текстом
            label.setUI(new GradientLabelUI());
        }
        label.setOpaque(false);
    }

    public void applyThemeToSubtitleLabel(JLabel label) {
        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            label.setForeground(getSecondaryColor());
            label.setFont(new Font("Consolas", Font.BOLD, 14));
        } else {
            // Midnight - стильные подзаголовки
            label.setForeground(new Color(255, 20, 147, 200));
            label.setFont(new Font("Segoe UI", Font.PLAIN, 16));
        }
        label.setOpaque(false);
    }
    
    public void applyThemeToButton(JButton button) {
        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            button.setBackground(getButtonBackgroundColor());
            button.setForeground(getTextColor());
            button.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            button.setFont(new Font("Consolas", Font.BOLD, 12));
            button.setFocusPainted(false);
            button.setContentAreaFilled(true);
            button.setOpaque(true);
        } else {
            // Midnight - КРУТЫЕ градиентные кнопки!
            button.setFont(new Font("Segoe UI", Font.BOLD, 14));
            button.setForeground(Color.WHITE);
            button.setFocusPainted(false);
            button.setBorderPainted(false);
            button.setContentAreaFilled(false);
            button.setOpaque(false);
            button.setPreferredSize(new Dimension(140, 45));

            // Создаем кастомную отрисовку с градиентом
            button.setUI(new GradientButtonUI());
        }

        // Добавляем эффекты наведения
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                if (currentTheme == Theme.MATRIX) {
                    button.setBackground(getButtonHoverBackgroundColor());
                    button.setForeground(Color.BLACK);
                } else {
                    button.repaint(); // Для градиентных кнопок
                }
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                if (currentTheme == Theme.MATRIX) {
                    button.setBackground(getButtonBackgroundColor());
                    button.setForeground(getTextColor());
                } else {
                    button.repaint(); // Для градиентных кнопок
                }
            }
        });
    }
    
    public void applyThemeToTextField(JTextField field) {
        field.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            field.setBackground(getInputBackgroundColor());
            field.setForeground(getTextColor());
            field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            field.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - СТИЛЬНЫЕ поля ввода!
            field.setBackground(new Color(35, 35, 55, 180));
            field.setForeground(new Color(220, 220, 255));
            field.setFont(new Font("Segoe UI", Font.PLAIN, 14));
            field.setSelectionColor(new Color(255, 20, 147, 100));
            field.setSelectedTextColor(Color.WHITE);
            field.setBorder(BorderFactory.createCompoundBorder(
                new GlowBorder(20),
                BorderFactory.createEmptyBorder(10, 20, 10, 20)
            ));
        }
    }
    
    public void applyThemeToPasswordField(JPasswordField field) {
        field.setBackground(getInputBackgroundColor());
        field.setForeground(getTextColor());
        field.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль
            field.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            field.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - современный стиль
            field.setFont(new Font("Segoe UI", Font.PLAIN, 13));
            field.setBorder(BorderFactory.createCompoundBorder(
                new RoundedBorder(15),
                BorderFactory.createEmptyBorder(8, 15, 8, 15)
            ));
        }
    }
    
    public void applyThemeToTextArea(JTextArea area) {
        area.setForeground(getTextColor());
        area.setCaretColor(getTextColor());

        if (currentTheme == Theme.MATRIX) {
            // Matrix - терминальный стиль (не трогаем)
            area.setBackground(getTerminalBackgroundColor());
            area.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
            area.setFont(new Font("Consolas", Font.PLAIN, 12));
        } else {
            // Midnight - КРУТОЙ современный стиль!
            area.setBackground(new Color(30, 30, 50, 200)); // Полупрозрачный фон
            area.setBorder(new RoundedBorder(20));
            area.setFont(new Font("Segoe UI", Font.PLAIN, 14));
            area.setForeground(new Color(220, 220, 255)); // Светло-фиолетовый текст
            area.setSelectionColor(new Color(138, 43, 226, 100));
            area.setSelectedTextColor(Color.WHITE);
        }
    }
    
    public void applyThemeToScrollPane(JScrollPane scrollPane) {
        scrollPane.getViewport().setBackground(getTerminalBackgroundColor());
        scrollPane.setBorder(BorderFactory.createLineBorder(getBorderColor(), 2));
        
        // Стилизуем скроллбары
        JScrollBar vScrollBar = scrollPane.getVerticalScrollBar();
        JScrollBar hScrollBar = scrollPane.getHorizontalScrollBar();
        
        vScrollBar.setBackground(getPanelBackgroundColor());
        vScrollBar.setForeground(getBorderColor());
        
        hScrollBar.setBackground(getPanelBackgroundColor());
        hScrollBar.setForeground(getBorderColor());
    }
    
    /**
     * Применяет тему ко всем компонентам в контейнере рекурсивно
     */
    public void applyThemeToContainer(Container container) {
        for (Component component : container.getComponents()) {
            if (component instanceof JPanel) {
                applyThemeToPanel((JPanel) component);
            } else if (component instanceof JLabel) {
                applyThemeToLabel((JLabel) component);
            } else if (component instanceof JButton) {
                applyThemeToButton((JButton) component);
            } else if (component instanceof JTextField) {
                applyThemeToTextField((JTextField) component);
            } else if (component instanceof JPasswordField) {
                applyThemeToPasswordField((JPasswordField) component);
            } else if (component instanceof JTextArea) {
                applyThemeToTextArea((JTextArea) component);
            } else if (component instanceof JScrollPane) {
                applyThemeToScrollPane((JScrollPane) component);
            }
            
            // Рекурсивно применяем к дочерним контейнерам
            if (component instanceof Container) {
                applyThemeToContainer((Container) component);
            }
        }
    }
    
    /**
     * Создает стилизованную кнопку переключения темы
     */
    public JButton createThemeToggleButton() {
        JButton button = new JButton(getThemeToggleText());
        applyThemeToButton(button);
        
        button.addActionListener(e -> {
            toggleTheme();
            button.setText(getThemeToggleText());
            
            // Находим родительское окно и обновляем тему
            Window window = SwingUtilities.getWindowAncestor(button);
            if (window != null) {
                applyThemeToContainer(window);
                window.repaint();
            }
        });
        
        return button;
    }
    
    private String getThemeToggleText() {
        return currentTheme == Theme.MATRIX ?
            "🌙 Switch to Midnight" :
            "🟢 Switch to Matrix";
    }

    /**
     * Класс для создания круглых границ в Midnight теме
     */
    public static class RoundedBorder implements javax.swing.border.Border {
        private int radius;

        public RoundedBorder(int radius) {
            this.radius = radius;
        }

        @Override
        public Insets getBorderInsets(Component c) {
            return new Insets(radius/2, radius/2, radius/2, radius/2);
        }

        @Override
        public boolean isBorderOpaque() {
            return false;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            SwingThemeManager manager = SwingThemeManager.getInstance();
            if (manager.getCurrentTheme() == Theme.MIDNIGHT) {
                g2d.setColor(manager.getPrimaryColor());
                g2d.setStroke(new BasicStroke(2));
                g2d.drawRoundRect(x + 1, y + 1, width - 3, height - 3, radius, radius);
            } else {
                // Matrix тема - обычная граница
                g2d.setColor(manager.getBorderColor());
                g2d.setStroke(new BasicStroke(2));
                g2d.drawRect(x + 1, y + 1, width - 3, height - 3);
            }

            g2d.dispose();
        }
    }

    /**
     * Кастомный UI для градиентных кнопок в Midnight теме
     */
    public static class GradientButtonUI extends javax.swing.plaf.basic.BasicButtonUI {

        @Override
        public void paint(Graphics g, JComponent c) {
            JButton button = (JButton) c;
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            int width = button.getWidth();
            int height = button.getHeight();

            // Создаем градиент
            Color color1, color2;
            if (button.getModel().isPressed()) {
                color1 = new Color(138, 43, 226, 200); // Темнее при нажатии
                color2 = new Color(75, 0, 130, 200);
            } else if (button.getModel().isRollover()) {
                color1 = new Color(255, 20, 147, 180); // Розовый при наведении
                color2 = new Color(138, 43, 226, 180);
            } else {
                color1 = new Color(138, 43, 226, 150); // Обычное состояние
                color2 = new Color(75, 0, 130, 150);
            }

            GradientPaint gradient = new GradientPaint(0, 0, color1, 0, height, color2);
            g2d.setPaint(gradient);

            // Рисуем закругленный прямоугольник
            g2d.fillRoundRect(0, 0, width, height, 25, 25);

            // Добавляем неоновое свечение
            if (button.getModel().isRollover()) {
                g2d.setColor(new Color(255, 20, 147, 100));
                g2d.setStroke(new BasicStroke(3));
                g2d.drawRoundRect(1, 1, width - 3, height - 3, 25, 25);
            }

            // Рисуем границу
            g2d.setColor(new Color(255, 255, 255, 100));
            g2d.setStroke(new BasicStroke(1));
            g2d.drawRoundRect(0, 0, width - 1, height - 1, 25, 25);

            g2d.dispose();

            // Рисуем текст
            super.paint(g, c);
        }

        @Override
        protected void paintText(Graphics g, JComponent c, Rectangle textRect, String text) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            JButton button = (JButton) c;
            FontMetrics fm = g2d.getFontMetrics();

            // Добавляем тень к тексту
            g2d.setColor(new Color(0, 0, 0, 150));
            g2d.drawString(text, textRect.x + 1, textRect.y + fm.getAscent() + 1);

            // Основной текст
            g2d.setColor(Color.WHITE);
            g2d.drawString(text, textRect.x, textRect.y + fm.getAscent());

            g2d.dispose();
        }
    }

    /**
     * Светящаяся граница для Midnight темы
     */
    public static class GlowBorder implements javax.swing.border.Border {
        private int radius;

        public GlowBorder(int radius) {
            this.radius = radius;
        }

        @Override
        public Insets getBorderInsets(Component c) {
            return new Insets(radius/2, radius/2, radius/2, radius/2);
        }

        @Override
        public boolean isBorderOpaque() {
            return false;
        }

        @Override
        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // Создаем эффект свечения
            for (int i = 0; i < 5; i++) {
                g2d.setColor(new Color(138, 43, 226, 50 - i * 10));
                g2d.setStroke(new BasicStroke(3 - i * 0.5f));
                g2d.drawRoundRect(x + i, y + i, width - 2*i - 1, height - 2*i - 1, radius, radius);
            }

            // Основная граница
            g2d.setColor(new Color(255, 20, 147, 150));
            g2d.setStroke(new BasicStroke(1));
            g2d.drawRoundRect(x + 2, y + 2, width - 5, height - 5, radius, radius);

            g2d.dispose();
        }
    }

    /**
     * Кастомный UI для градиентных заголовков в Midnight теме
     */
    public static class GradientLabelUI extends javax.swing.plaf.basic.BasicLabelUI {

        @Override
        public void paint(Graphics g, JComponent c) {
            JLabel label = (JLabel) c;
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            String text = label.getText();
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(text);
            int textHeight = fm.getHeight();

            int x = (label.getWidth() - textWidth) / 2;
            int y = (label.getHeight() + fm.getAscent()) / 2;

            // Создаем градиент для текста
            GradientPaint gradient = new GradientPaint(
                x, y - textHeight, new Color(138, 43, 226),
                x + textWidth, y, new Color(255, 20, 147)
            );

            // Рисуем тень
            g2d.setColor(new Color(0, 0, 0, 100));
            g2d.drawString(text, x + 2, y + 2);

            // Рисуем основной текст с градиентом
            g2d.setPaint(gradient);
            g2d.drawString(text, x, y);

            // Добавляем свечение
            g2d.setColor(new Color(255, 20, 147, 50));
            g2d.setStroke(new BasicStroke(1));
            // Можно добавить дополнительные эффекты здесь

            g2d.dispose();
        }
    }
}
