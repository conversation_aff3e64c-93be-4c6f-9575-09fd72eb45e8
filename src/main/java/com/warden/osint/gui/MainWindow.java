package com.warden.osint.gui;

import com.warden.osint.api.UsersBoxAPI;
import com.warden.osint.api.TelegramOSINT;
import com.warden.osint.api.TelegramSearchResult;
import com.warden.osint.auth.AuthManager;
import com.warden.osint.auth.User;
import com.warden.osint.chat.ChatWindow;
import com.warden.osint.utils.Logger;
import com.warden.osint.utils.Localization;
import com.warden.osint.utils.ThemeManager;
import javafx.animation.*;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.geometry.Rectangle2D;
import javafx.scene.input.ContextMenuEvent;
import javafx.scene.input.MouseButton;
import javafx.scene.input.Clipboard;
import javafx.scene.input.ClipboardContent;
import javafx.scene.control.TextInputDialog;
import javafx.scene.control.DialogPane;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import javafx.util.Duration;

import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.paint.Color;
import javafx.stage.Screen;
import javafx.stage.Stage;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicBoolean;

public class MainWindow {
    
    private final Stage stage;
    private final AuthManager authManager;
    private final UsersBoxAPI usersBoxAPI;
    private final TelegramOSINT telegramOSINT;
    private final User currentUser;
    private final Logger logger;
    private final ThemeManager themeManager;
    
    private VBox root;
    private TextArea terminalArea;
    private TextField inputField;
    private Label statusLabel;
    private Button searchButton;
    private Button adminButton;
    private Button logoutButton;
    private VBox notificationArea;
    private ChatWindow chatWindow;
    
    private enum InputState {
        MENU, PHONE_SEARCH, EMAIL_SEARCH, USERNAME_SEARCH
    }
    
    private InputState currentState = InputState.MENU;
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    
    public MainWindow(Stage stage, AuthManager authManager, User user) {
        this.stage = stage;
        this.authManager = authManager;
        this.currentUser = user;
        this.usersBoxAPI = new UsersBoxAPI();
        this.telegramOSINT = new TelegramOSINT();
        this.logger = Logger.getInstance();
        this.themeManager = ThemeManager.getInstance();
        
        initializeUI();
        setupEventHandlers();
        showWelcomeMessage();
    }
    
    private void initializeUI() {
        root = new VBox(0);
        themeManager.applyThemeToNode(root, "root", false);

        // Создаем верхнюю панель
        createTopPanel();

        // Создаем основную рабочую область
        createMainArea();

        // Создаем нижнюю панель
        createBottomPanel();

        // Адаптивность под разрешение экрана
        Screen screen = Screen.getPrimary();
        Rectangle2D bounds = screen.getVisualBounds();

        double screenWidth = bounds.getWidth();
        double screenHeight = bounds.getHeight();

        // Используем 85% от размера экрана
        double windowWidth = Math.min(screenWidth * 0.85, 1800);
        double windowHeight = Math.min(screenHeight * 0.85, 1200);

        Scene scene = new Scene(root, windowWidth, windowHeight);
        scene.setFill(Color.BLACK);

        // Добавляем горячие клавиши
        setupHotkeys(scene);

        // Добавляем обработчик CSS ошибок
        scene.getStylesheets().clear();

        stage.setScene(scene);
        stage.setTitle("WARDEN v3.0 - " + currentUser.getDisplayName());
        stage.setResizable(true);

        // Адаптивные минимальные размеры
        stage.setMinWidth(Math.min(1200, screenWidth * 0.6));
        stage.setMinHeight(Math.min(800, screenHeight * 0.6));

        // Центрируем окно
        stage.setX((screenWidth - windowWidth) / 2);
        stage.setY((screenHeight - windowHeight) / 2);
    }

    private void createTopPanel() {
        VBox topPanel = new VBox(10);
        topPanel.setPadding(new Insets(20, 20, 10, 20));
        topPanel.setStyle("-fx-background-color: #000000; -fx-border-color: #00ff00; -fx-border-width: 0 0 2 0;");

        // Анимированный заголовок
        VBox animatedTitleBox = createAnimatedTitle();

        // Информация о пользователе
        HBox userInfoBox = new HBox(20);
        userInfoBox.setAlignment(Pos.CENTER_LEFT);

        statusLabel = new Label(String.format("👤 %s | 📧 %s | %s",
                                             currentUser.getDisplayName(),
                                             currentUser.getEmail(),
                                             currentUser.getStatusDisplay()));
        statusLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 14px;" +
            "-fx-background-color: #001100;" +
            "-fx-padding: 8px 12px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;"
        );

        userInfoBox.getChildren().add(statusLabel);

        VBox headerBox = new VBox(5);
        headerBox.setAlignment(Pos.CENTER);
        headerBox.getChildren().addAll(animatedTitleBox, userInfoBox);

        topPanel.getChildren().add(headerBox);
        root.getChildren().add(topPanel);
    }

    private void createMainArea() {
        HBox mainArea = new HBox(15);
        mainArea.setPadding(new Insets(20));
        mainArea.setStyle("-fx-background-color: #0a0a0a;");

        // Левая панель - функции
        VBox leftPanel = createFunctionsPanel();

        // Правая панель - результаты
        VBox rightPanel = createResultsPanel();

        mainArea.getChildren().addAll(leftPanel, rightPanel);
        root.getChildren().add(mainArea);
    }

    private VBox createFunctionsPanel() {
        VBox functionsPanel = new VBox(15);

        // Адаптивная ширина левой панели
        Screen screen = Screen.getPrimary();
        double screenWidth = screen.getVisualBounds().getWidth();
        double panelWidth = Math.max(300, Math.min(400, screenWidth * 0.25));

        functionsPanel.setPrefWidth(panelWidth);
        functionsPanel.setMaxWidth(panelWidth);
        functionsPanel.setStyle(
            "-fx-background-color: #001100;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 10px;" +
            "-fx-background-radius: 10px;" +
            "-fx-padding: 20px;"
        );

        // Заголовок функций
        Label functionsTitle = new Label("🎯 OSINT FUNCTIONS");
        functionsTitle.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 16px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 5, 0, 0, 0);"
        );

        // Поле поиска
        VBox searchBox = new VBox(10);
        Label searchLabel = new Label("🔍 Quick Search:");
        searchLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 14px;" +
            "-fx-font-weight: bold;"
        );

        inputField = new TextField();
        inputField.setPromptText("Enter phone number, email, or username...");
        inputField.setStyle(
            "-fx-background-color: #000000;" +
            "-fx-text-fill: #00ff00;" +
            "-fx-prompt-text-fill: #006600;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 14px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 2px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;" +
            "-fx-padding: 10px;" +
            "-fx-focus-color: #00ccff;" +
            "-fx-faint-focus-color: transparent;"
        );

        searchBox.getChildren().addAll(searchLabel, inputField);

        // Кнопки функций
        VBox buttonsBox = createFunctionButtons();

        functionsPanel.getChildren().addAll(functionsTitle, searchBox, buttonsBox);
        return functionsPanel;
    }

    private VBox createFunctionButtons() {
        VBox buttonsBox = new VBox(12);

        // Поисковые функции
        Label searchTitle = new Label(Localization.get("search_functions"));
        searchTitle.setStyle(
            "-fx-text-fill: #00ccff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;"
        );

        Button phoneButton = createModernButton(Localization.get("phone_lookup"), Localization.get("phone_lookup_desc"), "#001a00", "#00ff00");
        Button emailButton = createModernButton(Localization.get("email_search"), Localization.get("email_search_desc"), "#001a1a", "#00ffff");
        Button telegramButton = createModernButton("📱 Telegram OSINT", "Мощный поиск по Telegram", "#001133", "#0088ff");

        // Документация и обучение
        Label docsTitle = new Label("📚 Documentation:");
        docsTitle.setStyle(
            "-fx-text-fill: #00ccff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;"
        );

        Button manualsButton = createModernButton(Localization.get("manuals"), Localization.get("manuals_desc"), "#1a001a", "#ff00ff");

        // Коммуникация
        Label commTitle = new Label(Localization.get("communication"));
        commTitle.setStyle(
            "-fx-text-fill: #ff00ff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, #ff00ff, 5, 0, 0, 0);"
        );

        Button chatButton = createModernButton(Localization.get("team_chat"), Localization.get("team_chat_desc"), "#1a0a1a", "#ff6600");

        // Системные функции
        Label systemTitle = new Label(Localization.get("system_functions"));
        systemTitle.setStyle(
            "-fx-text-fill: #ffff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, #ffff00, 5, 0, 0, 0);"
        );

        Button clearButton = createModernButton(Localization.get("clear_results"), Localization.get("clear_results_desc"), "#1a1a00", "#ffff00");

        // Кнопка переключения языка
        Button languageButton = createModernButton(Localization.get("language"), Localization.get("language_desc"), "#001a1a", "#00ffff");

        // Кнопка переключения темы
        Button themeButton = createModernButton(Localization.get("theme"), Localization.get("theme_desc"), "#1a001a", "#bb86fc");

        // Админские функции
        VBox adminBox = new VBox(8);
        if (currentUser.isAdmin()) {
            Label adminTitle = new Label(Localization.get("admin_functions"));
            adminTitle.setStyle(
                "-fx-text-fill: #ff6600;" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 12px;" +
                "-fx-font-weight: bold;"
            );

            adminButton = createModernButton(Localization.get("admin_panel"), Localization.get("admin_panel_desc"), "#1a0000", "#ff0000");
            adminBox.getChildren().addAll(adminTitle, adminButton);
        }

        // Кнопка выхода
        logoutButton = createModernButton(Localization.get("logout"), Localization.get("logout_desc"), "#330000", "#ff4444");

        buttonsBox.getChildren().addAll(
            searchTitle, phoneButton, emailButton, telegramButton,
            docsTitle, manualsButton,
            commTitle, chatButton,
            systemTitle, clearButton, languageButton, themeButton,
            adminBox, logoutButton
        );

        return buttonsBox;
    }
    
    private VBox createResultsPanel() {
        VBox resultsPanel = new VBox(10);

        // Адаптивная ширина правой панели
        Screen screen = Screen.getPrimary();
        double screenWidth = screen.getVisualBounds().getWidth();
        double panelWidth = Math.max(600, screenWidth * 0.65);

        resultsPanel.setPrefWidth(panelWidth);
        resultsPanel.setMinWidth(Math.min(600, screenWidth * 0.4));
        resultsPanel.setStyle(
            "-fx-background-color: #001100;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 10px;" +
            "-fx-background-radius: 10px;" +
            "-fx-padding: 20px;"
        );

        // Заголовок результатов
        Label resultsTitle = new Label("📊 SEARCH RESULTS");
        resultsTitle.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 16px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 5, 0, 0, 0);"
        );

        // Область результатов
        terminalArea = new TextArea();
        terminalArea.setEditable(false);
        terminalArea.setWrapText(true);
        terminalArea.setPrefHeight(600); // Увеличиваем высоту
        terminalArea.setMinHeight(500);
        terminalArea.setStyle(
            "-fx-control-inner-background: #000000;" +
            "-fx-background-color: #000000;" +
            "-fx-base: #000000;" + // Принудительно черный фон
            "-fx-background: #000000;" + // Еще один способ
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 14px;" + // Еще больше увеличиваем шрифт
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 2px;" + // Увеличиваем рамку
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;" +
            "-fx-focus-color: #00ccff;" +
            "-fx-faint-focus-color: transparent;"
        );

        // Заставляем TextArea заполнить всё доступное пространство
        VBox.setVgrow(terminalArea, Priority.ALWAYS);

        // Добавляем контекстное меню
        createContextMenu();

        resultsPanel.getChildren().addAll(resultsTitle, terminalArea);
        return resultsPanel;
    }

    private VBox createAnimatedTitle() {
        VBox titleContainer = new VBox(8);
        titleContainer.setAlignment(Pos.CENTER);

        // Главный заголовок
        Label mainTitle = new Label();
        mainTitle.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 32px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 15, 0, 0, 0);"
        );

        // Подзаголовок
        Label subTitle = new Label();
        subTitle.setStyle(
            "-fx-text-fill: #00ccff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 16px;" +
            "-fx-font-weight: bold;"
        );

        // Версия
        Label versionLabel = new Label();
        versionLabel.setStyle(
            "-fx-text-fill: #ffff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 14px;" +
            "-fx-font-weight: bold;"
        );

        titleContainer.getChildren().addAll(mainTitle, subTitle, versionLabel);

        // Запускаем печатную машинку
        startTypewriter(mainTitle, "W A R D E N", 0);

        // Запускаем подзаголовок и версию с задержкой
        Timeline timeline = new Timeline(
            new KeyFrame(Duration.seconds(2), e -> startTypewriter(subTitle, "Intelligence Platform", 0)),
            new KeyFrame(Duration.seconds(4), e -> startTypewriter(versionLabel, "v3.0", 0))
        );
        timeline.play();

        return titleContainer;
    }

    private void startTypewriter(Label label, String text, int index) {
        if (index <= text.length()) {
            String currentText = text.substring(0, index);

            // Добавляем мигающий курсор
            if (index < text.length()) {
                currentText += "█";
            }

            label.setText(currentText);

            // Следующий символ через 80ms
            Timeline nextChar = new Timeline(new KeyFrame(Duration.millis(80), e ->
                startTypewriter(label, text, index + 1)
            ));
            nextChar.play();
        } else {
            // Убираем курсор и добавляем легкое свечение
            label.setText(text);

            FadeTransition glow = new FadeTransition(Duration.seconds(1), label);
            glow.setFromValue(0.7);
            glow.setToValue(1.0);
            glow.setCycleCount(3);
            glow.setAutoReverse(true);
            glow.play();
        }
    }

    private void createBottomPanel() {
        HBox bottomPanel = new HBox(10);
        bottomPanel.setPadding(new Insets(10, 20, 20, 20));
        bottomPanel.setAlignment(Pos.CENTER);
        bottomPanel.setStyle("-fx-background-color: #000000; -fx-border-color: #00ff00; -fx-border-width: 2 0 0 0;");

        Label statusInfo = new Label("🟢 System Online | 🔗 API Connected | ⚡ Ready for Operations");
        statusInfo.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-background-color: #001100;" +
            "-fx-padding: 8px 15px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 15px;" +
            "-fx-background-radius: 15px;"
        );

        bottomPanel.getChildren().add(statusInfo);
        root.getChildren().add(bottomPanel);

        // Создаем область уведомлений
        createNotificationArea();
    }

    private Button createModernButton(String title, String description, String bgColor, String accentColor) {
        VBox buttonContent = new VBox(3);
        buttonContent.setAlignment(Pos.CENTER_LEFT);

        Label titleLabel = new Label(title);
        titleLabel.setStyle(
            "-fx-text-fill: " + accentColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 13px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, " + accentColor + ", 3, 0, 0, 0);"
        );

        Label descLabel = new Label(description);
        String dimmedColor = dimColor(accentColor);
        descLabel.setStyle(
            "-fx-text-fill: " + dimmedColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;"
        );

        buttonContent.getChildren().addAll(titleLabel, descLabel);

        Button button = new Button();
        button.setGraphic(buttonContent);

        // Адаптивная ширина кнопок
        Screen screen = Screen.getPrimary();
        double screenWidth = screen.getVisualBounds().getWidth();
        double buttonWidth = Math.max(250, Math.min(350, screenWidth * 0.22));

        button.setPrefWidth(buttonWidth);
        button.setPrefHeight(50);
        button.setStyle(
            "-fx-background-color: linear-gradient(to bottom, " + bgColor + ", " + darkenColor(bgColor) + ");" +
            "-fx-border-color: " + accentColor + ";" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 8px;" +
            "-fx-background-radius: 8px;" +
            "-fx-cursor: hand;" +
            "-fx-effect: dropshadow(gaussian, " + accentColor + ", 5, 0, 0, 0);"
        );

        // Эффекты при наведении с градиентом
        button.setOnMouseEntered(e -> {
            button.setStyle(
                "-fx-background-color: linear-gradient(to bottom, " + accentColor + ", " + brightenColor(accentColor) + ");" +
                "-fx-border-color: #ffffff;" +
                "-fx-border-width: 2px;" +
                "-fx-border-radius: 8px;" +
                "-fx-background-radius: 8px;" +
                "-fx-cursor: hand;" +
                "-fx-effect: dropshadow(gaussian, " + accentColor + ", 15, 0, 0, 0);" +
                "-fx-scale-x: 1.05;" +
                "-fx-scale-y: 1.05;"
            );
            titleLabel.setStyle(
                "-fx-text-fill: #000000;" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 13px;" +
                "-fx-font-weight: bold;" +
                "-fx-effect: dropshadow(gaussian, #ffffff, 2, 0, 0, 0);"
            );
            descLabel.setStyle(
                "-fx-text-fill: #333333;" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 10px;"
            );
        });

        button.setOnMouseExited(e -> {
            button.setStyle(
                "-fx-background-color: linear-gradient(to bottom, " + bgColor + ", " + darkenColor(bgColor) + ");" +
                "-fx-border-color: " + accentColor + ";" +
                "-fx-border-width: 1px;" +
                "-fx-border-radius: 8px;" +
                "-fx-background-radius: 8px;" +
                "-fx-cursor: hand;" +
                "-fx-effect: dropshadow(gaussian, " + accentColor + ", 5, 0, 0, 0);" +
                "-fx-scale-x: 1.0;" +
                "-fx-scale-y: 1.0;"
            );
            titleLabel.setStyle(
                "-fx-text-fill: " + accentColor + ";" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 13px;" +
                "-fx-font-weight: bold;" +
                "-fx-effect: dropshadow(gaussian, " + accentColor + ", 3, 0, 0, 0);"
            );
            descLabel.setStyle(
                "-fx-text-fill: " + dimmedColor + ";" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 10px;"
            );
        });

        return button;
    }

    private String dimColor(String color) {
        // Делаем цвет более тусклым
        if (color.equals("#00ff00")) return "#00aa00";
        if (color.equals("#00ffff")) return "#00aaaa";
        if (color.equals("#ff00ff")) return "#aa00aa";
        if (color.equals("#ff6600")) return "#aa4400";
        if (color.equals("#ffff00")) return "#aaaa00";
        if (color.equals("#ff0000")) return "#aa0000";
        if (color.equals("#ff4444")) return "#aa2222";
        return color;
    }

    private String darkenColor(String color) {
        // Делаем цвет темнее для градиента
        if (color.equals("#001a00")) return "#000d00";
        if (color.equals("#001a1a")) return "#000d0d";
        if (color.equals("#1a001a")) return "#0d000d";
        if (color.equals("#1a0a1a")) return "#0d050d";
        if (color.equals("#1a1a00")) return "#0d0d00";
        if (color.equals("#1a0000")) return "#0d0000";
        if (color.equals("#330000")) return "#1a0000";
        return color;
    }

    private String brightenColor(String color) {
        // Делаем цвет ярче для эффекта наведения
        if (color.equals("#00ff00")) return "#66ff66";
        if (color.equals("#00ffff")) return "#66ffff";
        if (color.equals("#ff00ff")) return "#ff66ff";
        if (color.equals("#ff6600")) return "#ff9966";
        if (color.equals("#ffff00")) return "#ffff66";
        if (color.equals("#ff0000")) return "#ff6666";
        if (color.equals("#ff4444")) return "#ff7777";
        return color;
    }

    private Button createTerminalButton(String text, String bgColor) {
        Button button = new Button(text);
        button.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 6px 12px;"
        );
        
        button.setOnMouseEntered(e -> button.setStyle(
            "-fx-background-color: #00ff00;" +
            "-fx-text-fill: #000000;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 6px 12px;"
        ));
        
        button.setOnMouseExited(e -> button.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 6px 12px;"
        ));
        
        return button;
    }
    
    private void setupEventHandlers() {
        // Обработка Enter в поле поиска
        inputField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                handleQuickSearch();
            }
        });

        // Получаем кнопки из панели функций
        HBox mainArea = (HBox) root.getChildren().get(1);
        VBox leftPanel = (VBox) mainArea.getChildren().get(0);
        VBox buttonsBox = (VBox) leftPanel.getChildren().get(2);

        // Поисковые кнопки
        Button phoneButton = (Button) buttonsBox.getChildren().get(1);
        Button emailButton = (Button) buttonsBox.getChildren().get(2);

        // Документация
        Button manualsButton = (Button) buttonsBox.getChildren().get(4);

        // Коммуникация
        Button chatButton = (Button) buttonsBox.getChildren().get(6);

        // Системные кнопки
        Button clearButton = (Button) buttonsBox.getChildren().get(9);
        Button languageButton = (Button) buttonsBox.getChildren().get(10);
        Button themeButton = (Button) buttonsBox.getChildren().get(11);

        phoneButton.setOnAction(e -> startPhoneSearch());
        emailButton.setOnAction(e -> startEmailSearch());
        telegramButton.setOnAction(e -> startTelegramSearch());
        manualsButton.setOnAction(e -> openManuals());
        chatButton.setOnAction(e -> openChat());
        clearButton.setOnAction(e -> clearTerminal());
        languageButton.setOnAction(e -> toggleLanguage());
        themeButton.setOnAction(e -> toggleTheme());

        // Админские кнопки
        if (currentUser.isAdmin()) {
            VBox adminBox = (VBox) buttonsBox.getChildren().get(12);
            adminButton = (Button) adminBox.getChildren().get(1);
            adminButton.setOnAction(e -> openAdminPanel());
            logoutButton = (Button) buttonsBox.getChildren().get(13);
        } else {
            logoutButton = (Button) buttonsBox.getChildren().get(12);
        }

        logoutButton.setOnAction(e -> logout());
    }

    private void handleQuickSearch() {
        String query = inputField.getText().trim();
        if (query.isEmpty()) return;

        appendToTerminal("");
        appendToTerminal("🔍 Quick Search: " + query);

        // Автоматическое определение типа поиска
        if (query.matches("^\\+?[0-9\\s\\-\\(\\)]{7,15}$")) {
            // Похоже на номер телефона
            appendToTerminal("📱 Detected phone number format - starting phone search...");
            searchPhone(query);
        } else if (query.contains("@")) {
            // Похоже на email
            appendToTerminal("📧 Detected email format - email search coming soon...");
            startEmailSearch();
        } else {
            // Любой другой запрос - показываем справку
            appendToTerminal("❓ Unknown query format: " + query);
            appendToTerminal("💡 Supported formats:");
            appendToTerminal("  📱 Phone numbers: +1234567890, (*************");
            appendToTerminal("  📧 Email addresses: <EMAIL>");
            appendToTerminal("");
            appendToTerminal("🔍 Or use the function buttons on the left");
        }
    }
    
    private void showWelcomeMessage() {
        appendToTerminal("╔══════════════════════════════════════════════════════════════════════════════╗");
        appendToTerminal("║                              🔍 WARDEN v3.0                                 ║");
        appendToTerminal("║                         Intelligence Platform                               ║");
        appendToTerminal("╚══════════════════════════════════════════════════════════════════════════════╝");
        appendToTerminal("");
        appendToTerminal("🎉 Welcome back, " + currentUser.getDisplayName() + "!");
        appendToTerminal("🔐 Access Level: " + currentUser.getStatusDisplay());
        appendToTerminal("⏰ Session started: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        appendToTerminal("");
        appendToTerminal("🚀 MODERN INTERFACE:");
        appendToTerminal("  ✨ Redesigned for maximum usability");
        appendToTerminal("  🎯 Smart auto-detection of search types");
        appendToTerminal("  ⚡ Lightning-fast search operations");
        appendToTerminal("  🎨 Beautiful terminal-style design");
        appendToTerminal("");
        appendToTerminal("🔍 HOW TO USE:");
        appendToTerminal("  1️⃣ Enter your search query in the Quick Search field");
        appendToTerminal("  2️⃣ Press ENTER or click a function button");
        appendToTerminal("  3️⃣ View results in this panel");
        appendToTerminal("");
        appendToTerminal("📱 AVAILABLE FUNCTIONS:");
        appendToTerminal("  • Phone Numbers    - Full lookup with multiple sources");
        appendToTerminal("  • Email Addresses  - Coming soon with breach detection");
        appendToTerminal("  • Manuals          - Complete training library and guides");
        appendToTerminal("");
        appendToTerminal("💡 SMART FEATURES:");
        appendToTerminal("  🧠 Auto-detects phone numbers and email addresses");
        appendToTerminal("  📖 Comprehensive manual library");
        appendToTerminal("  📊 Detailed results with source information");
        appendToTerminal("  💬 Team chat and collaboration tools");
        if (currentUser.isAdmin()) {
            appendToTerminal("  🎛️ Admin panel for user management");
        }
        appendToTerminal("");
        appendToTerminal("🎯 Ready for operations! Enter your search query above.");
        appendToTerminal("");
    }
    
    private void appendToTerminal(String text) {
        Platform.runLater(() -> {
            terminalArea.appendText(text + "\n");
            terminalArea.setScrollTop(Double.MAX_VALUE);
        });
    }
    
    private void handleInput() {
        if (isProcessing.get()) return;
        
        String input = inputField.getText().trim();
        inputField.clear();
        
        if (input.isEmpty()) return;
        
        appendToTerminal(input);
        
        String[] parts = input.split("\\s+", 2);
        String command = parts[0].toUpperCase();
        String argument = parts.length > 1 ? parts[1] : "";
        
        switch (command) {
            case "PHONE":
                if (!argument.isEmpty()) {
                    searchPhone(argument);
                } else {
                    appendToTerminal("[ERROR] Usage: PHONE <number>");
                    appendToTerminal("warden@osint:~$ ");
                }
                break;
            case "EMAIL":
                appendToTerminal("[INFO] Email search feature coming soon...");
                appendToTerminal("warden@osint:~$ ");
                break;
            case "USERNAME":
                appendToTerminal("[INFO] Username search feature coming soon...");
                appendToTerminal("warden@osint:~$ ");
                break;
            case "API":
                showAPIInfo();
                break;
            case "HELP":
                showWelcomeMessage();
                break;
            case "CLEAR":
                terminalArea.clear();
                appendToTerminal("warden@osint:~$ ");
                break;
            case "ADMIN":
                if (currentUser.isAdmin()) {
                    openAdminPanel();
                } else {
                    appendToTerminal("[ERROR] Access denied. Admin privileges required.");
                    appendToTerminal("warden@osint:~$ ");
                }
                break;
            case "LOGOUT":
                logout();
                break;
            default:
                appendToTerminal("[ERROR] Unknown command: " + command);
                appendToTerminal("Type HELP for available commands.");
                appendToTerminal("warden@osint:~$ ");
        }
    }
    
    private void startPhoneSearch() {
        String currentText = inputField.getText().trim();
        if (!currentText.isEmpty()) {
            // Если в поле уже есть текст, используем его
            appendToTerminal("");
            appendToTerminal("🔍 Starting phone search with: " + currentText);
            showNotification("🔍 " + Localization.get("phone_lookup"), Localization.get("phone_search_started") + ": " + currentText, "info");
            searchPhone(currentText);
        } else {
            // Иначе просим ввести номер
            appendToTerminal("");
            appendToTerminal("📱 PHONE SEARCH MODE ACTIVATED");
            appendToTerminal("💡 Enter a phone number in the search field above and press ENTER");
            appendToTerminal("📝 Supported formats:");
            appendToTerminal("  • +1234567890");
            appendToTerminal("  • +7 (123) 456-78-90");
            appendToTerminal("  • 1234567890");
            appendToTerminal("");
            inputField.setPromptText("Enter phone number (e.g., +1234567890)");
            inputField.requestFocus();
            currentState = InputState.PHONE_SEARCH;
            showNotification("📱 " + Localization.get("phone_lookup"), Localization.get("enter_phone_number"), "info");
        }
    }

    private void startEmailSearch() {
        String currentText = inputField.getText().trim();
        appendToTerminal("");
        appendToTerminal("📧 EMAIL SEARCH MODE");

        if (!currentText.isEmpty() && currentText.contains("@")) {
            appendToTerminal("🔍 Would search for: " + currentText);
        }

        appendToTerminal("🚧 EMAIL SEARCH - COMING SOON!");
        appendToTerminal("");
        appendToTerminal("📋 This feature will include:");
        appendToTerminal("  🔍 Social media account discovery");
        appendToTerminal("  🛡️ Data breach detection");
        appendToTerminal("  📊 Public records search");
        appendToTerminal("  🌐 Domain information lookup");
        appendToTerminal("  📱 Associated phone numbers");
        appendToTerminal("");
        appendToTerminal("💡 Stay tuned for updates!");
        appendToTerminal("");
    }

    private void startTelegramSearch() {
        appendToTerminal("");
        appendToTerminal("📱 ═══════════════════════════════════════════════════════════════");
        appendToTerminal("📱 TELEGRAM OSINT - МОЩНЫЙ ПОИСК ПО TELEGRAM");
        appendToTerminal("📱 ═══════════════════════════════════════════════════════════════");
        appendToTerminal("");

        String input = showInputDialog("Telegram OSINT Search",
            "Введите номер телефона или username для поиска:",
            "+7XXXXXXXXXX или @username");

        if (input != null && !input.trim().isEmpty()) {
            String query = input.trim();

            appendToTerminal("🔍 Начинаю поиск: " + query);
            appendToTerminal("📡 Подключение к Telegram API...");
            appendToTerminal("🔎 Поиск в базах данных...");
            appendToTerminal("📊 Анализ связанных аккаунтов...");
            appendToTerminal("");

            // Асинхронный поиск
            CompletableFuture<TelegramSearchResult> searchFuture;

            if (query.startsWith("@")) {
                searchFuture = telegramOSINT.searchByUsername(query);
            } else {
                searchFuture = telegramOSINT.searchByPhone(query);
            }

            searchFuture.thenAccept(result -> {
                Platform.runLater(() -> {
                    displayTelegramResults(result);
                });
            }).exceptionally(throwable -> {
                Platform.runLater(() -> {
                    appendToTerminal("❌ Ошибка поиска: " + throwable.getMessage());
                    appendToTerminal("");
                    appendToTerminal("warden@osint:~$ ");
                });
                return null;
            });

            logger.logAction(currentUser.getEmail(), "TELEGRAM_SEARCH", "Query: " + query);
        }
    }

    private void displayTelegramResults(TelegramSearchResult result) {
        appendToTerminal("📊 ═══════════════════════════════════════════════════════════════");
        appendToTerminal("📊 РЕЗУЛЬТАТЫ TELEGRAM OSINT");
        appendToTerminal("📊 ═══════════════════════════════════════════════════════════════");
        appendToTerminal("");

        if (!result.isSuccess()) {
            appendToTerminal("❌ Поиск не удался: " + result.getError());
            appendToTerminal("");
            appendToTerminal("warden@osint:~$ ");
            return;
        }

        if (!result.hasResults()) {
            appendToTerminal("🔍 Информация не найдена");
            appendToTerminal("💡 Попробуйте другой номер или username");
            appendToTerminal("");
            appendToTerminal("warden@osint:~$ ");
            return;
        }

        // Информация о пользователе
        if (result.getUserInfo() != null) {
            var userInfo = result.getUserInfo();
            appendToTerminal("👤 ИНФОРМАЦИЯ О ПОЛЬЗОВАТЕЛЕ:");
            appendToTerminal("   📱 Телефон: " + (userInfo.getPhone() != null ? userInfo.getPhone() : "Скрыт"));
            appendToTerminal("   👤 Имя: " + (userInfo.getFirstName() != null ? userInfo.getFirstName() : "Не указано"));
            appendToTerminal("   👤 Фамилия: " + (userInfo.getLastName() != null ? userInfo.getLastName() : "Не указано"));
            appendToTerminal("   🔗 Username: " + (userInfo.getUsername() != null ? userInfo.getUsername() : "Не указан"));
            appendToTerminal("   🆔 User ID: " + (userInfo.getUserId() != null ? userInfo.getUserId() : "Неизвестен"));
            appendToTerminal("   📝 Биография: " + (userInfo.getBio() != null ? userInfo.getBio() : "Не указана"));
            appendToTerminal("   ⏰ Последняя активность: " + (userInfo.getLastSeen() != null ? userInfo.getLastSeen() : "Неизвестно"));
            appendToTerminal("");
        }

        appendToTerminal("✅ Поиск завершен успешно!");
        appendToTerminal("📊 Краткое резюме: " + result.getSummary());
        appendToTerminal("");
        appendToTerminal("warden@osint:~$ ");

        showNotification("📱 Telegram OSINT", "Поиск завершен! Найдена информация.", "success");
    }

    private void openManuals() {
        appendToTerminal("");
        appendToTerminal("╔══════════════════════════════════════════════════════════════════════════════╗");
        appendToTerminal("║                              📖 MANUALS LIBRARY                             ║");
        appendToTerminal("╚══════════════════════════════════════════════════════════════════════════════╝");
        appendToTerminal("");
        appendToTerminal("📚 COMPLETE TRAINING COLLECTION");
        appendToTerminal("");

        appendToTerminal("📖 MANUAL 1: Основы");
        appendToTerminal("Open-Source Intelligence — это сбор данных из открытых источников. Источники:");
        appendToTerminal("• Соцсети (Facebook, VK, LinkedIn)");
        appendToTerminal("• Поисковики (Google Dorks)");
        appendToTerminal("• Онлайн-реестры (гос, судебные)");
        appendToTerminal("• Метаданные файлов и изображений");
        appendToTerminal("");
        appendToTerminal("Полезные сервисы:");
        appendToTerminal("• https://intelx.io");
        appendToTerminal("• https://www.whoxy.com/");
        appendToTerminal("• https://crt.sh/");
        appendToTerminal("");

        appendToTerminal("📖 MANUAL 2: Google Dorks");
        appendToTerminal("Google Dorks — это техника поиска в Google с использованием операторов:");
        appendToTerminal("• site: - поиск по сайту");
        appendToTerminal("• filetype:pdf - поиск файлов типа PDF");
        appendToTerminal("• intitle:\"index of\" - просмотр открытых директорий");
        appendToTerminal("");
        appendToTerminal("Примеры:");
        appendToTerminal("• site:linkedin.com \"CEO Russia\"");
        appendToTerminal("• filetype:xls \"пароли\"");
        appendToTerminal("");

        appendToTerminal("📖 MANUAL 3: Анонимность в интернете");
        appendToTerminal("Для защиты личности специалист должен оставаться анонимным:");
        appendToTerminal("• VPN (NordVPN, Mullvad)");
        appendToTerminal("• Tor Browser");
        appendToTerminal("• Linux-системы (Tails, Whonix)");
        appendToTerminal("• Создание фейковых аккаунтов с фейковыми данными");
        appendToTerminal("");
        appendToTerminal("Не входи со своего основного аккаунта!");
        appendToTerminal("");

        appendToTerminal("📖 MANUAL 4: Поиск людей");
        appendToTerminal("Сбор данных о человеке:");
        appendToTerminal("• Имя + фамилия в соцсетях");
        appendToTerminal("• Телефон: GetContact, Truecaller");
        appendToTerminal("• Email проверка: hunter.io, haveibeenpwned.com");
        appendToTerminal("• Фото через Google Images");
        appendToTerminal("");
        appendToTerminal("Полезные сервисы для ников:");
        appendToTerminal("• namechk.com");
        appendToTerminal("• whatsmyname.app");
        appendToTerminal("");

        appendToTerminal("📖 MANUAL 5: Метаданные и изображения");
        appendToTerminal("Изображения могут содержать GPS, модель камеры, время:");
        appendToTerminal("• Используй ExifTool (https://exiftool.org/)");
        appendToTerminal("• Анализ через https://fotoforensics.com/");
        appendToTerminal("");
        appendToTerminal("Поиск по картинке:");
        appendToTerminal("• Google Images");
        appendToTerminal("• Yandex Images (лучше для СНГ)");
        appendToTerminal("• TinEye");
        appendToTerminal("");
        appendToTerminal("Это помогает найти другие аккаунты или место съёмки.");
        appendToTerminal("");

        appendToTerminal("📞 SUPPORT:");
        appendToTerminal("  📱 Telegram: @InfernoSoulAttack");
        appendToTerminal("  📱 Telegram: @Svuanstvo");
        appendToTerminal("");

        appendToTerminal("🚀 Изучай мануалы и становись профессионалом!");
        appendToTerminal("");
    }

    private void openChat() {
        try {
            if (chatWindow == null) {
                chatWindow = new ChatWindow(currentUser);
            }

            if (!chatWindow.isShowing()) {
                chatWindow.show();
                showNotification("💬 " + Localization.get("team_chat"),
                               "Team chat window opened", "info");
            } else {
                chatWindow.show(); // Выводим на передний план
            }

        } catch (Exception e) {
            Logger.getInstance().error("Failed to open chat: " + e.getMessage());
            showNotification("❌ Error", "Failed to open chat: " + e.getMessage(), "error");

            // Fallback - показываем информацию в терминале
            appendToTerminal("");
            appendToTerminal("💬 TEAM CHAT");
            appendToTerminal("");
            appendToTerminal("❌ Не удалось открыть окно чата: " + e.getMessage());
            appendToTerminal("");
        }
    }

    private void clearTerminal() {
        terminalArea.clear();
        appendToTerminal("🗑️ Results cleared!");
        appendToTerminal("");
        appendToTerminal("💡 Ready for new search operations.");
        appendToTerminal("🔍 Enter your query in the search field above.");
        appendToTerminal("");
    }
    
    private void searchPhone(String phoneNumber) {
        appendToTerminal("");
        appendToTerminal("[SYSTEM] Searching phone number: " + phoneNumber);
        appendToTerminal("[SYSTEM] Connecting to UsersBox API...");
        
        isProcessing.set(true);
        
        usersBoxAPI.searchPhone(phoneNumber).thenAccept(result -> {
            Platform.runLater(() -> {
                isProcessing.set(false);

                appendToTerminal("");
                appendToTerminal(result.getData());
                appendToTerminal("");
                appendToTerminal("warden@osint:~$ ");

                showNotification(Localization.get("search_complete"), Localization.get("search_finished"), "success");
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                isProcessing.set(false);
                appendToTerminal("");
                appendToTerminal("❌ Search failed: " + throwable.getMessage());
                appendToTerminal("");
                appendToTerminal("warden@osint:~$ ");

                showNotification(Localization.get("search_failed"), throwable.getMessage(), "error");
            });
            return null;
        });
    }
    
    private void showAPIInfo() {
        appendToTerminal("");
        appendToTerminal("[SYSTEM] Retrieving API information...");
        
        isProcessing.set(true);
        
        usersBoxAPI.getAPIInfo().thenAccept(result -> {
            Platform.runLater(() -> {
                isProcessing.set(false);
                
                appendToTerminal("");
                appendToTerminal(result.getData());
                appendToTerminal("");
                appendToTerminal("warden@osint:~$ ");
            });
        });
    }
    
    private void openAdminPanel() {
        appendToTerminal("[SYSTEM] Opening admin panel...");
        appendToTerminal("[SYSTEM] Admin privileges verified");

        AdminPanel adminPanel = new AdminPanel(stage, authManager, currentUser);
        adminPanel.show();

        appendToTerminal("warden@osint:~$ ");
    }
    
    private void logout() {
        appendToTerminal("");
        appendToTerminal("[SYSTEM] Logging out...");
        appendToTerminal("[SYSTEM] Session terminated");

        // Закрываем чат если открыт
        if (chatWindow != null) {
            chatWindow.close();
        }

        // Возвращаемся к окну входа
        LoginWindow loginWindow = new LoginWindow(stage, authManager);
        loginWindow.show();
    }
    
    public void show() {
        stage.show();
        inputField.requestFocus();
    }

    private void createNotificationArea() {
        notificationArea = new VBox(5);
        notificationArea.setAlignment(Pos.TOP_RIGHT);
        notificationArea.setPadding(new Insets(10));
        notificationArea.setMaxWidth(350);
        notificationArea.setStyle("-fx-background-color: transparent;");
    }

    private void showNotification(String title, String message, String type) {
        Platform.runLater(() -> {
            VBox notification = new VBox(5);
            notification.setPadding(new Insets(15));
            notification.setMaxWidth(320);

            // Определяем цвет по типу
            String bgColor, borderColor, textColor;
            switch (type.toLowerCase()) {
                case "success":
                    bgColor = "#001a00";
                    borderColor = "#00ff00";
                    textColor = "#00ff00";
                    break;
                case "error":
                    bgColor = "#1a0000";
                    borderColor = "#ff0000";
                    textColor = "#ff0000";
                    break;
                case "warning":
                    bgColor = "#1a1a00";
                    borderColor = "#ffff00";
                    textColor = "#ffff00";
                    break;
                case "info":
                default:
                    bgColor = "#001a1a";
                    borderColor = "#00ffff";
                    textColor = "#00ffff";
                    break;
            }

            notification.setStyle(
                "-fx-background-color: " + bgColor + ";" +
                "-fx-border-color: " + borderColor + ";" +
                "-fx-border-width: 2px;" +
                "-fx-border-radius: 10px;" +
                "-fx-background-radius: 10px;" +
                "-fx-effect: dropshadow(gaussian, " + borderColor + ", 10, 0, 0, 0);"
            );

            // Заголовок уведомления
            Label titleLabel = new Label(title);
            titleLabel.setStyle(
                "-fx-text-fill: " + textColor + ";" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 12px;" +
                "-fx-font-weight: bold;"
            );

            // Сообщение
            Label messageLabel = new Label(message);
            messageLabel.setWrapText(true);
            messageLabel.setStyle(
                "-fx-text-fill: " + dimColor(textColor) + ";" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 10px;"
            );

            notification.getChildren().addAll(titleLabel, messageLabel);

            // Анимация появления
            notification.setOpacity(0);
            notification.setTranslateX(350);

            if (notificationArea.getParent() == null) {
                root.getChildren().add(notificationArea);
                notificationArea.setLayoutX(stage.getWidth() - 370);
                notificationArea.setLayoutY(10);
            }

            notificationArea.getChildren().add(0, notification);

            // Анимация въезда
            Timeline slideIn = new Timeline(
                new KeyFrame(Duration.ZERO,
                    new KeyValue(notification.opacityProperty(), 0),
                    new KeyValue(notification.translateXProperty(), 350)
                ),
                new KeyFrame(Duration.millis(300),
                    new KeyValue(notification.opacityProperty(), 1),
                    new KeyValue(notification.translateXProperty(), 0)
                )
            );

            // Автоматическое исчезновение через 4 секунды
            Timeline autoHide = new Timeline(new KeyFrame(Duration.seconds(4), e -> hideNotification(notification)));

            slideIn.play();
            autoHide.play();

            // Клик для закрытия
            notification.setOnMouseClicked(e -> hideNotification(notification));
        });
    }

    private void hideNotification(VBox notification) {
        Timeline slideOut = new Timeline(
            new KeyFrame(Duration.millis(200),
                new KeyValue(notification.opacityProperty(), 0),
                new KeyValue(notification.translateXProperty(), 350)
            )
        );
        slideOut.setOnFinished(e -> notificationArea.getChildren().remove(notification));
        slideOut.play();
    }

    private void createContextMenu() {
        javafx.scene.control.ContextMenu contextMenu = new javafx.scene.control.ContextMenu();

        // Копировать все
        javafx.scene.control.MenuItem copyAll = new javafx.scene.control.MenuItem("📋 Copy All Results");
        copyAll.setStyle("-fx-text-fill: #00ff00; -fx-background-color: #001100;");
        copyAll.setOnAction(e -> {
            Clipboard clipboard = Clipboard.getSystemClipboard();
            ClipboardContent content = new ClipboardContent();
            content.putString(terminalArea.getText());
            clipboard.setContent(content);
            showNotification("📋 Copied", "All results copied to clipboard", "success");
        });

        // Копировать выделенное
        javafx.scene.control.MenuItem copySelected = new javafx.scene.control.MenuItem("📄 Copy Selected");
        copySelected.setStyle("-fx-text-fill: #00ccff; -fx-background-color: #001100;");
        copySelected.setOnAction(e -> {
            String selectedText = terminalArea.getSelectedText();
            if (selectedText != null && !selectedText.isEmpty()) {
                Clipboard clipboard = Clipboard.getSystemClipboard();
                ClipboardContent content = new ClipboardContent();
                content.putString(selectedText);
                clipboard.setContent(content);
                showNotification("📄 Copied", "Selected text copied to clipboard", "success");
            } else {
                showNotification("⚠️ Warning", "No text selected", "warning");
            }
        });

        // Экспорт в файл
        javafx.scene.control.MenuItem exportToFile = new javafx.scene.control.MenuItem("💾 Export to File");
        exportToFile.setStyle("-fx-text-fill: #ffff00; -fx-background-color: #001100;");
        exportToFile.setOnAction(e -> exportResults());

        // Очистить результаты
        javafx.scene.control.MenuItem clearResults = new javafx.scene.control.MenuItem("🗑️ Clear Results");
        clearResults.setStyle("-fx-text-fill: #ff6600; -fx-background-color: #001100;");
        clearResults.setOnAction(e -> {
            clearTerminal();
            showNotification("🗑️ Cleared", "Results cleared successfully", "info");
        });

        contextMenu.getItems().addAll(copyAll, copySelected,
            new javafx.scene.control.SeparatorMenuItem(),
            exportToFile,
            new javafx.scene.control.SeparatorMenuItem(),
            clearResults);

        // Стилизуем контекстное меню
        contextMenu.setStyle("-fx-background-color: #001100; -fx-border-color: #00ff00; -fx-border-width: 1px;");

        terminalArea.setContextMenu(contextMenu);
    }

    private void exportResults() {
        javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
        fileChooser.setTitle("Export Search Results");
        fileChooser.setInitialFileName("warden_results_" + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss")) + ".txt");

        fileChooser.getExtensionFilters().addAll(
            new javafx.stage.FileChooser.ExtensionFilter("Text Files", "*.txt"),
            new javafx.stage.FileChooser.ExtensionFilter("JSON Files", "*.json"),
            new javafx.stage.FileChooser.ExtensionFilter("All Files", "*.*")
        );

        java.io.File file = fileChooser.showSaveDialog(stage);
        if (file != null) {
            try {
                String content = terminalArea.getText();

                // Добавляем заголовок к экспорту
                String exportContent = "╔══════════════════════════════════════════════════════════════════════════════╗\n" +
                                     "║                        WARDEN v3.0 - SEARCH RESULTS                        ║\n" +
                                     "╚══════════════════════════════════════════════════════════════════════════════╝\n" +
                                     "Export Date: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                                     "User: " + currentUser.getDisplayName() + "\n" +
                                     "═══════════════════════════════════════════════════════════════════════════════\n\n" +
                                     content;

                java.nio.file.Files.write(file.toPath(), exportContent.getBytes());
                showNotification("💾 Export Complete", "Results saved to: " + file.getName(), "success");
            } catch (Exception ex) {
                showNotification("❌ Export Failed", "Error: " + ex.getMessage(), "error");
            }
        }
    }

    private void setupHotkeys(Scene scene) {
        scene.setOnKeyPressed(event -> {
            // Проверяем комбинации клавиш
            if (event.isControlDown()) {
                switch (event.getCode()) {
                    case DIGIT1:
                        startPhoneSearch();
                        showNotification("🔥 Hotkey", "Phone Search activated (Ctrl+1)", "info");
                        event.consume();
                        break;
                    case DIGIT2:
                        startEmailSearch();
                        showNotification("🔥 Hotkey", "Email Search activated (Ctrl+2)", "info");
                        event.consume();
                        break;
                    case M:
                        showManuals();
                        showNotification("🔥 Hotkey", "Manuals opened (Ctrl+M)", "info");
                        event.consume();
                        break;
                    case L:
                        clearTerminal();
                        showNotification("🔥 Hotkey", "Results cleared (Ctrl+L)", "info");
                        event.consume();
                        break;
                    case S:
                        exportResults();
                        showNotification("🔥 Hotkey", "Export dialog opened (Ctrl+S)", "info");
                        event.consume();
                        break;
                    case Q:
                        logout();
                        event.consume();
                        break;
                    default:
                        // Игнорируем остальные комбинации
                        break;
                }
            } else if (event.getCode() == KeyCode.F1) {
                showHelp();
                showNotification("🔥 Hotkey", "Help opened (F1)", "info");
                event.consume();
            } else if (event.getCode() == KeyCode.F5) {
                showAPIInfo();
                showNotification("🔥 Hotkey", "API Info refreshed (F5)", "info");
                event.consume();
            }
        });
    }



    private void showManuals() {
        appendToTerminal("");
        appendToTerminal("📖 MANUALS & DOCUMENTATION");
        appendToTerminal("═══════════════════════════════════════════════════════════════");
        appendToTerminal("");
        appendToTerminal("🔍 SEARCH GUIDES:");
        appendToTerminal("  • Phone Number Investigation Techniques");
        appendToTerminal("  • Email Address OSINT Methods");
        appendToTerminal("  • Social Media Reconnaissance");
        appendToTerminal("  • Advanced Search Operators");
        appendToTerminal("");
        appendToTerminal("⚡ HOTKEYS:");
        appendToTerminal("  • Ctrl+1 - Phone Search");
        appendToTerminal("  • Ctrl+2 - Email Search");
        appendToTerminal("  • Ctrl+M - Manuals");
        appendToTerminal("  • Ctrl+L - Clear Results");
        appendToTerminal("  • Ctrl+S - Export Results");
        appendToTerminal("  • Ctrl+Q - Logout");
        appendToTerminal("  • F1 - Help");
        appendToTerminal("  • F5 - API Info");
        appendToTerminal("");
        appendToTerminal("🎯 Right-click on results for context menu options");
        appendToTerminal("");
        showNotification("📖 Manuals", "Documentation displayed", "info");
    }

    private void showHelp() {
        appendToTerminal("");
        appendToTerminal("❓ WARDEN v3.0 - HELP & COMMANDS");
        appendToTerminal("═══════════════════════════════════════════════════════════════");
        appendToTerminal("");
        appendToTerminal("🔍 SEARCH COMMANDS:");
        appendToTerminal("  PHONE <number>  - Search phone number information");
        appendToTerminal("  EMAIL <address> - Search email address data");
        appendToTerminal("");
        appendToTerminal("📋 SYSTEM COMMANDS:");
        appendToTerminal("  HELP    - Show this help message");
        appendToTerminal("  CLEAR   - Clear the terminal");
        appendToTerminal("  API     - Show API information");
        appendToTerminal("  LOGOUT  - Sign out from application");
        if (currentUser.isAdmin()) {
            appendToTerminal("  ADMIN   - Open admin panel");
        }
        appendToTerminal("");
        appendToTerminal("⚡ HOTKEYS: See Ctrl+M for complete hotkey list");
        appendToTerminal("");
        showNotification("❓ Help", "Command reference displayed", "info");
    }

    private void toggleLanguage() {
        Localization.toggleLanguage();
        showNotification("🌐 " + Localization.get("language"),
                        "Language switched to " + Localization.getLanguageName(), "info");

        // Обновляем заголовок окна
        stage.setTitle(Localization.get("app_title") + " - " + currentUser.getDisplayName());

        // Обновляем только тексты без пересоздания интерфейса
        updateInterfaceTexts();

        showNotification("✅ " + Localization.get("language"),
                       "Interface updated to " + Localization.getLanguageName(), "success");
    }

    private void toggleTheme() {
        themeManager.toggleTheme();

        appendToTerminal("");
        appendToTerminal("🎨 " + Localization.get("theme_switched") + ": " + themeManager.getCurrentTheme().getDisplayName());
        appendToTerminal("✨ Applying new theme to all interface elements...");

        // Применяем новую тему ко всем элементам
        applyThemeToAllElements();

        appendToTerminal("🎉 Theme applied successfully!");
        appendToTerminal("");
        appendToTerminal("warden@osint:~$ ");

        showNotification("🎨 Theme", "Switched to " + themeManager.getCurrentTheme().getDisplayName(), "success");
    }

    private void applyThemeToAllElements() {
        // Применяем тему к основным элементам
        themeManager.applyThemeToNode(root, "root", true);

        // Рекурсивно обновляем все элементы
        updateAllNodesTheme(root);
    }

    private void updateAllNodesTheme(javafx.scene.Node node) {
        // Применяем темы к различным типам элементов
        if (node instanceof VBox) {
            VBox vbox = (VBox) node;
            if (vbox.getStyleClass().contains("panel") ||
                node.getStyle().contains("border-color") ||
                node.getStyle().contains("background-color: #001100")) {
                themeManager.applyThemeToNode(node, "panel", true);
            }
        } else if (node instanceof Label) {
            Label label = (Label) node;
            String text = label.getText();
            if (text.contains("WARDEN") || text.contains("SEARCH RESULTS") || text.contains("FUNCTIONS")) {
                themeManager.applyThemeToNode(node, "title", true);
            } else if (text.contains(":") || text.contains("📧") || text.contains("🔐")) {
                themeManager.applyThemeToNode(node, "subtitle", true);
            } else if (text.contains("ONLINE") || text.contains("STATUS")) {
                themeManager.applyThemeToNode(node, "status", true);
            }
        } else if (node instanceof TextArea) {
            themeManager.applyThemeToNode(node, "terminal", true);
        } else if (node instanceof TextField) {
            themeManager.applyThemeToNode(node, "input", true);
        } else if (node instanceof Button) {
            themeManager.applyThemeToNode(node, "button", true);
        }

        // Рекурсивно обрабатываем дочерние элементы
        if (node instanceof javafx.scene.Parent) {
            javafx.scene.Parent parent = (javafx.scene.Parent) node;
            for (javafx.scene.Node child : parent.getChildrenUnmodifiable()) {
                updateAllNodesTheme(child);
            }
        }
    }

    private void updateInterfaceTexts() {
        try {
            // Безопасно находим и обновляем элементы
            updateElementsRecursively(root);

            // Обновляем заголовок окна
            stage.setTitle(Localization.get("app_title") + " - " + currentUser.getDisplayName());

        } catch (Exception e) {
            Logger.getInstance().error("Failed to update interface texts: " + e.getMessage());
            // В случае ошибки просто перезагружаем интерфейс
            Platform.runLater(() -> {
                String currentTerminalText = terminalArea.getText();
                initializeUI();
                terminalArea.setText(currentTerminalText);
            });
        }
    }

    private void updateElementsRecursively(javafx.scene.Parent parent) {
        for (javafx.scene.Node node : parent.getChildrenUnmodifiable()) {
            if (node instanceof Button) {
                Button button = (Button) node;
                updateButtonByContent(button);
            } else if (node instanceof Label) {
                Label label = (Label) node;
                updateLabelByContent(label);
            } else if (node instanceof javafx.scene.Parent) {
                updateElementsRecursively((javafx.scene.Parent) node);
            }
        }
    }

    private void updateButtonByContent(Button button) {
        if (button.getGraphic() instanceof VBox) {
            VBox content = (VBox) button.getGraphic();
            if (content.getChildren().size() >= 2) {
                Label titleLabel = (Label) content.getChildren().get(0);
                Label descLabel = (Label) content.getChildren().get(1);
                String currentTitle = titleLabel.getText();

                // Определяем кнопку по текущему содержимому и обновляем
                if (currentTitle.contains("Phone") || currentTitle.contains("телефон")) {
                    titleLabel.setText(Localization.get("phone_lookup"));
                    descLabel.setText(Localization.get("phone_lookup_desc"));
                } else if (currentTitle.contains("Email") || currentTitle.contains("email")) {
                    titleLabel.setText(Localization.get("email_search"));
                    descLabel.setText(Localization.get("email_search_desc"));
                } else if (currentTitle.contains("Manual") || currentTitle.contains("Руководств")) {
                    titleLabel.setText(Localization.get("manuals"));
                    descLabel.setText(Localization.get("manuals_desc"));
                } else if (currentTitle.contains("Chat") || currentTitle.contains("чат")) {
                    titleLabel.setText(Localization.get("team_chat"));
                    descLabel.setText(Localization.get("team_chat_desc"));
                } else if (currentTitle.contains("Clear") || currentTitle.contains("Очистить")) {
                    titleLabel.setText(Localization.get("clear_results"));
                    descLabel.setText(Localization.get("clear_results_desc"));
                } else if (currentTitle.contains("Language") || currentTitle.contains("Язык")) {
                    titleLabel.setText(Localization.get("language"));
                    descLabel.setText(Localization.get("language_desc"));
                } else if (currentTitle.contains("Admin") || currentTitle.contains("администратор")) {
                    titleLabel.setText(Localization.get("admin_panel"));
                    descLabel.setText(Localization.get("admin_panel_desc"));
                } else if (currentTitle.contains("Logout") || currentTitle.contains("Выход")) {
                    titleLabel.setText(Localization.get("logout"));
                    descLabel.setText(Localization.get("logout_desc"));
                }
            }
        }
    }

    private void updateLabelByContent(Label label) {
        String currentText = label.getText();

        // Обновляем заголовки секций
        if (currentText.contains("Search Functions") || currentText.contains("Функции поиска")) {
            label.setText(Localization.get("search_functions"));
        } else if (currentText.contains("Documentation") || currentText.contains("Документация")) {
            label.setText("📚 " + Localization.get("manuals") + ":");
        } else if (currentText.contains("Communication") || currentText.contains("Коммуникация")) {
            label.setText(Localization.get("communication"));
        } else if (currentText.contains("System Functions") || currentText.contains("Системные функции")) {
            label.setText(Localization.get("system_functions"));
        } else if (currentText.contains("Admin Functions") || currentText.contains("администратор")) {
            label.setText(Localization.get("admin_functions"));
        } else if (currentText.contains("SEARCH RESULTS") || currentText.contains("РЕЗУЛЬТАТЫ ПОИСКА")) {
            label.setText(Localization.get("search_results"));
        }
    }

    /**
     * Показывает диалог ввода данных
     */
    private String showInputDialog(String title, String message, String defaultValue) {
        TextInputDialog dialog = new TextInputDialog(defaultValue);
        dialog.setTitle(title);
        dialog.setHeaderText(null);
        dialog.setContentText(message);

        // Применяем тему к диалогу
        DialogPane dialogPane = dialog.getDialogPane();
        if (themeManager.getCurrentTheme() == ThemeManager.Theme.MATRIX) {
            dialogPane.setStyle(
                "-fx-background-color: #000000;" +
                "-fx-text-fill: #00ff00;" +
                "-fx-font-family: 'Consolas';"
            );
        } else {
            dialogPane.setStyle(
                "-fx-background-color: #0f172a;" +
                "-fx-text-fill: #ffffff;" +
                "-fx-font-family: 'Segoe UI';"
            );
        }

        Optional<String> result = dialog.showAndWait();
        return result.orElse(null);
    }

}
