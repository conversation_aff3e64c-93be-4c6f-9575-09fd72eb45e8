package com.warden.osint.gui;

import com.warden.osint.auth.AuthManager;
import com.warden.osint.auth.User;
import com.warden.osint.utils.Logger;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.paint.Color;
import javafx.stage.Stage;

public class AdminPanel {
    
    private final Stage stage;
    private final AuthManager authManager;
    private final User currentUser;
    private final Logger logger;
    
    private VBox root;
    private TextArea terminalArea;
    private TextField emailField;
    private Button grantAccessButton;
    private Button blockUserButton;
    private Button refreshButton;
    private ListView<String> usersList;
    
    public AdminPanel(Stage parentStage, AuthManager authManager, User user) {
        this.stage = new Stage();
        this.authManager = authManager;
        this.currentUser = user;
        this.logger = Logger.getInstance();
        
        initializeUI();
        setupEventHandlers();
        showWelcomeMessage();
        
        // Настройка окна
        stage.initOwner(parentStage);
        stage.setTitle("WARDEN v3.0 - Admin Panel");
    }
    
    private void initializeUI() {
        root = new VBox(15);
        root.setAlignment(Pos.TOP_CENTER);
        root.setPadding(new Insets(20));
        root.setStyle("-fx-background-color: #000000;");
        
        // Заголовок
        Label titleLabel = new Label("🔧 WARDEN ADMIN TERMINAL v3.0");
        titleLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 18px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 10, 0, 0, 0);"
        );
        
        // Терминальная область с информацией
        terminalArea = new TextArea();
        terminalArea.setEditable(false);
        terminalArea.setPrefRowCount(8);
        terminalArea.setPrefColumnCount(80);
        terminalArea.setStyle(
            "-fx-control-inner-background: #000000;" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-focus-color: #00ff00;" +
            "-fx-faint-focus-color: transparent;"
        );
        
        // Секция управления доступом
        Label accessLabel = new Label("[ADMIN] USER ACCESS CONTROL:");
        accessLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;"
        );
        
        HBox accessBox = new HBox(10);
        accessBox.setAlignment(Pos.CENTER);
        
        emailField = new TextField();
        emailField.setPromptText("<EMAIL>");
        emailField.setPrefWidth(300);
        emailField.setStyle(
            "-fx-control-inner-background: #000000;" +
            "-fx-text-fill: #00ff00;" +
            "-fx-prompt-text-fill: #006600;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-focus-color: #00ff00;" +
            "-fx-faint-focus-color: transparent;"
        );
        
        grantAccessButton = createTerminalButton("GRANT ACCESS", "#003300");
        blockUserButton = createTerminalButton("BLOCK USER", "#330000");
        
        accessBox.getChildren().addAll(emailField, grantAccessButton, blockUserButton);
        
        // Секция списка пользователей
        Label usersLabel = new Label("[ADMIN] APPROVED USERS DATABASE:");
        usersLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;"
        );
        
        usersList = new ListView<>();
        usersList.setPrefHeight(200);
        usersList.setStyle(
            "-fx-control-inner-background: #000000;" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-focus-color: #00ff00;" +
            "-fx-faint-focus-color: transparent;" +
            "-fx-selection-bar: #003300;" +
            "-fx-selection-bar-non-focused: #002200;"
        );
        
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        
        refreshButton = createTerminalButton("REFRESH DATABASE", "#000033");
        Button revokeButton = createTerminalButton("REVOKE SELECTED", "#330000");
        Button closeButton = createTerminalButton("CLOSE", "#333333");
        
        buttonBox.getChildren().addAll(refreshButton, revokeButton, closeButton);
        
        root.getChildren().addAll(
            titleLabel, 
            terminalArea, 
            accessLabel, 
            accessBox, 
            usersLabel, 
            usersList, 
            buttonBox
        );
        
        Scene scene = new Scene(root, 900, 700);
        scene.setFill(Color.BLACK);
        stage.setScene(scene);
        stage.setResizable(false);
    }
    
    private Button createTerminalButton(String text, String bgColor) {
        Button button = new Button(text);
        button.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 6px 12px;"
        );
        
        button.setOnMouseEntered(e -> button.setStyle(
            "-fx-background-color: #00ff00;" +
            "-fx-text-fill: #000000;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 6px 12px;"
        ));
        
        button.setOnMouseExited(e -> button.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-padding: 6px 12px;"
        ));
        
        return button;
    }
    
    private void setupEventHandlers() {
        grantAccessButton.setOnAction(e -> grantAccess());
        blockUserButton.setOnAction(e -> blockUser());
        refreshButton.setOnAction(e -> refreshUsersList());
        
        // Закрытие окна
        root.getChildren().get(root.getChildren().size() - 1); // buttonBox
        HBox buttonBox = (HBox) root.getChildren().get(root.getChildren().size() - 1);
        Button closeButton = (Button) buttonBox.getChildren().get(2);
        closeButton.setOnAction(e -> stage.close());
    }
    
    private void showWelcomeMessage() {
        appendToTerminal("╔══════════════════════════════════════════════════════════════════════════════╗");
        appendToTerminal("║                        🔧 WARDEN ADMIN TERMINAL v3.0                       ║");
        appendToTerminal("╚══════════════════════════════════════════════════════════════════════════════╝");
        appendToTerminal("");
        appendToTerminal("[SYSTEM] Administrator access granted");
        appendToTerminal("[SYSTEM] Current user: " + currentUser.getEmail());
        appendToTerminal("[SYSTEM] Privileges: FULL ACCESS");
        appendToTerminal("[SYSTEM] Support: @InfernoSoulAttack, @Svuanstvo");
        appendToTerminal("");
        appendToTerminal("[ADMIN] Available commands:");
        appendToTerminal("  • GRANT ACCESS - Approve user by email");
        appendToTerminal("  • BLOCK USER   - Revoke access and block account");
        appendToTerminal("  • VIEW USERS   - Show approved users list");
        
        // Загружаем список пользователей
        refreshUsersList();
    }
    
    private void appendToTerminal(String text) {
        Platform.runLater(() -> {
            terminalArea.appendText(text + "\n");
            terminalArea.setScrollTop(Double.MAX_VALUE);
        });
    }
    
    private void grantAccess() {
        String email = emailField.getText().trim();
        
        if (email.isEmpty()) {
            showAlert("Warning", "Please enter an email address");
            return;
        }
        
        if (!email.contains("@")) {
            showAlert("Warning", "Please enter a valid email address");
            return;
        }
        
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Grant Access");
        confirmation.setHeaderText("Grant access to user:");
        confirmation.setContentText(email + "\n\nThis will allow them to login immediately.");
        
        if (confirmation.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK) {
            // TODO: Реализовать через AuthManager
            logger.info("Admin granting access to: " + email);
            
            showAlert("Access Granted", "✅ Access granted to: " + email + "\n\nUser can now login to the application.");
            emailField.clear();
            
            // Обновляем список через 2 секунды
            Platform.runLater(() -> {
                try {
                    Thread.sleep(2000);
                    refreshUsersList();
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            });
        }
    }
    
    private void blockUser() {
        String email = emailField.getText().trim();
        
        if (email.isEmpty()) {
            showAlert("Warning", "Please enter an email address");
            return;
        }
        
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Block User");
        confirmation.setHeaderText("Block user and revoke access:");
        confirmation.setContentText(email + "\n\nThis will prevent them from logging in.");
        
        if (confirmation.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK) {
            // TODO: Реализовать через AuthManager
            logger.info("Admin blocking user: " + email);
            
            showAlert("User Blocked", "🚫 User blocked: " + email + "\n\nAccess has been revoked.");
            emailField.clear();
            
            // Обновляем список через 2 секунды
            Platform.runLater(() -> {
                try {
                    Thread.sleep(2000);
                    refreshUsersList();
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            });
        }
    }
    
    private void refreshUsersList() {
        // TODO: Получить реальный список пользователей через AuthManager
        usersList.getItems().clear();
        usersList.getItems().addAll(
            "✅ admin (<EMAIL>) | ACTIVE | HWID: 1234ABCD... | Approved: 2025-06-30",
            "✅ testuser (<EMAIL>) | ACTIVE | HWID: 5678EFGH... | Approved: 2025-06-30",
            "🚫 blockeduser (<EMAIL>) | BLOCKED | HWID: 9012IJKL... | Blocked: 2025-06-30"
        );
        
        logger.info("Admin panel: User list refreshed");
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    public void show() {
        stage.show();
    }
}
