package com.warden.osint.chat;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class ChatMessage {
    private String id;
    private String userId;
    private String username;
    private String message;
    private long timestamp;
    private MessageType type;
    private String userRole;
    
    public enum MessageType {
        TEXT,           // 💬 Обычное сообщение
        SYSTEM,         // 📢 Системное уведомление
        WARNING,        // ⚠️ Предупреждение админа
        WELCOME,        // 🎉 Приветствие нового пользователя
        USER_JOIN,      // 👋 Пользователь присоединился
        USER_LEAVE      // 👋 Пользователь покинул чат
    }
    
    // Конструктор по умолчанию для Firebase
    public ChatMessage() {}
    
    public ChatMessage(String userId, String username, String message, MessageType type, String userRole) {
        this.id = generateId();
        this.userId = userId;
        this.username = username;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
        this.type = type;
        this.userRole = userRole;
    }
    
    public ChatMessage(String userId, String username, String message) {
        this(userId, username, message, MessageType.TEXT, "user");
    }
    
    private String generateId() {
        return "msg_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    public String getFormattedTime() {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp),
            java.time.ZoneId.systemDefault()
        );
        return dateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    }
    
    public String getFormattedDateTime() {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp),
            java.time.ZoneId.systemDefault()
        );
        return dateTime.format(DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss"));
    }
    
    public String getUserIcon() {
        if (userRole == null) return "👤";
        
        switch (userRole.toLowerCase()) {
            case "admin":
                return "👑";
            case "moderator":
                return "🛡️";
            case "vip":
                return "⭐";
            default:
                return "👤";
        }
    }
    
    public String getTypeIcon() {
        switch (type) {
            case SYSTEM:
                return "📢";
            case WARNING:
                return "⚠️";
            case WELCOME:
                return "🎉";
            case USER_JOIN:
                return "👋";
            case USER_LEAVE:
                return "👋";
            default:
                return "💬";
        }
    }
    
    public String getUserColor() {
        if (userRole == null) return "#00ccff";

        switch (userRole.toLowerCase()) {
            case "admin":
                return "#ff0000";  // Красный для админа
            case "moderator":
                return "#ff6600";  // Оранжевый для модератора
            case "vip":
                return "#ffff00";  // Желтый для VIP
            case "system":
                return "#ff00ff";  // Фиолетовый для системы
            default:
                return "#00ccff";  // Голубой для обычного пользователя
        }
    }
    
    public boolean isSystemMessage() {
        return type != MessageType.TEXT;
    }
    
    public boolean isFromCurrentUser(String currentUserId) {
        return userId != null && userId.equals(currentUserId);
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public MessageType getType() { return type; }
    public void setType(MessageType type) { this.type = type; }
    
    public String getUserRole() { return userRole; }
    public void setUserRole(String userRole) { this.userRole = userRole; }
    
    @Override
    public String toString() {
        return String.format("[%s] %s %s: %s", 
            getFormattedTime(), 
            getUserIcon(), 
            username, 
            message);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ChatMessage that = (ChatMessage) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
