package com.warden.osint.chat;

import com.warden.osint.auth.User;
import com.warden.osint.utils.Localization;
import javafx.animation.*;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.Duration;

import java.util.Map;
import java.util.function.Consumer;

public class ChatWindow {
    private Stage chatStage;
    private VBox messagesContainer;
    private ScrollPane messagesScrollPane;
    private TextField messageInput;
    private Label onlineUsersLabel;
    private Label connectionStatusLabel;
    private Button sendButton;
    private Button attachButton;
    
    private User currentUser;
    private ChatService chatService;
    private boolean soundEnabled = true;
    private boolean autoScroll = true;
    
    // Слушатели событий
    private Consumer<ChatMessage> messageListener;
    private Consumer<Map<String, User>> userListener;
    
    public ChatWindow(User user) {
        this.currentUser = user;
        this.chatService = ChatService.getInstance();
        initializeWindow();
        setupEventListeners();
        connectToChat();
    }
    
    private void initializeWindow() {
        chatStage = new Stage();
        chatStage.setTitle("💬 WARDEN TEAM CHAT v3.0");
        chatStage.initModality(Modality.NONE);
        chatStage.setResizable(true);
        chatStage.setMinWidth(600);
        chatStage.setMinHeight(400);
        
        // Создаем основной контейнер
        VBox root = new VBox();
        root.setStyle("-fx-background-color: #000000;");
        
        // Создаем компоненты
        createTopPanel(root);
        createMessagesArea(root);
        createInputPanel(root);
        
        // Создаем сцену
        Scene scene = new Scene(root, 800, 600);
        scene.setFill(Color.BLACK);
        
        // Горячие клавиши
        setupHotkeys(scene);
        
        chatStage.setScene(scene);
        
        // Анимация появления окна
        chatStage.setOpacity(0);
        chatStage.show();
        
        Timeline fadeIn = new Timeline(
            new KeyFrame(Duration.ZERO, new KeyValue(chatStage.opacityProperty(), 0)),
            new KeyFrame(Duration.millis(500), new KeyValue(chatStage.opacityProperty(), 1))
        );
        fadeIn.play();
    }
    
    private void createTopPanel(VBox root) {
        // Верхняя панель с информацией
        HBox topPanel = new HBox(10);
        topPanel.setPadding(new Insets(10));
        topPanel.setAlignment(Pos.CENTER_LEFT);
        topPanel.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #001a00, #000d00);" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 0 0 2px 0;"
        );
        
        // Заголовок чата
        Label titleLabel = new Label("💬 КОМАНДНЫЙ ЧАТ WARDEN");
        titleLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 16px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 10, 0, 0, 0);"
        );
        
        // Spacer
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        // Статус подключения
        connectionStatusLabel = new Label("🟢 Подключен");
        connectionStatusLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;"
        );
        
        // Пользователи онлайн
        onlineUsersLabel = new Label("👥 Онлайн: 0");
        onlineUsersLabel.setStyle(
            "-fx-text-fill: #00ccff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;"
        );
        
        // Кнопка настроек
        Button settingsButton = new Button("⚙️");
        settingsButton.setStyle(
            "-fx-background-color: transparent;" +
            "-fx-text-fill: #ffff00;" +
            "-fx-font-size: 14px;" +
            "-fx-cursor: hand;"
        );
        settingsButton.setOnAction(e -> showSettings());
        
        topPanel.getChildren().addAll(titleLabel, spacer, connectionStatusLabel, onlineUsersLabel, settingsButton);
        root.getChildren().add(topPanel);
    }
    
    private void createMessagesArea(VBox root) {
        // Контейнер для сообщений
        messagesContainer = new VBox(5);
        messagesContainer.setPadding(new Insets(10));
        messagesContainer.setStyle("-fx-background-color: #000000;");
        
        // Скролл панель
        messagesScrollPane = new ScrollPane(messagesContainer);
        messagesScrollPane.setFitToWidth(true);
        messagesScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        messagesScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        messagesScrollPane.setStyle(
            "-fx-background: #000000;" +
            "-fx-background-color: #000000;" +
            "-fx-control-inner-background: #000000;"
        );
        
        VBox.setVgrow(messagesScrollPane, Priority.ALWAYS);
        root.getChildren().add(messagesScrollPane);
    }
    
    private void createInputPanel(VBox root) {
        // Нижняя панель с вводом
        HBox inputPanel = new HBox(10);
        inputPanel.setPadding(new Insets(10));
        inputPanel.setAlignment(Pos.CENTER_LEFT);
        inputPanel.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #000d00, #001a00);" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 2px 0 0 0;"
        );
        
        // Кнопка вложений
        attachButton = new Button("📎");
        attachButton.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #1a1a00, #0d0d00);" +
            "-fx-text-fill: #ffff00;" +
            "-fx-font-size: 16px;" +
            "-fx-border-color: #ffff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;" +
            "-fx-cursor: hand;" +
            "-fx-min-width: 40px;" +
            "-fx-min-height: 35px;"
        );
        attachButton.setOnAction(e -> showAttachMenu());
        
        // Поле ввода сообщения
        messageInput = new TextField();
        messageInput.setPromptText("💬 Введите сообщение...");
        messageInput.setStyle(
            "-fx-background-color: #001100;" +
            "-fx-text-fill: #00ff00;" +
            "-fx-prompt-text-fill: #006600;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;" +
            "-fx-padding: 8px;"
        );
        
        // Кнопка отправки
        sendButton = new Button("🚀 Send");
        sendButton.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #003300, #001a00);" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 2px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;" +
            "-fx-cursor: hand;" +
            "-fx-min-width: 80px;" +
            "-fx-min-height: 35px;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 5, 0, 0, 0);"
        );
        
        // Эффекты наведения для кнопки отправки
        sendButton.setOnMouseEntered(e -> {
            sendButton.setStyle(sendButton.getStyle() + 
                "-fx-background-color: linear-gradient(to bottom, #00ff00, #00aa00);" +
                "-fx-text-fill: #000000;" +
                "-fx-effect: dropshadow(gaussian, #00ff00, 15, 0, 0, 0);" +
                "-fx-scale-x: 1.05;" +
                "-fx-scale-y: 1.05;"
            );
        });
        
        sendButton.setOnMouseExited(e -> {
            sendButton.setStyle(
                "-fx-background-color: linear-gradient(to bottom, #003300, #001a00);" +
                "-fx-text-fill: #00ff00;" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-size: 12px;" +
                "-fx-font-weight: bold;" +
                "-fx-border-color: #00ff00;" +
                "-fx-border-width: 2px;" +
                "-fx-border-radius: 5px;" +
                "-fx-background-radius: 5px;" +
                "-fx-cursor: hand;" +
                "-fx-min-width: 80px;" +
                "-fx-min-height: 35px;" +
                "-fx-effect: dropshadow(gaussian, #00ff00, 5, 0, 0, 0);" +
                "-fx-scale-x: 1.0;" +
                "-fx-scale-y: 1.0;"
            );
        });
        
        // Обработчики событий
        sendButton.setOnAction(e -> sendMessage());
        messageInput.setOnKeyPressed(e -> {
            if (e.getCode() == KeyCode.ENTER) {
                sendMessage();
            }
        });
        
        HBox.setHgrow(messageInput, Priority.ALWAYS);
        inputPanel.getChildren().addAll(attachButton, messageInput, sendButton);
        root.getChildren().add(inputPanel);
    }
    
    private void setupHotkeys(Scene scene) {
        scene.setOnKeyPressed(event -> {
            if (event.isControlDown()) {
                switch (event.getCode()) {
                    case W:
                        close();
                        event.consume();
                        break;
                    case L:
                        clearMessages();
                        event.consume();
                        break;
                    case D:
                        toggleSounds();
                        event.consume();
                        break;
                    default:
                        // Игнорируем остальные комбинации
                        break;
                }
            } else if (event.getCode() == KeyCode.ESCAPE) {
                close();
                event.consume();
            } else if (event.getCode() == KeyCode.F1) {
                showHelp();
                event.consume();
            }
        });
    }

    private void setupEventListeners() {
        // Слушатель новых сообщений
        messageListener = message -> {
            Platform.runLater(() -> {
                addMessageToUI(message);

                // Воспроизводим звук если включен
                if (soundEnabled && !message.isFromCurrentUser(currentUser.getEmail())) {
                    ChatUtils.playNotificationSound(message.getType());
                }

                // Автоскролл если включен
                if (autoScroll) {
                    scrollToBottom();
                }
            });
        };

        // Слушатель изменений пользователей
        userListener = users -> {
            Platform.runLater(() -> updateOnlineUsers(users));
        };
    }

    private void connectToChat() {
        try {
            chatService.addMessageListener(messageListener);
            chatService.addUserListener(userListener);
            chatService.connect(currentUser);

            connectionStatusLabel.setText("🟢 Подключен");
            connectionStatusLabel.setStyle(connectionStatusLabel.getStyle().replace("#ff0000", "#00ff00"));

            // Загружаем историю сообщений
            loadMessageHistory();

        } catch (Exception e) {
            connectionStatusLabel.setText("🔴 Ошибка подключения");
            connectionStatusLabel.setStyle(connectionStatusLabel.getStyle().replace("#00ff00", "#ff0000"));
            showError("Не удалось подключиться к чату: " + e.getMessage());
        }
    }

    private void loadMessageHistory() {
        for (ChatMessage message : chatService.getMessageHistory()) {
            addMessageToUI(message);
        }
        scrollToBottom();
    }

    private void addMessageToUI(ChatMessage message) {
        VBox messageNode = MessageRenderer.createMessageNode(message, currentUser.getEmail());
        messagesContainer.getChildren().add(messageNode);

        // Ограничиваем количество сообщений в UI
        if (messagesContainer.getChildren().size() > 100) {
            messagesContainer.getChildren().remove(0);
        }
    }

    private void sendMessage() {
        String messageText = messageInput.getText().trim();
        if (messageText.isEmpty()) {
            return;
        }

        // Проверяем команды
        if (ChatUtils.isCommand(messageText)) {
            handleCommand(messageText);
            // Скроллим вниз после команды
            scrollToBottom();
        } else {
            // Отправляем обычное сообщение
            if (chatService.sendMessage(messageText)) {
                messageInput.clear();
                messageInput.requestFocus();
                // Принудительно скроллим вниз после отправки
                Platform.runLater(() -> {
                    scrollToBottom();
                });
            }
        }
    }

    private void handleCommand(String commandText) {
        String[] parts = ChatUtils.parseCommand(commandText);
        if (parts.length == 0) {
            return;
        }

        String command = parts[0].toLowerCase();

        switch (command) {
            case "help":
                showCommandHelp();
                break;
            case "clear":
                if (currentUser.isAdmin()) {
                    chatService.sendAdminCommand("clear", "");
                } else {
                    clearMessages();
                }
                break;
            case "users":
                showUsersList();
                break;
            case "ban":
                if (currentUser.isAdmin() && parts.length > 1) {
                    chatService.sendAdminCommand("ban", parts[1]);
                }
                break;
            case "unban":
                if (currentUser.isAdmin() && parts.length > 1) {
                    chatService.sendAdminCommand("unban", parts[1]);
                }
                break;
            case "kick":
                if (currentUser.isAdmin() && parts.length > 1) {
                    chatService.sendAdminCommand("kick", parts[1]);
                }
                break;
            default:
                addSystemMessage("❌ Неизвестная команда: " + command + ". Используйте /help для справки");
                break;
        }

        messageInput.clear();
    }

    private void showCommandHelp() {
        String[] commands = ChatUtils.getAvailableCommands(currentUser.isAdmin());
        StringBuilder help = new StringBuilder("📋 Доступные команды:\n");
        for (String cmd : commands) {
            help.append("  ").append(cmd).append("\n");
        }
        addSystemMessage(help.toString());
    }

    private void showUsersList() {
        Map<String, User> users = chatService.getOnlineUsers();
        StringBuilder usersList = new StringBuilder("👥 Пользователи онлайн (" + users.size() + "):\n");
        for (User user : users.values()) {
            String icon = user.isAdmin() ? "👑" : "👤";
            usersList.append("  ").append(icon).append(" ").append(user.getDisplayName()).append("\n");
        }
        addSystemMessage(usersList.toString());
    }

    private void addSystemMessage(String text) {
        ChatMessage systemMessage = new ChatMessage("system", "СИСТЕМА", text, ChatMessage.MessageType.SYSTEM, "system");
        addMessageToUI(systemMessage);
        scrollToBottom();
    }

    private void updateOnlineUsers(Map<String, User> users) {
        int count = users.size();
        onlineUsersLabel.setText("👥 Онлайн: " + count);

        // Создаем тултип со списком пользователей
        StringBuilder tooltip = new StringBuilder("Пользователи онлайн:\n");
        for (User user : users.values()) {
            String icon = user.isAdmin() ? "👑" : "👤";
            tooltip.append(icon).append(" ").append(user.getDisplayName()).append("\n");
        }

        Tooltip userTooltip = new Tooltip(tooltip.toString());
        userTooltip.setStyle(
            "-fx-background-color: #001100;" +
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;"
        );
        onlineUsersLabel.setTooltip(userTooltip);
    }

    private void scrollToBottom() {
        Platform.runLater(() -> {
            // Принудительно обновляем layout перед скроллом
            messagesContainer.applyCss();
            messagesContainer.layout();

            // Устанавливаем скролл в самый низ
            messagesScrollPane.setVvalue(1.0);

            // Дополнительная проверка через небольшую задержку
            Timeline scrollDelay = new Timeline(new KeyFrame(Duration.millis(50), e -> {
                messagesScrollPane.setVvalue(1.0);
            }));
            scrollDelay.play();
        });
    }

    private void clearMessages() {
        messagesContainer.getChildren().clear();
        addSystemMessage("💬 Локальная история сообщений очищена");
    }

    private void showSettings() {
        Alert settingsDialog = new Alert(Alert.AlertType.INFORMATION);
        settingsDialog.setTitle("⚙️ Настройки чата");
        settingsDialog.setHeaderText("Настройки WARDEN Chat");

        VBox content = new VBox(15);
        content.setPadding(new Insets(15));

        // Звуковые уведомления
        HBox soundBox = new HBox(10);
        soundBox.setAlignment(Pos.CENTER_LEFT);

        CheckBox soundCheckBox = new CheckBox("🔊 Звуковые уведомления");
        soundCheckBox.setSelected(soundEnabled);
        soundCheckBox.setOnAction(e -> {
            soundEnabled = soundCheckBox.isSelected();
            ChatUtils.setSoundsEnabled(soundEnabled);
        });

        Button testSoundButton = new Button("🎵 Тест");
        testSoundButton.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #001a1a, #000d0d);" +
            "-fx-text-fill: #00ffff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;" +
            "-fx-border-color: #00ffff;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 3px;" +
            "-fx-background-radius: 3px;" +
            "-fx-cursor: hand;"
        );
        testSoundButton.setOnAction(e -> {
            ChatUtils.playNotificationSound(ChatMessage.MessageType.TEXT);
        });

        soundBox.getChildren().addAll(soundCheckBox, testSoundButton);

        // Автопрокрутка
        CheckBox autoScrollCheckBox = new CheckBox("⬇️ Автопрокрутка к новым сообщениям");
        autoScrollCheckBox.setSelected(autoScroll);
        autoScrollCheckBox.setOnAction(e -> autoScroll = autoScrollCheckBox.isSelected());

        // Стиль звуков
        VBox soundStyleBox = new VBox(5);
        Label soundStyleLabel = new Label("🎵 Стиль звуков:");
        soundStyleLabel.setStyle("-fx-font-weight: bold;");

        ToggleGroup soundStyleGroup = new ToggleGroup();

        RadioButton softSounds = new RadioButton("🔇 Мягкие (рекомендуется)");
        softSounds.setToggleGroup(soundStyleGroup);
        softSounds.setSelected(ChatUtils.getSoundStyle() == ChatUtils.SoundStyle.SOFT);
        softSounds.setOnAction(e -> ChatUtils.setSoundStyle(ChatUtils.SoundStyle.SOFT));

        RadioButton classicSounds = new RadioButton("🔊 Классические beep");
        classicSounds.setToggleGroup(soundStyleGroup);
        classicSounds.setSelected(ChatUtils.getSoundStyle() == ChatUtils.SoundStyle.CLASSIC);
        classicSounds.setOnAction(e -> ChatUtils.setSoundStyle(ChatUtils.SoundStyle.CLASSIC));

        RadioButton silentSounds = new RadioButton("🔕 Только системный звук");
        silentSounds.setToggleGroup(soundStyleGroup);
        silentSounds.setSelected(ChatUtils.getSoundStyle() == ChatUtils.SoundStyle.SILENT);
        silentSounds.setOnAction(e -> ChatUtils.setSoundStyle(ChatUtils.SoundStyle.SILENT));

        soundStyleBox.getChildren().addAll(soundStyleLabel, softSounds, classicSounds, silentSounds);

        // Тест всех звуков
        VBox soundTestBox = new VBox(5);
        Label soundTestLabel = new Label("🎵 Тест звуков:");
        soundTestLabel.setStyle("-fx-font-weight: bold;");

        HBox soundButtonsBox = new HBox(5);

        Button testMessageSound = new Button("💬 Сообщение");
        testMessageSound.setOnAction(e -> ChatUtils.playNotificationSound(ChatMessage.MessageType.TEXT));

        Button testJoinSound = new Button("👋 Вход");
        testJoinSound.setOnAction(e -> ChatUtils.playNotificationSound(ChatMessage.MessageType.USER_JOIN));

        Button testLeaveSound = new Button("👋 Выход");
        testLeaveSound.setOnAction(e -> ChatUtils.playNotificationSound(ChatMessage.MessageType.USER_LEAVE));

        Button testSystemSound = new Button("📢 Система");
        testSystemSound.setOnAction(e -> ChatUtils.playNotificationSound(ChatMessage.MessageType.SYSTEM));

        Button testWarningSound = new Button("⚠️ Предупреждение");
        testWarningSound.setOnAction(e -> ChatUtils.playNotificationSound(ChatMessage.MessageType.WARNING));

        // Стилизация кнопок тестирования
        String buttonStyle =
            "-fx-background-color: linear-gradient(to bottom, #1a1a00, #0d0d00);" +
            "-fx-text-fill: #ffff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 9px;" +
            "-fx-border-color: #ffff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 3px;" +
            "-fx-background-radius: 3px;" +
            "-fx-cursor: hand;" +
            "-fx-padding: 3px 6px;";

        testMessageSound.setStyle(buttonStyle);
        testJoinSound.setStyle(buttonStyle);
        testLeaveSound.setStyle(buttonStyle);
        testSystemSound.setStyle(buttonStyle);
        testWarningSound.setStyle(buttonStyle);

        soundButtonsBox.getChildren().addAll(testMessageSound, testJoinSound, testLeaveSound, testSystemSound, testWarningSound);
        soundTestBox.getChildren().addAll(soundTestLabel, soundButtonsBox);

        content.getChildren().addAll(soundBox, autoScrollCheckBox, soundStyleBox, soundTestBox);
        settingsDialog.getDialogPane().setContent(content);
        settingsDialog.showAndWait();
    }

    private void showAttachMenu() {
        ContextMenu attachMenu = new ContextMenu();
        attachMenu.setStyle(
            "-fx-background-color: #001100;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 2px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;"
        );

        MenuItem emojiItem = new MenuItem("😊 Эмодзи");
        emojiItem.setStyle("-fx-text-fill: #00ff00; -fx-background-color: #001100;");
        emojiItem.setOnAction(e -> showEmojiPicker());

        MenuItem commandsItem = new MenuItem("⚡ Команды");
        commandsItem.setStyle("-fx-text-fill: #00ffff; -fx-background-color: #001100;");
        commandsItem.setOnAction(e -> showCommandHelp());

        attachMenu.getItems().addAll(emojiItem, commandsItem);

        // Показываем меню над кнопкой
        attachMenu.show(attachButton, 0, -80);
    }

    private void showEmojiPicker() {
        String[] emojis = {
            "😊", "😢", "😃", "😛", "😉", "😮", "😐", "😕", "😘", "❤️",
            "💔", "👍", "👎", "🔥", "🚀", "⭐", "⚠️", "✅", "❌", "❓",
            "💻", "🔒", "🔓", "🔑", "🛡️", "👑", "💀", "👻", "🤖", "🎯"
        };

        Stage emojiStage = new Stage();
        emojiStage.setTitle("😊 Выбор эмодзи");
        emojiStage.initModality(Modality.APPLICATION_MODAL);
        emojiStage.setResizable(false);
        emojiStage.setAlwaysOnTop(true);

        VBox container = new VBox(10);
        container.setPadding(new Insets(15));
        container.setStyle("-fx-background-color: #001100;");

        Label titleLabel = new Label("😊 Выберите эмодзи:");
        titleLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 14px;" +
            "-fx-font-weight: bold;"
        );

        FlowPane emojiPane = new FlowPane(8, 8);
        emojiPane.setPadding(new Insets(10));
        emojiPane.setStyle("-fx-background-color: #000800; -fx-border-color: #00ff00; -fx-border-width: 1px;");

        for (String emoji : emojis) {
            Button emojiButton = new Button(emoji);
            emojiButton.setStyle(
                "-fx-font-size: 18px;" +
                "-fx-background-color: transparent;" +
                "-fx-border-color: transparent;" +
                "-fx-cursor: hand;" +
                "-fx-min-width: 35px;" +
                "-fx-min-height: 35px;"
            );

            // Эффекты наведения
            emojiButton.setOnMouseEntered(e -> {
                emojiButton.setStyle(emojiButton.getStyle() +
                    "-fx-background-color: #002200;" +
                    "-fx-border-color: #00ff00;" +
                    "-fx-border-width: 1px;" +
                    "-fx-border-radius: 5px;" +
                    "-fx-background-radius: 5px;"
                );
            });

            emojiButton.setOnMouseExited(e -> {
                emojiButton.setStyle(
                    "-fx-font-size: 18px;" +
                    "-fx-background-color: transparent;" +
                    "-fx-border-color: transparent;" +
                    "-fx-cursor: hand;" +
                    "-fx-min-width: 35px;" +
                    "-fx-min-height: 35px;"
                );
            });

            emojiButton.setOnAction(e -> {
                messageInput.setText(messageInput.getText() + emoji);
                emojiStage.close();
                messageInput.requestFocus();
                messageInput.positionCaret(messageInput.getText().length());
            });

            emojiPane.getChildren().add(emojiButton);
        }

        Button closeButton = new Button("❌ Закрыть");
        closeButton.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #330000, #1a0000);" +
            "-fx-text-fill: #ff0000;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;" +
            "-fx-border-color: #ff0000;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;" +
            "-fx-cursor: hand;"
        );
        closeButton.setOnAction(e -> emojiStage.close());

        container.getChildren().addAll(titleLabel, emojiPane, closeButton);

        Scene scene = new Scene(container, 350, 280);
        scene.setFill(Color.BLACK);
        emojiStage.setScene(scene);

        // Позиционируем окно рядом с кнопкой
        emojiStage.setX(chatStage.getX() + 100);
        emojiStage.setY(chatStage.getY() + 200);

        emojiStage.show();
    }

    private void showHelp() {
        Alert helpDialog = new Alert(Alert.AlertType.INFORMATION);
        helpDialog.setTitle("❓ Справка по чату");
        helpDialog.setHeaderText("WARDEN Team Chat - Справка");

        String helpText = """
            🔥 Горячие клавиши:
            • Enter - Отправить сообщение
            • Ctrl+W - Закрыть чат
            • Ctrl+L - Очистить локальную историю
            • Ctrl+D - Вкл/выкл звуки
            • Esc - Закрыть чат
            • F1 - Показать эту справку

            💬 Команды чата:
            • /help - Показать команды
            • /users - Список пользователей
            • /clear - Очистить чат (админ)

            🎨 Особенности:
            • Поддержка эмодзи :) :D <3
            • Контекстное меню (ПКМ на сообщении)
            • Цветовое кодирование пользователей
            • Автопрокрутка к новым сообщениям
            """;

        helpDialog.setContentText(helpText);
        helpDialog.showAndWait();
    }

    private void showError(String message) {
        Alert errorAlert = new Alert(Alert.AlertType.ERROR);
        errorAlert.setTitle("❌ Ошибка чата");
        errorAlert.setHeaderText("Произошла ошибка");
        errorAlert.setContentText(message);
        errorAlert.showAndWait();
    }

    private void toggleSounds() {
        soundEnabled = !soundEnabled;
        String status = soundEnabled ? "включены" : "выключены";
        addSystemMessage("🔊 Звуковые уведомления " + status);
    }

    public void show() {
        if (chatStage != null) {
            chatStage.show();
            chatStage.toFront();
            messageInput.requestFocus();
        }
    }

    public void close() {
        if (chatStage != null) {
            // Отключаемся от чата
            if (chatService != null) {
                chatService.removeMessageListener(messageListener);
                chatService.removeUserListener(userListener);
                chatService.disconnect();
            }

            // Анимация закрытия
            Timeline fadeOut = new Timeline(
                new KeyFrame(Duration.ZERO, new KeyValue(chatStage.opacityProperty(), 1)),
                new KeyFrame(Duration.millis(300), new KeyValue(chatStage.opacityProperty(), 0))
            );
            fadeOut.setOnFinished(e -> chatStage.close());
            fadeOut.play();
        }
    }

    public boolean isShowing() {
        return chatStage != null && chatStage.isShowing();
    }
}
