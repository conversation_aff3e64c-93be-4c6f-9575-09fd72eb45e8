package com.warden.osint.chat;

import javafx.animation.*;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.input.Clipboard;
import javafx.scene.input.ClipboardContent;
import javafx.scene.layout.*;
import javafx.util.Duration;

public class MessageRenderer {
    
    public static VBox createMessageNode(ChatMessage message, String currentUserId) {
        VBox messageContainer = new VBox(2);
        messageContainer.setPadding(new Insets(5, 10, 5, 10));
        
        boolean isCurrentUser = message.isFromCurrentUser(currentUserId);
        boolean isSystemMessage = message.isSystemMessage();
        
        if (isSystemMessage) {
            return createSystemMessageNode(message);
        } else if (isCurrentUser) {
            return createOwnMessageNode(message);
        } else {
            return createOtherMessageNode(message);
        }
    }
    
    private static VBox createSystemMessageNode(ChatMessage message) {
        VBox container = new VBox(2);
        container.setAlignment(Pos.CENTER);
        container.setPadding(new Insets(5));
        
        // Системное сообщение
        Label messageLabel = new Label(message.getTypeIcon() + " " + message.getMessage());
        messageLabel.setWrapText(true);
        messageLabel.setMaxWidth(600);
        messageLabel.setStyle(
            "-fx-text-fill: #ffff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;" +
            "-fx-font-style: italic;" +
            "-fx-background-color: #1a1a00;" +
            "-fx-background-radius: 15px;" +
            "-fx-padding: 8px 12px;" +
            "-fx-border-color: #ffff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 15px;" +
            "-fx-effect: dropshadow(gaussian, #ffff00, 5, 0, 0, 0);"
        );
        
        // Время
        Label timeLabel = new Label(message.getFormattedTime());
        timeLabel.setStyle(
            "-fx-text-fill: #666666;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 9px;"
        );
        
        container.getChildren().addAll(messageLabel, timeLabel);
        
        // Анимация появления
        addAppearAnimation(container);
        
        return container;
    }
    
    private static VBox createOwnMessageNode(ChatMessage message) {
        VBox container = new VBox(2);
        container.setAlignment(Pos.CENTER_RIGHT);
        container.setPadding(new Insets(2, 5, 2, 50));

        // Информация о времени, ранге и пользователе (СВЕРХУ)
        HBox infoBox = new HBox(8);
        infoBox.setAlignment(Pos.CENTER_RIGHT);
        infoBox.setPadding(new Insets(2, 0, 2, 0));

        // Время
        Label timeLabel = new Label(message.getFormattedTime());
        timeLabel.setStyle(
            "-fx-text-fill: #888888;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 9px;" +
            "-fx-font-style: italic;"
        );

        // Имя пользователя (свое)
        Label usernameLabel = new Label(message.getUsername());
        usernameLabel.setStyle(
            "-fx-text-fill: " + message.getUserColor() + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 11px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, " + message.getUserColor() + ", 2, 0, 0, 0);"
        );

        // Ранг пользователя (свой)
        Label rankLabel = new Label(getRankDisplayName(message.getUserRole()));
        String rankColor = getRankColor(message.getUserRole());
        rankLabel.setStyle(
            "-fx-text-fill: " + rankColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 9px;" +
            "-fx-font-weight: bold;" +
            "-fx-background-color: " + getRankBackgroundColor(message.getUserRole()) + ";" +
            "-fx-background-radius: 6px;" +
            "-fx-padding: 1px 4px;" +
            "-fx-border-color: " + rankColor + ";" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 6px;" +
            "-fx-effect: dropshadow(gaussian, " + rankColor + ", 2, 0, 0, 0);"
        );

        // Иконка пользователя
        Label userIcon = new Label(message.getUserIcon());
        userIcon.setStyle(
            "-fx-font-size: 14px;" +
            "-fx-effect: dropshadow(gaussian, " + message.getUserColor() + ", 2, 0, 0, 0);"
        );

        infoBox.getChildren().addAll(timeLabel, usernameLabel, rankLabel, userIcon);

        // Контейнер сообщения
        HBox messageBox = new HBox(5);
        messageBox.setAlignment(Pos.CENTER_RIGHT);

        // Текст сообщения
        Label messageLabel = new Label(message.getMessage());
        messageLabel.setWrapText(true);
        messageLabel.setMaxWidth(400);
        messageLabel.setStyle(
            "-fx-text-fill: #ffffff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-background-color: linear-gradient(to bottom, #003300, #001a00);" +
            "-fx-background-radius: 15px 15px 5px 15px;" +
            "-fx-padding: 10px 15px;" +
            "-fx-border-color: #00ff00;" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 15px 15px 5px 15px;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 8, 0, 0, 0);"
        );

        // Статус доставки
        Label statusLabel = new Label("✓");
        statusLabel.setStyle(
            "-fx-text-fill: #00ff00;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;" +
            "-fx-effect: dropshadow(gaussian, #00ff00, 2, 0, 0, 0);"
        );

        messageBox.getChildren().addAll(messageLabel, statusLabel);

        container.getChildren().addAll(infoBox, messageBox);

        // Контекстное меню
        addContextMenu(messageLabel, message);

        // Анимация появления
        addAppearAnimation(container);

        return container;
    }
    
    private static VBox createOtherMessageNode(ChatMessage message) {
        VBox container = new VBox(2);
        container.setAlignment(Pos.CENTER_LEFT);
        container.setPadding(new Insets(2, 50, 2, 5));
        
        // Заголовок с именем пользователя и рангом
        HBox headerBox = new HBox(8);
        headerBox.setAlignment(Pos.CENTER_LEFT);
        headerBox.setPadding(new Insets(2, 0, 2, 0));

        // Иконка пользователя
        Label userIcon = new Label(message.getUserIcon());
        userIcon.setStyle(
            "-fx-font-size: 16px;" +
            "-fx-effect: dropshadow(gaussian, " + message.getUserColor() + ", 3, 0, 0, 0);"
        );

        // Ранг пользователя
        Label rankLabel = new Label(getRankDisplayName(message.getUserRole()));
        String rankColor = getRankColor(message.getUserRole());
        rankLabel.setStyle(
            "-fx-text-fill: " + rankColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 10px;" +
            "-fx-font-weight: bold;" +
            "-fx-background-color: " + getRankBackgroundColor(message.getUserRole()) + ";" +
            "-fx-background-radius: 8px;" +
            "-fx-padding: 2px 6px;" +
            "-fx-border-color: " + rankColor + ";" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 8px;" +
            "-fx-effect: dropshadow(gaussian, " + rankColor + ", 3, 0, 0, 0);"
        );

        // Имя пользователя
        Label usernameLabel = new Label(message.getUsername());
        usernameLabel.setStyle(
            "-fx-text-fill: " + message.getUserColor() + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, " + message.getUserColor() + ", 2, 0, 0, 0);"
        );

        // Spacer
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        // Время
        Label timeLabel = new Label(message.getFormattedTime());
        timeLabel.setStyle(
            "-fx-text-fill: #888888;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 9px;" +
            "-fx-font-style: italic;"
        );

        headerBox.getChildren().addAll(userIcon, rankLabel, usernameLabel, spacer, timeLabel);
        
        // Контейнер сообщения
        HBox messageBox = new HBox(5);
        messageBox.setAlignment(Pos.CENTER_LEFT);
        
        // Текст сообщения
        Label messageLabel = new Label(message.getMessage());
        messageLabel.setWrapText(true);
        messageLabel.setMaxWidth(400);
        
        String bgColor = message.getUserRole().equals("admin") ? "#1a0000" : "#001a1a";
        String borderColor = message.getUserColor();
        
        messageLabel.setStyle(
            "-fx-text-fill: #ffffff;" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 12px;" +
            "-fx-background-color: linear-gradient(to bottom, " + bgColor + ", " + darkenColor(bgColor) + ");" +
            "-fx-background-radius: 15px 15px 15px 5px;" +
            "-fx-padding: 10px 15px;" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 15px 15px 15px 5px;" +
            "-fx-effect: dropshadow(gaussian, " + borderColor + ", 5, 0, 0, 0);"
        );
        
        messageBox.getChildren().add(messageLabel);
        
        container.getChildren().addAll(headerBox, messageBox);
        
        // Контекстное меню
        addContextMenu(messageLabel, message);
        
        // Анимация появления
        addAppearAnimation(container);
        
        return container;
    }
    
    private static void addContextMenu(Label messageLabel, ChatMessage message) {
        ContextMenu contextMenu = new ContextMenu();
        
        // Копировать сообщение
        MenuItem copyItem = new MenuItem("📋 Копировать сообщение");
        copyItem.setStyle("-fx-text-fill: #00ff00; -fx-background-color: #001100;");
        copyItem.setOnAction(e -> {
            Clipboard clipboard = Clipboard.getSystemClipboard();
            ClipboardContent content = new ClipboardContent();
            content.putString(message.getMessage());
            clipboard.setContent(content);
        });
        
        // Копировать с информацией
        MenuItem copyWithInfoItem = new MenuItem("📄 Копировать с информацией");
        copyWithInfoItem.setStyle("-fx-text-fill: #00ccff; -fx-background-color: #001100;");
        copyWithInfoItem.setOnAction(e -> {
            Clipboard clipboard = Clipboard.getSystemClipboard();
            ClipboardContent content = new ClipboardContent();
            content.putString(String.format("[%s] %s %s: %s", 
                message.getFormattedDateTime(),
                message.getUserIcon(),
                message.getUsername(),
                message.getMessage()));
            clipboard.setContent(content);
        });
        
        // Ответить пользователю
        MenuItem replyItem = new MenuItem("↩️ Ответить");
        replyItem.setStyle("-fx-text-fill: #ffff00; -fx-background-color: #001100;");
        replyItem.setOnAction(e -> {
            // Здесь будет логика ответа
            System.out.println("Reply to: " + message.getUsername());
        });
        
        contextMenu.getItems().addAll(copyItem, copyWithInfoItem, replyItem);
        
        // Админские функции
        if (ChatService.getInstance().getCurrentUser() != null && 
            ChatService.getInstance().getCurrentUser().isAdmin() &&
            !message.isSystemMessage()) {
            
            contextMenu.getItems().add(new SeparatorMenuItem());
            
            MenuItem banItem = new MenuItem("🚫 Заблокировать пользователя");
            banItem.setStyle("-fx-text-fill: #ff0000; -fx-background-color: #001100;");
            banItem.setOnAction(e -> {
                ChatService.getInstance().sendAdminCommand("ban", message.getUserId());
            });
            
            contextMenu.getItems().add(banItem);
        }
        
        contextMenu.setStyle("-fx-background-color: #001100; -fx-border-color: #00ff00; -fx-border-width: 1px;");
        messageLabel.setContextMenu(contextMenu);
    }
    
    private static void addAppearAnimation(VBox container) {
        // Начальное состояние
        container.setOpacity(0);
        container.setTranslateY(20);
        container.setScaleX(0.8);
        container.setScaleY(0.8);
        
        // Анимация появления
        Timeline timeline = new Timeline(
            new KeyFrame(Duration.ZERO,
                new KeyValue(container.opacityProperty(), 0),
                new KeyValue(container.translateYProperty(), 20),
                new KeyValue(container.scaleXProperty(), 0.8),
                new KeyValue(container.scaleYProperty(), 0.8)
            ),
            new KeyFrame(Duration.millis(300),
                new KeyValue(container.opacityProperty(), 1),
                new KeyValue(container.translateYProperty(), 0),
                new KeyValue(container.scaleXProperty(), 1.0),
                new KeyValue(container.scaleYProperty(), 1.0)
            )
        );
        
        timeline.play();
        
        // Эффект пульсации для новых сообщений
        if (!container.getChildren().isEmpty()) {
            Timeline pulseTimeline = new Timeline(
                new KeyFrame(Duration.millis(500),
                    new KeyValue(container.scaleXProperty(), 1.05),
                    new KeyValue(container.scaleYProperty(), 1.05)
                ),
                new KeyFrame(Duration.millis(700),
                    new KeyValue(container.scaleXProperty(), 1.0),
                    new KeyValue(container.scaleYProperty(), 1.0)
                )
            );
            pulseTimeline.setDelay(Duration.millis(300));
            pulseTimeline.play();
        }
    }
    
    private static String darkenColor(String color) {
        // Простое затемнение цвета
        if (color.equals("#1a0000")) return "#0d0000";
        if (color.equals("#001a1a")) return "#000d0d";
        if (color.equals("#1a1a00")) return "#0d0d00";
        return color;
    }

    private static String getRankDisplayName(String userRole) {
        if (userRole == null) return "User";

        switch (userRole.toLowerCase()) {
            case "admin":
                return "Админ";
            case "moderator":
                return "Модер";
            case "vip":
                return "VIP";
            case "system":
                return "Система";
            default:
                return "User";
        }
    }

    private static String getRankColor(String userRole) {
        if (userRole == null) return "#00ccff";

        switch (userRole.toLowerCase()) {
            case "admin":
                return "#ff0000";  // Красный для админа
            case "moderator":
                return "#ff6600";  // Оранжевый для модератора
            case "vip":
                return "#ffff00";  // Желтый для VIP
            case "system":
                return "#ff00ff";  // Фиолетовый для системы
            default:
                return "#00ccff";  // Голубой для обычного пользователя
        }
    }

    private static String getRankBackgroundColor(String userRole) {
        if (userRole == null) return "#001a1a";

        switch (userRole.toLowerCase()) {
            case "admin":
                return "#1a0000";  // Темно-красный фон
            case "moderator":
                return "#1a0a00";  // Темно-оранжевый фон
            case "vip":
                return "#1a1a00";  // Темно-желтый фон
            case "system":
                return "#1a001a";  // Темно-фиолетовый фон
            default:
                return "#001a1a";  // Темно-голубой фон
        }
    }
}
