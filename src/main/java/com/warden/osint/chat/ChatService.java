package com.warden.osint.chat;

import com.warden.osint.auth.User;
import com.warden.osint.utils.Logger;
import javafx.application.Platform;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

public class ChatService {
    private static ChatService instance;
    private final Map<String, User> onlineUsers = new ConcurrentHashMap<>();
    private final List<ChatMessage> messageHistory = new CopyOnWriteArrayList<>();
    private final List<Consumer<ChatMessage>> messageListeners = new CopyOnWriteArrayList<>();
    private final List<Consumer<Map<String, User>>> userListeners = new CopyOnWriteArrayList<>();
    private final Set<String> bannedUsers = ConcurrentHashMap.newKeySet();
    
    private User currentUser;
    private boolean connected = false;
    private Timer heartbeatTimer;
    private final int MAX_MESSAGES = 100;
    private final int MESSAGE_RATE_LIMIT = 5; // сообщений в минуту
    private final Map<String, List<Long>> userMessageTimes = new ConcurrentHashMap<>();
    
    private ChatService() {}
    
    public static synchronized ChatService getInstance() {
        if (instance == null) {
            instance = new ChatService();
        }
        return instance;
    }
    
    public void connect(User user) {
        this.currentUser = user;
        this.connected = true;
        
        // Добавляем пользователя в онлайн
        onlineUsers.put(user.getEmail(), user);
        
        // Отправляем приветственное сообщение
        if (messageHistory.isEmpty()) {
            addSystemMessage("🎉 Добро пожаловать в командный чат WARDEN!", ChatMessage.MessageType.WELCOME);

            // Добавляем демонстрационные сообщения
            addDemoMessages();
        }

        // Уведомляем о присоединении пользователя
        addSystemMessage(user.getDisplayName() + " присоединился к чату", ChatMessage.MessageType.USER_JOIN);
        
        // Запускаем heartbeat для поддержания соединения
        startHeartbeat();
        
        // Уведомляем слушателей об обновлении пользователей
        notifyUserListeners();
        
        Logger.getInstance().info("Chat service connected for user: " + user.getDisplayName());
    }
    
    public void disconnect() {
        if (currentUser != null && connected) {
            // Уведомляем о выходе пользователя
            addSystemMessage(currentUser.getDisplayName() + " покинул чат", ChatMessage.MessageType.USER_LEAVE);
            
            // Удаляем из онлайн
            onlineUsers.remove(currentUser.getEmail());
            
            // Останавливаем heartbeat
            if (heartbeatTimer != null) {
                heartbeatTimer.cancel();
                heartbeatTimer = null;
            }
            
            connected = false;
            notifyUserListeners();
            
            Logger.getInstance().info("Chat service disconnected for user: " + currentUser.getDisplayName());
        }
    }
    
    public boolean sendMessage(String messageText) {
        if (!connected || currentUser == null) {
            return false;
        }
        
        // Проверяем бан
        if (bannedUsers.contains(currentUser.getEmail())) {
            addSystemMessage("❌ Вы заблокированы и не можете отправлять сообщения", ChatMessage.MessageType.WARNING);
            return false;
        }
        
        // Проверяем лимит сообщений
        if (!checkRateLimit(currentUser.getEmail())) {
            addSystemMessage("⚠️ Слишком много сообщений! Подождите немного", ChatMessage.MessageType.WARNING);
            return false;
        }
        
        // Фильтруем сообщение
        String filteredMessage = filterMessage(messageText);
        if (filteredMessage.isEmpty()) {
            addSystemMessage("❌ Сообщение содержит запрещенный контент", ChatMessage.MessageType.WARNING);
            return false;
        }
        
        // Создаем и отправляем сообщение
        String userRole = currentUser.isAdmin() ? "admin" : "user";
        ChatMessage message = new ChatMessage(
            currentUser.getEmail(),
            currentUser.getDisplayName(),
            filteredMessage,
            ChatMessage.MessageType.TEXT,
            userRole
        );
        
        addMessage(message);
        return true;
    }
    
    public void sendAdminCommand(String command, String targetUser) {
        if (!currentUser.isAdmin()) {
            return;
        }
        
        switch (command.toLowerCase()) {
            case "ban":
                banUser(targetUser);
                break;
            case "unban":
                unbanUser(targetUser);
                break;
            case "clear":
                clearChat();
                break;
            case "kick":
                kickUser(targetUser);
                break;
        }
    }
    
    private void banUser(String userEmail) {
        bannedUsers.add(userEmail);
        addSystemMessage("👑 Администратор заблокировал пользователя: " + userEmail, ChatMessage.MessageType.WARNING);
    }
    
    private void unbanUser(String userEmail) {
        bannedUsers.remove(userEmail);
        addSystemMessage("👑 Администратор разблокировал пользователя: " + userEmail, ChatMessage.MessageType.SYSTEM);
    }
    
    private void clearChat() {
        messageHistory.clear();
        addSystemMessage("👑 Администратор очистил чат", ChatMessage.MessageType.SYSTEM);
    }
    
    private void kickUser(String userEmail) {
        onlineUsers.remove(userEmail);
        addSystemMessage("👑 Администратор исключил пользователя: " + userEmail, ChatMessage.MessageType.WARNING);
        notifyUserListeners();
    }
    
    private boolean checkRateLimit(String userEmail) {
        long currentTime = System.currentTimeMillis();
        List<Long> userTimes = userMessageTimes.computeIfAbsent(userEmail, k -> new ArrayList<>());
        
        // Удаляем старые записи (старше минуты)
        userTimes.removeIf(time -> currentTime - time > 60000);
        
        if (userTimes.size() >= MESSAGE_RATE_LIMIT) {
            return false;
        }
        
        userTimes.add(currentTime);
        return true;
    }
    
    private String filterMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            return "";
        }
        
        String filtered = message.trim();
        
        // Фильтр ссылок (простой)
        if (filtered.toLowerCase().contains("http://") || 
            filtered.toLowerCase().contains("https://") ||
            filtered.toLowerCase().contains("www.")) {
            
            if (!currentUser.isAdmin()) {
                return ""; // Блокируем ссылки для обычных пользователей
            }
        }
        
        // Фильтр длины
        if (filtered.length() > 500) {
            filtered = filtered.substring(0, 500) + "...";
        }
        
        return filtered;
    }
    
    private void addMessage(ChatMessage message) {
        messageHistory.add(message);
        
        // Ограничиваем историю
        if (messageHistory.size() > MAX_MESSAGES) {
            messageHistory.remove(0);
        }
        
        // Уведомляем слушателей
        Platform.runLater(() -> {
            for (Consumer<ChatMessage> listener : messageListeners) {
                try {
                    listener.accept(message);
                } catch (Exception e) {
                    Logger.getInstance().error("Error notifying message listener: " + e.getMessage());
                }
            }
        });
    }
    
    private void addSystemMessage(String text, ChatMessage.MessageType type) {
        ChatMessage systemMessage = new ChatMessage("system", "СИСТЕМА", text, type, "system");
        addMessage(systemMessage);
    }
    
    private void startHeartbeat() {
        heartbeatTimer = new Timer(true);
        heartbeatTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                if (connected && currentUser != null) {
                    // Обновляем время последней активности
                    onlineUsers.put(currentUser.getEmail(), currentUser);
                }
            }
        }, 0, 30000); // Каждые 30 секунд
    }
    
    private void notifyUserListeners() {
        Platform.runLater(() -> {
            for (Consumer<Map<String, User>> listener : userListeners) {
                try {
                    listener.accept(new HashMap<>(onlineUsers));
                } catch (Exception e) {
                    Logger.getInstance().error("Error notifying user listener: " + e.getMessage());
                }
            }
        });
    }
    
    // Методы для подписки на события
    public void addMessageListener(Consumer<ChatMessage> listener) {
        messageListeners.add(listener);
    }
    
    public void removeMessageListener(Consumer<ChatMessage> listener) {
        messageListeners.remove(listener);
    }
    
    public void addUserListener(Consumer<Map<String, User>> listener) {
        userListeners.add(listener);
    }
    
    public void removeUserListener(Consumer<Map<String, User>> listener) {
        userListeners.remove(listener);
    }
    
    // Getters
    public List<ChatMessage> getMessageHistory() {
        return new ArrayList<>(messageHistory);
    }
    
    public Map<String, User> getOnlineUsers() {
        return new HashMap<>(onlineUsers);
    }
    
    public boolean isConnected() {
        return connected;
    }
    
    public User getCurrentUser() {
        return currentUser;
    }

    private void addDemoMessages() {
        // Демонстрационные сообщения для показа интерфейса

        // Сообщение от админа
        ChatMessage adminMessage = new ChatMessage(
            "<EMAIL>",
            "Администратор",
            "👑 Добро пожаловать в командный чат WARDEN! Здесь мы координируем наши операции.",
            ChatMessage.MessageType.TEXT,
            "admin"
        );
        adminMessage.setTimestamp(System.currentTimeMillis() - 300000); // 5 минут назад
        addMessage(adminMessage);

        // Сообщение от обычного пользователя
        ChatMessage userMessage = new ChatMessage(
            "<EMAIL>",
            "Аналитик_007",
            "Привет всем! 👋 Готов к работе! Есть новые данные по цели.",
            ChatMessage.MessageType.TEXT,
            "user"
        );
        userMessage.setTimestamp(System.currentTimeMillis() - 240000); // 4 минуты назад
        addMessage(userMessage);

        // Еще одно сообщение от админа
        ChatMessage adminMessage2 = new ChatMessage(
            "<EMAIL>",
            "Администратор",
            "Отлично! 🔥 Загружайте данные в общую папку. Начинаем анализ.",
            ChatMessage.MessageType.TEXT,
            "admin"
        );
        adminMessage2.setTimestamp(System.currentTimeMillis() - 180000); // 3 минуты назад
        addMessage(adminMessage2);

        // Сообщение от VIP пользователя
        ChatMessage vipMessage = new ChatMessage(
            "<EMAIL>",
            "Senior_Analyst",
            "⭐ Проверил данные - все выглядит корректно. Можем продолжать операцию.",
            ChatMessage.MessageType.TEXT,
            "vip"
        );
        vipMessage.setTimestamp(System.currentTimeMillis() - 120000); // 2 минуты назад
        addMessage(vipMessage);

        // Системные сообщения
        addSystemMessage("📊 Система: Все пользователи подключены. Канал связи защищен.", ChatMessage.MessageType.SYSTEM);
        addSystemMessage("🔊 Звуки уведомлений настроены в мягком режиме. Можно изменить в настройках ⚙️", ChatMessage.MessageType.SYSTEM);
    }
}
