package com.warden.osint.chat;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import javax.sound.sampled.*;
import java.io.ByteArrayInputStream;

public class ChatUtils {
    
    // Эмодзи мапа для замены текстовых смайликов
    private static final Map<String, String> EMOJI_MAP = new HashMap<>();
    
    // Паттерны для фильтрации
    private static final Pattern URL_PATTERN = Pattern.compile(
        "(?i)\\b(?:https?://|www\\.|[a-z0-9.-]+\\.[a-z]{2,})\\S*",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b"
    );
    
    // Звуки уведомлений (используем системные звуки)
    private static boolean soundsEnabled = true;
    private static SoundStyle soundStyle = SoundStyle.SOFT;

    public enum SoundStyle {
        SOFT,      // Мягкие приятные звуки
        CLASSIC,   // Классические beep звуки
        SILENT     // Только системный beep
    }
    
    static {
        initializeEmojiMap();
        initializeSounds();
    }
    
    private static void initializeEmojiMap() {
        // Основные смайлики
        EMOJI_MAP.put(":)", "😊");
        EMOJI_MAP.put(":(", "😢");
        EMOJI_MAP.put(":D", "😃");
        EMOJI_MAP.put(":P", "😛");
        EMOJI_MAP.put(";)", "😉");
        EMOJI_MAP.put(":o", "😮");
        EMOJI_MAP.put(":|", "😐");
        EMOJI_MAP.put(":/", "😕");
        EMOJI_MAP.put(":*", "😘");
        EMOJI_MAP.put("<3", "❤️");
        EMOJI_MAP.put("</3", "💔");
        
        // Дополнительные эмодзи
        EMOJI_MAP.put(":thumbsup:", "👍");
        EMOJI_MAP.put(":thumbsdown:", "👎");
        EMOJI_MAP.put(":fire:", "🔥");
        EMOJI_MAP.put(":rocket:", "🚀");
        EMOJI_MAP.put(":star:", "⭐");
        EMOJI_MAP.put(":warning:", "⚠️");
        EMOJI_MAP.put(":check:", "✅");
        EMOJI_MAP.put(":cross:", "❌");
        EMOJI_MAP.put(":question:", "❓");
        EMOJI_MAP.put(":exclamation:", "❗");
        
        // Хакерские эмодзи
        EMOJI_MAP.put(":hack:", "💻");
        EMOJI_MAP.put(":skull:", "💀");
        EMOJI_MAP.put(":ghost:", "👻");
        EMOJI_MAP.put(":robot:", "🤖");
        EMOJI_MAP.put(":shield:", "🛡️");
        EMOJI_MAP.put(":key:", "🔑");
        EMOJI_MAP.put(":lock:", "🔒");
        EMOJI_MAP.put(":unlock:", "🔓");
        EMOJI_MAP.put(":search:", "🔍");
        EMOJI_MAP.put(":target:", "🎯");
        
        // Статусы
        EMOJI_MAP.put(":online:", "🟢");
        EMOJI_MAP.put(":offline:", "🔴");
        EMOJI_MAP.put(":away:", "🟡");
        EMOJI_MAP.put(":busy:", "🟠");
    }
    
    private static void initializeSounds() {
        // Используем системные звуки вместо файлов
        soundsEnabled = true;
    }
    
    /**
     * Заменяет текстовые смайлики на эмодзи
     */
    public static String processEmojis(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        String result = text;
        for (Map.Entry<String, String> entry : EMOJI_MAP.entrySet()) {
            result = result.replace(entry.getKey(), entry.getValue());
        }
        
        return result;
    }
    
    /**
     * Фильтрует сообщение от нежелательного контента
     */
    public static String filterMessage(String message, boolean isAdmin) {
        if (message == null || message.trim().isEmpty()) {
            return "";
        }
        
        String filtered = message.trim();
        
        // Ограничение длины
        if (filtered.length() > 500) {
            filtered = filtered.substring(0, 500) + "...";
        }
        
        // Фильтр URL для обычных пользователей
        if (!isAdmin && containsUrl(filtered)) {
            return ""; // Блокируем сообщения с ссылками
        }
        
        // Фильтр email адресов
        if (!isAdmin && containsEmail(filtered)) {
            filtered = EMAIL_PATTERN.matcher(filtered).replaceAll("[EMAIL СКРЫТ]");
        }
        
        // Обработка эмодзи
        filtered = processEmojis(filtered);
        
        return filtered;
    }
    
    /**
     * Проверяет, содержит ли текст URL
     */
    public static boolean containsUrl(String text) {
        return URL_PATTERN.matcher(text).find();
    }
    
    /**
     * Проверяет, содержит ли текст email
     */
    public static boolean containsEmail(String text) {
        return EMAIL_PATTERN.matcher(text).find();
    }
    
    /**
     * Форматирует время в читаемый вид
     */
    public static String formatTimeAgo(long timestamp) {
        long now = System.currentTimeMillis();
        long diff = now - timestamp;
        
        if (diff < 60000) { // Меньше минуты
            return "только что";
        } else if (diff < 3600000) { // Меньше часа
            long minutes = diff / 60000;
            return minutes + " мин назад";
        } else if (diff < 86400000) { // Меньше дня
            long hours = diff / 3600000;
            return hours + " ч назад";
        } else {
            long days = diff / 86400000;
            return days + " дн назад";
        }
    }
    
    /**
     * Генерирует цвет для пользователя на основе имени
     */
    public static String generateUserColor(String username) {
        if (username == null || username.isEmpty()) {
            return "#00ccff";
        }
        
        // Простой алгоритм генерации цвета
        int hash = username.hashCode();
        
        // Предопределенные цвета для лучшей читаемости
        String[] colors = {
            "#00ccff", "#ff6600", "#ffff00", "#ff00ff", 
            "#00ff99", "#ff9900", "#9900ff", "#00ffff",
            "#ff3366", "#66ff33", "#3366ff", "#ff6633"
        };
        
        int index = Math.abs(hash) % colors.length;
        return colors[index];
    }
    
    /**
     * Воспроизводит звук уведомления
     */
    public static void playNotificationSound(ChatMessage.MessageType type) {
        if (!soundsEnabled) {
            return;
        }

        new Thread(() -> {
            try {
                if (soundStyle == SoundStyle.SILENT) {
                    // Только системный beep
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    return;
                }

                if (soundStyle == SoundStyle.CLASSIC) {
                    // Классические beep звуки
                    playClassicSound(type);
                    return;
                }

                // Мягкие звуки (SOFT)
                switch (type) {
                    case TEXT:
                        // Очень мягкий звук как в WhatsApp
                        playTone(440, 100, 0.08f);
                        break;
                    case USER_JOIN:
                        // Нежный восходящий звук
                        playTone(392, 60, 0.06f);
                        Thread.sleep(30);
                        playTone(440, 80, 0.08f);
                        break;
                    case USER_LEAVE:
                        // Нежный нисходящий звук
                        playTone(440, 60, 0.06f);
                        Thread.sleep(30);
                        playTone(392, 80, 0.08f);
                        break;
                    case SYSTEM:
                        // Очень тихий системный звук
                        playTone(523, 50, 0.05f);
                        break;
                    case WARNING:
                        // Мягкое предупреждение
                        playTone(659, 100, 0.1f);
                        break;
                    default:
                        // Тихие системные сообщения
                        break;
                }
            } catch (Exception e) {
                System.err.println("Failed to play notification sound: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Генерирует и воспроизводит мягкий тон заданной частоты
     */
    private static void playTone(int frequency, int duration, float volume) {
        try {
            // Параметры аудио
            int sampleRate = 44100;
            int samples = (int) (sampleRate * duration / 1000.0);

            // Генерируем мягкую синусоидальную волну с плавными переходами
            byte[] buffer = new byte[samples * 2];
            for (int i = 0; i < samples; i++) {
                double angle = 2.0 * Math.PI * i * frequency / sampleRate;

                // Основная синусоида
                double wave = Math.sin(angle);

                // Добавляем плавное нарастание в начале (первые 10%)
                double envelope = 1.0;
                if (i < samples * 0.1) {
                    envelope = (double) i / (samples * 0.1);
                }
                // Добавляем плавное затухание в конце (последние 30%)
                else if (i > samples * 0.7) {
                    envelope = 1.0 - (i - samples * 0.7) / (samples * 0.3);
                }

                // Применяем envelope для мягкости
                short sample = (short) (wave * envelope * Short.MAX_VALUE * volume);

                buffer[i * 2] = (byte) (sample & 0xFF);
                buffer[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
            }

            // Настройка аудио формата
            AudioFormat format = new AudioFormat(sampleRate, 16, 1, true, false);
            DataLine.Info info = new DataLine.Info(SourceDataLine.class, format);

            if (AudioSystem.isLineSupported(info)) {
                SourceDataLine line = (SourceDataLine) AudioSystem.getLine(info);
                line.open(format);
                line.start();
                line.write(buffer, 0, buffer.length);
                line.drain();
                line.close();
            } else {
                // Fallback на системный звук
                java.awt.Toolkit.getDefaultToolkit().beep();
            }

        } catch (Exception e) {
            // Fallback на системный звук
            java.awt.Toolkit.getDefaultToolkit().beep();
        }
    }

    /**
     * Включает/выключает звуки
     */
    public static void setSoundsEnabled(boolean enabled) {
        soundsEnabled = enabled;
    }

    /**
     * Проверяет, включены ли звуки
     */
    public static boolean isSoundsEnabled() {
        return soundsEnabled;
    }

    /**
     * Устанавливает стиль звуков
     */
    public static void setSoundStyle(SoundStyle style) {
        soundStyle = style;
    }

    /**
     * Получает текущий стиль звуков
     */
    public static SoundStyle getSoundStyle() {
        return soundStyle;
    }

    /**
     * Воспроизводит классические beep звуки
     */
    private static void playClassicSound(ChatMessage.MessageType type) {
        try {
            switch (type) {
                case TEXT:
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case USER_JOIN:
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(100);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case USER_LEAVE:
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(50);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(50);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case SYSTEM:
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case WARNING:
                    for (int i = 0; i < 2; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(200);
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            java.awt.Toolkit.getDefaultToolkit().beep();
        }
    }
    
    /**
     * Проверяет, является ли сообщение командой
     */
    public static boolean isCommand(String message) {
        return message != null && message.trim().startsWith("/");
    }
    
    /**
     * Парсит команду из сообщения
     */
    public static String[] parseCommand(String message) {
        if (!isCommand(message)) {
            return new String[0];
        }
        
        String command = message.trim().substring(1); // Убираем /
        return command.split("\\s+");
    }
    
    /**
     * Экранирует HTML символы
     */
    public static String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#x27;");
    }
    
    /**
     * Проверяет валидность имени пользователя
     */
    public static boolean isValidUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = username.trim();
        
        // Длина от 2 до 20 символов
        if (trimmed.length() < 2 || trimmed.length() > 20) {
            return false;
        }
        
        // Только буквы, цифры, пробелы и некоторые символы
        return trimmed.matches("^[a-zA-Zа-яА-Я0-9\\s._-]+$");
    }
    
    /**
     * Получает список доступных команд для пользователя
     */
    public static String[] getAvailableCommands(boolean isAdmin) {
        if (isAdmin) {
            return new String[]{
                "/help - Показать справку",
                "/clear - Очистить чат",
                "/ban <пользователь> - Заблокировать пользователя",
                "/unban <пользователь> - Разблокировать пользователя",
                "/kick <пользователь> - Исключить пользователя",
                "/users - Показать список пользователей"
            };
        } else {
            return new String[]{
                "/help - Показать справку",
                "/users - Показать список пользователей"
            };
        }
    }
}
