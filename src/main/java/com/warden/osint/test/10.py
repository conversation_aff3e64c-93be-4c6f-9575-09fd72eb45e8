#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Warden OSINT Tool v2.0 - Clean Version
"""

from pystyle import Write, Colors, Colorate, Center
import requests
import os
import time
import json
import webbrowser

import hashlib
import uuid
import sys

def get_hwid():
    return hashlib.sha256(uuid.getnode().to_bytes(6, 'little')).hexdigest()

# 🛡️ ДОДАЙТЕ СЮДИ СВОЇ HWID (SHA256 від UUID)
ALLOWED_HWIDS = [
    "d9e0701d3e59e4b45d00c7c11dc2d8a7bfc04069de6c7b63fff715e75a616b7d"
    # приклад: "your_hwid_here"
]

def check_hwid():
    hwid = get_hwid()
    if hwid not in ALLOWED_HWIDS:
        print(f"[!] Ваш HWID не авторизовано!\nHWID: {hwid}\nЗверніться до @underlordprojects для активації.")
        input("[Enter] Вихід...")
        sys.exit()

check_hwid()

webbrowser.open("https://t.me/underlordprojects")

API_BASE_URL = "https://api.usersbox.ru/v1"
API_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVhdGVkX2F0IjoxNzUxMzA2NzExLCJhcHBfaWQiOjE3NTEzMDY3MTF9.iHy-lWuy8rKTwyczmDBL0hKXUsQUfnjJ3PnGsxDAG8k"

def make_api_request(endpoint, params=None):
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    try:
        url = f"{API_BASE_URL}{endpoint}"
        response = requests.get(url, headers=headers, params=params, timeout=30)

        if response.status_code == 200:
            return True, response.json()
        elif response.status_code == 404:
            return False, "Данные не найдены"
        elif response.status_code == 429:
            return False, "Превышен лимит запросов"
        else:
            return False, f"Ошибка API: {response.status_code}"

    except requests.exceptions.Timeout:
        return False, "Превышено время ожидания"
    except requests.exceptions.RequestException as e:
        return False, f"Ошибка сети: {str(e)}"

def format_result(data):
    if isinstance(data, dict):
        formatted = json.dumps(data, indent=2, ensure_ascii=False)
    else:
        formatted = str(data)
    return Colorate.Vertical(Colors.cyan_to_blue, formatted)

def search_generic(prompt):
    query = input(f"\n[?] {prompt}: ").strip()
    if not query:
        print(Colorate.Vertical(Colors.red_to_black, "[!] Поле не может быть пустым"))
        return

    print(Colorate.Vertical(Colors.red_to_black, "[⏳] Поиск данных..."))
    success, result = make_api_request("/search", {"q": query})

    if success:
        print(Colorate.Vertical(Colors.cyan_to_blue, "\n[✓] Результаты найдены:"))
        print(format_result(result))
    else:
        print(Colorate.Vertical(Colors.red_to_black, f"\n[!] {result}"))

def show_manual_anon():
    print(Colorate.Vertical(Colors.purple_to_black, """
    ╔══════════════════════════════════════════════════════════════╗
    ║             МАНУАЛ 1: АНОНИМНОСТЬ В ОСИНТ                    ║
    ║                                                              ║
    ║  • Используйте Tails или Whonix для безопасной среды         ║
    ║  • Никогда не используйте свои реальные аккаунты или почты   ║
    ║  • Используйте Tor + VPN цепочки для маскировки IP           ║
    ║  • Создавайте виртуальные личности для разведки              ║
    ║  • Удаляйте все следы после сессии работы                    ║
    ║                                                              ║
    ║  Подробнее в Telegram: @underlordprojects                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """))

def show_manual_osint():
    print(Colorate.Vertical(Colors.purple_to_black, """
    ╔══════════════════════════════════════════════════════════════╗
    ║             МАНУАЛ 2: ОСНОВЫ ОСИНТ РАЗВЕДКИ                 ║
    ║                                                              ║
    ║  • Запросы к открытым базам данных                          ║
    ║  • Поиск через агрегаторы (skid, leak, paste)               ║
    ║  • Использование dork-запросов в Google                     ║
    ║  • Анализ метаданных файлов и изображений                   ║
    ║  • Использование Telegram-ботов для быстрой проверки        ║
    ║                                                              ║
    ║  Ещё больше информации — @underlordprojects                 ║
    ╚══════════════════════════════════════════════════════════════╝
    """))

def show_manual_about():
    print(Colorate.Vertical(Colors.purple_to_black, """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    WARDEN OSINT TOOL v2.0                   ║
    ║                                                              ║
    ║  Мощный инструмент для OSINT исследований                   ║
    ║  мы неимеем отношение к вардену это ремейк и к дедфесту     ║
    ║  этот софт ремейк а не варден                               ║
    ║  Возможности:                                               ║
    ║  • Поиск по телефону, email, ФИО                           ║
    ║  • Поиск по никнейму и VK ID                               ║
    ║  • Поиск информации по номеру авто                         ║
    ║                                                              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """))

def show_banner():
    os.system("cls" if os.name == "nt" else "clear")
    print(Colorate.Vertical(Colors.red_to_black, """
 ██▄   ███▄▄▄▄      ▄████████    ▄████████    ▄████████ ███▄▄▄▄    ▄██████▄     ▄████████    ▄███████▄    ▄████████  ▄████████    ▄████████ ▀████    ▐████▀ 
 ███   ███▀▀▀██▄   ███    ███   ███    ███   ███    ███ ███▀▀▀██▄ ███    ███   ███    ███   ███    ███   ███    ███ ███    ███   ███    ███   ███▌   ████▀  
 ███   ███   ███   ███    █▀    ███    █▀    ███    ███ ███   ███ ███    ███   ███    █▀    ███    ███   ███    ███ ███    █▀    ███    █▀     ███  ▐███    
 ███   ███   ███  ▄███▄▄▄      ▄███▄▄▄      ▄███▄▄▄▄██▀ ███   ███ ███    ███   ███          ███    ███   ███    ███ ███         ▄███▄▄▄        ▀███▄███▀    
 ███   ███   ███ ▀▀███▀▀▀     ▀▀███▀▀▀     ▀▀███▀▀▀▀▀   ███   ███ ███    ███ ▀███████████ ▀█████████▀  ▀███████████ ███        ▀▀███▀▀▀        ████▀██▄     
 ███   ███   ███   ███          ███    █▄  ▀███████████ ███   ███ ███    ███          ███   ███          ███    ███ ███    █▄    ███    █▄    ▐███  ▀███    
 ███   ███   ███   ███          ███    ███   ███    ███ ███   ███ ███    ███    ▄█    ███   ███          ███    ███ ███    ███   ███    ███  ▄███     ███▄  
 █▀     ▀█   █▀    ███          ██████████   ███    ███  ▀█   █▀   ▀██████▀   ▄████████▀   ▄████▀        ███    █▀  ████████▀    ██████████ ████       ███▄ 
                                             ███    ███
                                            version v2.6
                                            =
                                            =
                                            premium true
                                            =
                                            =
                                            license true
                                            =
                                            =
                                            tgk @underlordprojects
                                            =
                                            =
                                            user @uaziq
    """))

def show_menu():
    Write.Print("""
╭─────────────────────────╮ ╭────────────────────────╮ ╭──────────────────────────╮
│ 1 • Поиск по телефону   │ │ 2 • Поиск по Email     │ │ 3 • Поиск по ФИО         │
│ 4 • Поиск по никнейму   │ │ 5 • Поиск по VK ID     │ │ 6 • Поиск по авто        │
│ 7 • тут 11 если негрузит│ │ 8 • Выход              │ │ 9 • Мануал: Анонимность  │
│10 • Мануал: OSINT       │ │11 • Мануал: О программе│ │                          │
╰─────────────────────────╯ ╰────────────────────────╯ ╰──────────────────────────╯
    """, Colors.blue_to_purple, interval=0.001)

def main():
    while True:
        show_banner()
        show_menu()

        choice = input("\n[?] Введите номер функции: ").strip()

        if choice == "1":
            search_generic("Введите номер телефона (с кодом страны)")
        elif choice == "2":
            search_generic("Введите email адрес")
        elif choice == "3":
            search_generic("Введите ФИО")
        elif choice == "4":
            search_generic("Введите никнейм")
        elif choice == "5":
            search_generic("Введите VK ID")
        elif choice == "6":
            search_generic("Введите номер автомобиля")
        elif choice == "7":
            show_manual_about()
        elif choice == "8":
            print(Colorate.Vertical(Colors.red_to_black, "[!] Выход..."))
            break
        elif choice == "9":
            show_manual_anon()
        elif choice == "10":
            show_manual_osint()
        elif choice == "11":
            show_manual_about()
        else:
            print(Colorate.Vertical(Colors.red_to_black, "[!] Неверная опция"))

        input("\n[Enter] Вернуться в меню...")

if __name__ == "__main__":
    main()
