package com.warden.osint.test;

import com.warden.osint.utils.ThemeManager;

/**
 * Простой тест системы тем без JavaFX
 * Проверяет корректность работы ThemeManager
 */
public class ThemeTest {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("🎨 WARDEN Theme System Test");
        System.out.println("========================================");
        System.out.println();
        
        try {
            // Инициализируем ThemeManager
            ThemeManager themeManager = ThemeManager.getInstance();
            
            System.out.println("✅ ThemeManager initialized successfully");
            System.out.println();
            
            // Тестируем начальную тему
            System.out.println("🔍 Testing initial theme:");
            printThemeInfo(themeManager);
            System.out.println();
            
            // Тестируем переключение темы
            System.out.println("🔄 Testing theme toggle:");
            themeManager.toggleTheme();
            printThemeInfo(themeManager);
            System.out.println();
            
            // Тестируем еще одно переключение
            System.out.println("🔄 Testing second toggle:");
            themeManager.toggleTheme();
            printThemeInfo(themeManager);
            System.out.println();
            
            // Тестируем установку конкретной темы
            System.out.println("🎯 Testing specific theme setting:");
            themeManager.setTheme(ThemeManager.Theme.MIDNIGHT);
            printThemeInfo(themeManager);
            System.out.println();
            
            themeManager.setTheme(ThemeManager.Theme.MATRIX);
            printThemeInfo(themeManager);
            System.out.println();
            
            // Тестируем цветовые схемы
            System.out.println("🎨 Testing color schemes:");
            testColorSchemes(themeManager);
            System.out.println();
            
            System.out.println("========================================");
            System.out.println("✅ All theme tests passed successfully!");
            System.out.println("🎉 Theme system is ready for use!");
            System.out.println("========================================");
            
        } catch (Exception e) {
            System.err.println("❌ Theme test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void printThemeInfo(ThemeManager themeManager) {
        ThemeManager.Theme currentTheme = themeManager.getCurrentTheme();
        
        System.out.println("  📋 Current Theme: " + currentTheme.getDisplayName());
        System.out.println("  📝 Description: " + currentTheme.getDescription());
        System.out.println("  🎨 Primary Color: " + themeManager.getPrimaryColor());
        System.out.println("  🎨 Secondary Color: " + themeManager.getSecondaryColor());
        System.out.println("  🎨 Background Color: " + themeManager.getBackgroundColor());
    }
    
    private static void testColorSchemes(ThemeManager themeManager) {
        System.out.println("🟢 Matrix Theme Colors:");
        themeManager.setTheme(ThemeManager.Theme.MATRIX);
        System.out.println("  Primary: " + themeManager.getPrimaryColor());
        System.out.println("  Secondary: " + themeManager.getSecondaryColor());
        System.out.println("  Accent: " + themeManager.getAccentColor());
        System.out.println("  Background: " + themeManager.getBackgroundColor());
        System.out.println("  Panel BG: " + themeManager.getPanelBackgroundColor());
        System.out.println("  Terminal BG: " + themeManager.getTerminalBackgroundColor());
        System.out.println();
        
        System.out.println("🌙 Midnight Theme Colors:");
        themeManager.setTheme(ThemeManager.Theme.MIDNIGHT);
        System.out.println("  Primary: " + themeManager.getPrimaryColor());
        System.out.println("  Secondary: " + themeManager.getSecondaryColor());
        System.out.println("  Accent: " + themeManager.getAccentColor());
        System.out.println("  Background: " + themeManager.getBackgroundColor());
        System.out.println("  Panel BG: " + themeManager.getPanelBackgroundColor());
        System.out.println("  Terminal BG: " + themeManager.getTerminalBackgroundColor());
    }
}
