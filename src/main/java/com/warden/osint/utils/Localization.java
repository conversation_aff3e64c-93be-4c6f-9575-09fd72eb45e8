package com.warden.osint.utils;

import java.util.HashMap;
import java.util.Map;

public class Localization {
    private static String currentLanguage = "ru"; // По умолчанию русский
    private static final Map<String, Map<String, String>> translations = new HashMap<>();
    
    static {
        initializeTranslations();
    }
    
    private static void initializeTranslations() {
        // Русские переводы
        Map<String, String> ru = new HashMap<>();
        ru.put("app_title", "WARDEN v3.0 - Платформа разведки");
        ru.put("phone_lookup", "📱 Поиск по телефону");
        ru.put("phone_lookup_desc", "Поиск информации по номеру телефона");
        ru.put("email_search", "📧 Поиск по email");
        ru.put("email_search_desc", "Поиск данных по email адресу");
        ru.put("manuals", "📖 Руководства");
        ru.put("manuals_desc", "Полные руководства и инструкции");
        ru.put("team_chat", "💬 Командный чат");
        ru.put("team_chat_desc", "Общение с другими аналитиками");
        ru.put("clear_results", "🗑️ Очистить результаты");
        ru.put("clear_results_desc", "Очистить область результатов");
        ru.put("admin_panel", "👑 Панель администратора");
        ru.put("admin_panel_desc", "Управление пользователями и правами");
        ru.put("logout", "🚪 Выход");
        ru.put("logout_desc", "Выйти из приложения");
        ru.put("language", "🌐 Язык");
        ru.put("language_desc", "Переключить язык интерфейса");
        
        // Заголовки секций
        ru.put("search_functions", "🔍 Функции поиска:");
        ru.put("communication", "💬 Коммуникация:");
        ru.put("system_functions", "⚙️ Системные функции:");
        ru.put("admin_functions", "👑 Функции администратора:");
        
        // Результаты поиска
        ru.put("search_results", "📊 РЕЗУЛЬТАТЫ ПОИСКА");
        ru.put("phone_lookup_results", "📱 РЕЗУЛЬТАТЫ ПОИСКА ПО ТЕЛЕФОНУ");
        ru.put("email_search_results", "📧 РЕЗУЛЬТАТЫ ПОИСКА ПО EMAIL");
        ru.put("personal_info", "👤 ЛИЧНАЯ ИНФОРМАЦИЯ:");
        ru.put("birth_personal", "🎂 ДАТА РОЖДЕНИЯ И ЛИЧНЫЕ ДАННЫЕ:");
        ru.put("documents_ids", "📄 ДОКУМЕНТЫ И ID:");
        ru.put("contact_info", "📞 КОНТАКТНАЯ ИНФОРМАЦИЯ:");
        ru.put("address_info", "🏠 АДРЕСНАЯ ИНФОРМАЦИЯ:");
        ru.put("other_info", "📋 ДРУГАЯ ИНФОРМАЦИЯ:");
        
        // Поля данных
        ru.put("surname", "Фамилия");
        ru.put("name", "Имя");
        ru.put("middle_name", "Отчество");
        ru.put("full_name", "Полное имя");
        ru.put("birth_date", "Дата рождения");
        ru.put("gender", "Пол");
        ru.put("nationality", "Национальность");
        ru.put("inn", "ИНН");
        ru.put("ecb_id", "ECB ID");
        ru.put("passport", "Паспорт");
        ru.put("login", "Логин");
        ru.put("phone_numbers", "Номера телефонов");
        ru.put("email", "Email");
        ru.put("address", "Адрес");
        ru.put("city", "Город");
        ru.put("region", "Регион");
        
        // Статусы и сообщения
        ru.put("search_completed", "🎯 Поиск завершен успешно!");
        ru.put("phone_number", "Номер телефона");
        ru.put("total_sources_found", "Всего источников найдено");
        ru.put("search_status", "Статус поиска");
        ru.put("success", "УСПЕХ");
        ru.put("record", "ЗАПИСЬ");
        ru.put("no_data_found", "❌ Данные для этого номера телефона не найдены");
        ru.put("not_in_databases", "📝 Этого номера может не быть в базах данных");
        ru.put("source", "🗄️ Источник");
        ru.put("records_found", "📊 Найдено записей");
        ru.put("full_data_found", "📋 НАЙДЕНЫ ПОЛНЫЕ ДАННЫЕ:");
        
        // Уведомления
        ru.put("phone_search_started", "Начат поиск по номеру");
        ru.put("search_complete", "✅ Поиск завершен");
        ru.put("search_finished", "Поиск по телефону завершен успешно");
        ru.put("search_failed", "❌ Поиск не удался");
        ru.put("enter_phone_number", "Введите номер телефона в поле поиска");
        ru.put("hotkey_activated", "🔥 Горячая клавиша");
        ru.put("phone_search_activated", "Поиск по телефону активирован (Ctrl+1)");
        ru.put("email_search_activated", "Поиск по email активирован (Ctrl+2)");
        ru.put("manuals_opened", "Руководства открыты (Ctrl+M)");
        ru.put("results_cleared", "Результаты очищены (Ctrl+L)");
        ru.put("export_opened", "Диалог экспорта открыт (Ctrl+S)");
        ru.put("help_opened", "Справка открыта (F1)");
        ru.put("api_refreshed", "Информация API обновлена (F5)");
        
        // Контекстное меню
        ru.put("copy_all_results", "📋 Копировать все результаты");
        ru.put("copy_selected", "📄 Копировать выделенное");
        ru.put("export_to_file", "💾 Экспорт в файл");
        ru.put("clear_results_menu", "🗑️ Очистить результаты");
        ru.put("copied", "📋 Скопировано");
        ru.put("all_results_copied", "Все результаты скопированы в буфер обмена");
        ru.put("selected_copied", "Выделенный текст скопирован в буфер обмена");
        ru.put("warning", "⚠️ Предупреждение");
        ru.put("no_text_selected", "Текст не выделен");
        ru.put("cleared", "🗑️ Очищено");
        ru.put("results_cleared_success", "Результаты успешно очищены");
        
        translations.put("ru", ru);
        
        // Английские переводы
        Map<String, String> en = new HashMap<>();
        en.put("app_title", "WARDEN v3.0 - Intelligence Platform");
        en.put("phone_lookup", "📱 Phone Lookup");
        en.put("phone_lookup_desc", "Search phone number information");
        en.put("email_search", "📧 Email Search");
        en.put("email_search_desc", "Find email-related data");
        en.put("manuals", "📖 Manuals");
        en.put("manuals_desc", "Complete guides and tutorials");
        en.put("team_chat", "💬 Team Chat");
        en.put("team_chat_desc", "Communicate with other analysts");
        en.put("clear_results", "🗑️ Clear Results");
        en.put("clear_results_desc", "Clear the results area");
        en.put("admin_panel", "👑 Admin Panel");
        en.put("admin_panel_desc", "Manage users and permissions");
        en.put("logout", "🚪 Logout");
        en.put("logout_desc", "Sign out from the application");
        en.put("language", "🌐 Language");
        en.put("language_desc", "Switch interface language");
        
        // Section headers
        en.put("search_functions", "🔍 Search Functions:");
        en.put("communication", "💬 Communication:");
        en.put("system_functions", "⚙️ System Functions:");
        en.put("admin_functions", "👑 Admin Functions:");
        
        // Search results
        en.put("search_results", "📊 SEARCH RESULTS");
        en.put("phone_lookup_results", "📱 PHONE LOOKUP RESULTS");
        en.put("email_search_results", "📧 EMAIL SEARCH RESULTS");
        en.put("personal_info", "👤 PERSONAL INFO:");
        en.put("birth_personal", "🎂 BIRTH & PERSONAL:");
        en.put("documents_ids", "📄 DOCUMENTS & IDs:");
        en.put("contact_info", "📞 CONTACT INFO:");
        en.put("address_info", "🏠 ADDRESS INFO:");
        en.put("other_info", "📋 OTHER INFO:");
        
        // Data fields
        en.put("surname", "Surname");
        en.put("name", "Name");
        en.put("middle_name", "Middle Name");
        en.put("full_name", "Full Name");
        en.put("birth_date", "Birth Date");
        en.put("gender", "Gender");
        en.put("nationality", "Nationality");
        en.put("inn", "INN");
        en.put("ecb_id", "ECB ID");
        en.put("passport", "Passport");
        en.put("login", "Login");
        en.put("phone_numbers", "Phone Numbers");
        en.put("email", "Email");
        en.put("address", "Address");
        en.put("city", "City");
        en.put("region", "Region");
        
        // Status and messages
        en.put("search_completed", "🎯 Search completed successfully!");
        en.put("phone_number", "Phone Number");
        en.put("total_sources_found", "Total Sources Found");
        en.put("search_status", "Search Status");
        en.put("success", "SUCCESS");
        en.put("record", "RECORD");
        en.put("no_data_found", "❌ No data found for this phone number");
        en.put("not_in_databases", "📝 This number may not be in any databases");
        en.put("source", "🗄️ Source");
        en.put("records_found", "📊 Records found");
        en.put("full_data_found", "📋 FULL DATA FOUND:");
        
        // Notifications
        en.put("phone_search_started", "Phone search started for");
        en.put("search_complete", "✅ Search Complete");
        en.put("search_finished", "Phone search finished successfully");
        en.put("search_failed", "❌ Search Failed");
        en.put("enter_phone_number", "Enter phone number in search field");
        en.put("hotkey_activated", "🔥 Hotkey");
        en.put("phone_search_activated", "Phone Search activated (Ctrl+1)");
        en.put("email_search_activated", "Email Search activated (Ctrl+2)");
        en.put("manuals_opened", "Manuals opened (Ctrl+M)");
        en.put("results_cleared", "Results cleared (Ctrl+L)");
        en.put("export_opened", "Export dialog opened (Ctrl+S)");
        en.put("help_opened", "Help opened (F1)");
        en.put("api_refreshed", "API Info refreshed (F5)");
        
        // Context menu
        en.put("copy_all_results", "📋 Copy All Results");
        en.put("copy_selected", "📄 Copy Selected");
        en.put("export_to_file", "💾 Export to File");
        en.put("clear_results_menu", "🗑️ Clear Results");
        en.put("copied", "📋 Copied");
        en.put("all_results_copied", "All results copied to clipboard");
        en.put("selected_copied", "Selected text copied to clipboard");
        en.put("warning", "⚠️ Warning");
        en.put("no_text_selected", "No text selected");
        en.put("cleared", "🗑️ Cleared");
        en.put("results_cleared_success", "Results cleared successfully");
        
        translations.put("en", en);
    }
    
    public static String get(String key) {
        Map<String, String> currentTranslations = translations.get(currentLanguage);
        return currentTranslations.getOrDefault(key, key);
    }
    
    public static void setLanguage(String language) {
        if (translations.containsKey(language)) {
            currentLanguage = language;
        }
    }
    
    public static String getCurrentLanguage() {
        return currentLanguage;
    }
    
    public static void toggleLanguage() {
        currentLanguage = currentLanguage.equals("ru") ? "en" : "ru";
    }
    
    public static String getLanguageName() {
        return currentLanguage.equals("ru") ? "Русский" : "English";
    }
}
