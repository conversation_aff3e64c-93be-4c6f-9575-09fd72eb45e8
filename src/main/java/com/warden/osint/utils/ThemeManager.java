package com.warden.osint.utils;

import javafx.animation.*;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.util.Duration;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Менеджер тем для WARDEN v3.0
 * Поддерживает переключение между Matrix (зеленая) и Midnight (фиолетово-синяя) темами
 */
public class ThemeManager {
    
    private static ThemeManager instance;
    private Theme currentTheme = Theme.MATRIX;
    private final Map<String, Consumer<Node>> themeAppliers = new HashMap<>();
    
    public enum Theme {
        MATRIX("Matrix", "🟢 Matrix Theme", "Классическая зеленая тема в стиле хакера"),
        MIDNIGHT("Midnight", "🌙 Midnight Theme", "Элегантная полуночная тема с фиолетово-синими оттенками");
        
        private final String name;
        private final String displayName;
        private final String description;
        
        Theme(String name, String displayName, String description) {
            this.name = name;
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    private ThemeManager() {
        initializeThemeAppliers();
    }
    
    public static ThemeManager getInstance() {
        if (instance == null) {
            instance = new ThemeManager();
        }
        return instance;
    }
    
    /**
     * Переключает тему на следующую
     */
    public void toggleTheme() {
        currentTheme = (currentTheme == Theme.MATRIX) ? Theme.MIDNIGHT : Theme.MATRIX;
        Logger.getInstance().system("Theme switched to: " + currentTheme.getDisplayName());
    }
    
    /**
     * Устанавливает конкретную тему
     */
    public void setTheme(Theme theme) {
        if (theme != currentTheme) {
            currentTheme = theme;
            Logger.getInstance().system("Theme set to: " + currentTheme.getDisplayName());
        }
    }
    
    /**
     * Получает текущую тему
     */
    public Theme getCurrentTheme() {
        return currentTheme;
    }
    
    /**
     * Применяет тему к узлу с анимацией
     */
    public void applyThemeToNode(Node node, String nodeType) {
        applyThemeToNode(node, nodeType, true);
    }
    
    /**
     * Применяет тему к узлу
     */
    public void applyThemeToNode(Node node, String nodeType, boolean animated) {
        Consumer<Node> applier = themeAppliers.get(nodeType);
        if (applier != null) {
            if (animated) {
                animateThemeChange(node, () -> applier.accept(node));
            } else {
                applier.accept(node);
            }
        }
    }
    
    /**
     * Анимирует смену темы
     */
    private void animateThemeChange(Node node, Runnable themeApplication) {
        // Эффект затухания
        FadeTransition fadeOut = new FadeTransition(Duration.millis(200), node);
        fadeOut.setFromValue(1.0);
        fadeOut.setToValue(0.3);
        
        fadeOut.setOnFinished(e -> {
            // Применяем новую тему
            themeApplication.run();
            
            // Эффект появления
            FadeTransition fadeIn = new FadeTransition(Duration.millis(300), node);
            fadeIn.setFromValue(0.3);
            fadeIn.setToValue(1.0);
            fadeIn.play();
        });
        
        fadeOut.play();
    }
    
    /**
     * Инициализирует применители тем для разных типов узлов
     */
    private void initializeThemeAppliers() {
        // Основные контейнеры
        themeAppliers.put("root", this::applyRootTheme);
        themeAppliers.put("panel", this::applyPanelTheme);
        themeAppliers.put("login_panel", this::applyLoginPanelTheme);
        
        // Элементы интерфейса
        themeAppliers.put("title", this::applyTitleTheme);
        themeAppliers.put("subtitle", this::applySubtitleTheme);
        themeAppliers.put("status", this::applyStatusTheme);
        themeAppliers.put("terminal", this::applyTerminalTheme);
        themeAppliers.put("input", this::applyInputTheme);
        themeAppliers.put("button", this::applyButtonTheme);
        themeAppliers.put("modern_button", this::applyModernButtonTheme);
        
        // Чат элементы
        themeAppliers.put("chat_panel", this::applyChatPanelTheme);
        themeAppliers.put("chat_title", this::applyChatTitleTheme);
        themeAppliers.put("message_area", this::applyMessageAreaTheme);
        themeAppliers.put("message_input", this::applyMessageInputTheme);
        
        // Админские элементы
        themeAppliers.put("admin_panel", this::applyAdminPanelTheme);
    }
    
    // ==================== ОСНОВНЫЕ КОНТЕЙНЕРЫ ====================
    
    private void applyRootTheme(Node node) {
        String bgColor = getBackgroundColor();
        node.setStyle("-fx-background-color: " + bgColor + ";");
    }
    
    private void applyPanelTheme(Node node) {
        String bgColor = getPanelBackgroundColor();
        String borderColor = getPrimaryColor();
        
        node.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 10px;" +
            "-fx-background-radius: 10px;" +
            "-fx-padding: 20px;"
        );
    }
    
    private void applyLoginPanelTheme(Node node) {
        String bgColor = getPanelBackgroundColor();
        String borderColor = getPrimaryColor();
        String glowColor = getPrimaryColor();
        
        node.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 2px;" +
            "-fx-border-radius: 15px;" +
            "-fx-background-radius: 15px;" +
            "-fx-padding: 40px;" +
            "-fx-effect: dropshadow(gaussian, " + glowColor + ", 20, 0, 0, 0);"
        );
    }
    
    // ==================== ЭЛЕМЕНТЫ ИНТЕРФЕЙСА ====================
    
    private void applyTitleTheme(Node node) {
        String textColor = getPrimaryColor();
        String glowColor = getPrimaryColor();
        
        node.setStyle(
            "-fx-text-fill: " + textColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, " + glowColor + ", 15, 0, 0, 0);"
        );
    }
    
    private void applySubtitleTheme(Node node) {
        String textColor = getSecondaryColor();
        
        node.setStyle(
            "-fx-text-fill: " + textColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-weight: bold;"
        );
    }
    
    private void applyStatusTheme(Node node) {
        String textColor = getPrimaryColor();
        String bgColor = getStatusBackgroundColor();
        String borderColor = getPrimaryColor();
        
        node.setStyle(
            "-fx-text-fill: " + textColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-background-color: " + bgColor + ";" +
            "-fx-padding: 8px 15px;" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 1px;" +
            "-fx-border-radius: 10px;" +
            "-fx-background-radius: 10px;"
        );
    }
    
    private void applyTerminalTheme(Node node) {
        String bgColor = getTerminalBackgroundColor();
        String textColor = getPrimaryColor();
        String borderColor = getPrimaryColor();
        
        String style = 
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: " + textColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 2px;" +
            "-fx-border-radius: 8px;" +
            "-fx-background-radius: 8px;";
        
        node.setStyle(style);
        
        // Для TextArea устанавливаем дополнительно внутренний фон
        if (node instanceof TextArea) {
            node.setStyle(style + " -fx-control-inner-background: " + bgColor + ";");
        }
    }
    
    private void applyInputTheme(Node node) {
        String bgColor = getInputBackgroundColor();
        String textColor = getPrimaryColor();
        String borderColor = getPrimaryColor();
        String promptColor = getDimmedColor();
        String focusColor = getSecondaryColor();
        
        node.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-control-inner-background: " + bgColor + ";" +
            "-fx-text-fill: " + textColor + ";" +
            "-fx-prompt-text-fill: " + promptColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 2px;" +
            "-fx-border-radius: 5px;" +
            "-fx-background-radius: 5px;" +
            "-fx-padding: 10px;" +
            "-fx-focus-color: " + focusColor + ";" +
            "-fx-faint-focus-color: transparent;"
        );
    }
    
    private void applyButtonTheme(Node node) {
        if (!(node instanceof Button)) return;
        
        Button button = (Button) node;
        String bgColor = getButtonBackgroundColor();
        String textColor = getPrimaryColor();
        String borderColor = getPrimaryColor();
        String glowColor = getPrimaryColor();
        
        String normalStyle = 
            "-fx-background-color: " + bgColor + ";" +
            "-fx-text-fill: " + textColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-weight: bold;" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 2px;" +
            "-fx-border-radius: 10px;" +
            "-fx-background-radius: 10px;" +
            "-fx-cursor: hand;" +
            "-fx-effect: dropshadow(gaussian, " + glowColor + ", 5, 0, 0, 0);";
        
        button.setStyle(normalStyle);
        
        // Эффекты наведения
        button.setOnMouseEntered(e -> {
            String hoverBg = getButtonHoverBackgroundColor();
            String hoverBorder = getButtonHoverBorderColor();
            String hoverGlow = getButtonHoverGlowColor();
            
            button.setStyle(
                "-fx-background-color: " + hoverBg + ";" +
                "-fx-text-fill: #000000;" +
                "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
                "-fx-font-weight: bold;" +
                "-fx-border-color: " + hoverBorder + ";" +
                "-fx-border-width: 3px;" +
                "-fx-border-radius: 10px;" +
                "-fx-background-radius: 10px;" +
                "-fx-cursor: hand;" +
                "-fx-effect: dropshadow(gaussian, " + hoverGlow + ", 10, 0, 0, 0);"
            );
        });
        
        button.setOnMouseExited(e -> button.setStyle(normalStyle));
    }
    
    private void applyModernButtonTheme(Node node) {
        // Для современных кнопок с градиентами
        applyButtonTheme(node);
    }
    
    // ==================== ЧАТ ЭЛЕМЕНТЫ ====================
    
    private void applyChatPanelTheme(Node node) {
        String bgColor = getChatPanelBackgroundColor();
        String borderColor = getPrimaryColor();
        
        node.setStyle(
            "-fx-background-color: " + bgColor + ";" +
            "-fx-border-color: " + borderColor + ";" +
            "-fx-border-width: 0 0 2px 0;"
        );
    }
    
    private void applyChatTitleTheme(Node node) {
        String textColor = getPrimaryColor();
        String glowColor = getPrimaryColor();
        
        node.setStyle(
            "-fx-text-fill: " + textColor + ";" +
            "-fx-font-family: 'Consolas', 'Courier New', monospace;" +
            "-fx-font-size: 16px;" +
            "-fx-font-weight: bold;" +
            "-fx-effect: dropshadow(gaussian, " + glowColor + ", 10, 0, 0, 0);"
        );
    }
    
    private void applyMessageAreaTheme(Node node) {
        applyTerminalTheme(node);
    }
    
    private void applyMessageInputTheme(Node node) {
        applyInputTheme(node);
    }
    
    // ==================== АДМИНСКИЕ ЭЛЕМЕНТЫ ====================
    
    private void applyAdminPanelTheme(Node node) {
        applyPanelTheme(node);
    }
    
    // ==================== ЦВЕТОВЫЕ СХЕМЫ ====================
    
    public String getPrimaryColor() {
        return currentTheme == Theme.MATRIX ? "#00ff00" : "#bb86fc";
    }
    
    public String getSecondaryColor() {
        return currentTheme == Theme.MATRIX ? "#00ccff" : "#03dac6";
    }
    
    public String getAccentColor() {
        return currentTheme == Theme.MATRIX ? "#ffff00" : "#cf6679";
    }
    
    public String getBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "#000000" : "#121212";
    }
    
    public String getPanelBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "#001100" : "#1e1e2e";
    }
    
    public String getTerminalBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "#000000" : "#0d1117";
    }
    
    public String getInputBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "#000000" : "#161b22";
    }
    
    public String getButtonBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "#003300" : "#2d2d42";
    }
    
    public String getButtonHoverBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "#00ff00" : "#bb86fc";
    }
    
    public String getButtonHoverBorderColor() {
        return currentTheme == Theme.MATRIX ? "#00ccff" : "#03dac6";
    }
    
    public String getButtonHoverGlowColor() {
        return currentTheme == Theme.MATRIX ? "#00ccff" : "#bb86fc";
    }
    
    public String getStatusBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "#003300" : "#2d2d42";
    }
    
    public String getChatPanelBackgroundColor() {
        return currentTheme == Theme.MATRIX ? "linear-gradient(to bottom, #001a00, #000d00)" : "linear-gradient(to bottom, #2d2d42, #1e1e2e)";
    }
    
    public String getDimmedColor() {
        return currentTheme == Theme.MATRIX ? "#006600" : "#6c7086";
    }
    
    public String getErrorColor() {
        return currentTheme == Theme.MATRIX ? "#ff0000" : "#f38ba8";
    }
    
    public String getWarningColor() {
        return currentTheme == Theme.MATRIX ? "#ffaa00" : "#fab387";
    }
    
    public String getSuccessColor() {
        return currentTheme == Theme.MATRIX ? "#00ff00" : "#a6e3a1";
    }
}
