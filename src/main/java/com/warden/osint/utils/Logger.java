package com.warden.osint.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.io.PrintWriter;
import java.io.StringWriter;

public class Logger {
    
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static Logger instance;
    
    private Logger() {}
    
    public static Logger getInstance() {
        if (instance == null) {
            instance = new Logger();
        }
        return instance;
    }
    
    /**
     * Логирует информационное сообщение
     */
    public void info(String message) {
        log("INFO", message);
    }
    
    /**
     * Логирует предупреждение
     */
    public void warning(String message) {
        log("WARN", message);
    }
    
    /**
     * Логирует ошибку
     */
    public void error(String message) {
        log("ERROR", message);
    }
    
    /**
     * Логирует ошибку с исключением
     */
    public void error(String message, Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        log("ERROR", message + "\n" + sw.toString());
    }
    
    /**
     * Логирует отладочное сообщение
     */
    public void debug(String message) {
        log("DEBUG", message);
    }
    
    /**
     * Основной метод логирования
     */
    private void log(String level, String message) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String logMessage = String.format("[%s] [%s] %s", timestamp, level, message);
        
        // Выводим в консоль
        if ("ERROR".equals(level)) {
            System.err.println(logMessage);
        } else {
            System.out.println(logMessage);
        }
        
        // TODO: В будущем можно добавить запись в файл
    }
    
    /**
     * Логирует системное сообщение в терминальном стиле
     */
    public void system(String message) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String logMessage = String.format("[%s] [SYSTEM] %s", timestamp, message);
        System.out.println(logMessage);
    }
    
    /**
     * Логирует сообщение аутентификации
     */
    public void auth(String message) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String logMessage = String.format("[%s] [AUTH] %s", timestamp, message);
        System.out.println(logMessage);
    }
    
    /**
     * Логирует API запрос
     */
    public void api(String message) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String logMessage = String.format("[%s] [API] %s", timestamp, message);
        System.out.println(logMessage);
    }

    public void logAction(String email, String telegramSearch, String s) {
    }
}
