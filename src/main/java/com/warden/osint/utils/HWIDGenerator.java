package com.warden.osint.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;

public class HWIDGenerator {
    
    /**
     * Генерирует уникальный HWID на основе характеристик системы
     */
    public static String generateHWID() {
        try {
            StringBuilder hwInfo = new StringBuilder();
            
            // Получаем системную информацию
            hwInfo.append(System.getProperty("os.name", "unknown"));
            hwInfo.append(System.getProperty("os.arch", "unknown"));
            hwInfo.append(System.getProperty("os.version", "unknown"));
            hwInfo.append(System.getProperty("user.name", "unknown"));
            
            // Получаем информацию о процессоре
            hwInfo.append(System.getProperty("java.vm.name", "unknown"));
            hwInfo.append(System.getProperty("java.vm.vendor", "unknown"));
            
            // Получаем информацию о системе
            hwInfo.append(Runtime.getRuntime().availableProcessors());
            hwInfo.append(System.getProperty("file.separator", "/"));
            
            // Пытаемся получить MAC адрес
            try {
                java.net.NetworkInterface networkInterface = java.net.NetworkInterface.getNetworkInterfaces().nextElement();
                if (networkInterface != null && networkInterface.getHardwareAddress() != null) {
                    byte[] mac = networkInterface.getHardwareAddress();
                    StringBuilder macStr = new StringBuilder();
                    for (byte b : mac) {
                        macStr.append(String.format("%02X", b));
                    }
                    hwInfo.append(macStr.toString());
                }
            } catch (Exception e) {
                // Если не удалось получить MAC, используем системное время
                hwInfo.append(System.getProperty("java.version", "unknown"));
            }
            
            // Хешируем полученную информацию
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(hwInfo.toString().getBytes(StandardCharsets.UTF_8));
            
            // Конвертируем в hex строку
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            String hwid = hexString.toString().toUpperCase();
            System.out.println("[HWID] Generated HWID: " + hwid.substring(0, 16) + "...");
            
            return hwid;
            
        } catch (NoSuchAlgorithmException e) {
            System.err.println("[ERROR] Failed to generate HWID: " + e.getMessage());
            // Fallback HWID на основе системного времени и случайных данных
            return "FALLBACK_" + System.currentTimeMillis() + "_" + Math.random();
        }
    }
    
    /**
     * Проверяет валидность HWID
     */
    public static boolean isValidHWID(String hwid) {
        if (hwid == null || hwid.trim().isEmpty()) {
            return false;
        }
        
        // HWID должен быть hex строкой определенной длины
        return hwid.matches("^[A-F0-9]{64}$") || hwid.startsWith("FALLBACK_");
    }
    
    /**
     * Получает короткую версию HWID для отображения
     */
    public static String getShortHWID(String hwid) {
        if (hwid == null || hwid.length() < 16) {
            return "UNKNOWN";
        }
        return hwid.substring(0, 8) + "..." + hwid.substring(hwid.length() - 8);
    }
}
