package com.warden;

/**
 * Launcher класс для обхода проблем с JavaFX модулями
 * Этот класс не наследует Application, что позволяет избежать
 * ошибки "JavaFX runtime components are missing"
 */
public class Launcher {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("🔍 WARDEN OSINT Tool v3.0");
        System.out.println("Advanced Intelligence Platform");
        System.out.println("========================================");
        System.out.println();
        
        System.out.println("[LAUNCHER] Starting JavaFX application...");
        
        try {
            // Проверяем доступность JavaFX
            checkJavaFXAvailability();

            // Запускаем основное приложение
            Main1.main(args);
        } catch (Exception e) {
            System.err.println("[ERROR] Failed to start application: " + e.getMessage());
            e.printStackTrace();

            System.err.println();
            System.err.println("========================================");
            System.err.println("🔧 TROUBLESHOOTING GUIDE:");
            System.err.println("========================================");

            if (e.getMessage() != null && e.getMessage().contains("JavaFX")) {
                System.err.println("❌ JavaFX runtime components are missing!");
                System.err.println();
                System.err.println("🔧 SOLUTION OPTIONS:");
                System.err.println();
                System.err.println("📦 Option 1 - Use Maven (Recommended):");
                System.err.println("   mvn clean compile javafx:run");
                System.err.println();
                System.err.println("📦 Option 2 - Use provided scripts:");
                System.err.println("   Windows: run-warden.bat");
                System.err.println("   Linux/Mac: ./run-warden.sh");
                System.err.println();
                System.err.println("📦 Option 3 - Manual JavaFX setup:");
                System.err.println("   1. Download JavaFX SDK from: https://openjfx.io/");
                System.err.println("   2. Extract to C:\\javafx-sdk-17\\ (Windows) or ~/javafx-sdk-17/ (Linux/Mac)");
                System.err.println("   3. Add VM arguments:");
                System.err.println("      --module-path [JavaFX-Path]/lib");
                System.err.println("      --add-modules javafx.controls,javafx.fxml");
                System.err.println();
                System.err.println("🎨 NEW FEATURE: Try the theme demo!");
                System.err.println("   Windows: run-theme-demo.bat");
                System.err.println("   Linux/Mac: ./run-theme-demo.sh");
            } else {
                System.err.println("❌ General application error occurred.");
                System.err.println("📞 Contact support: @InfernoSoulAttack, @Svuanstvo");
            }

            System.err.println("========================================");
        }
    }

    private static void checkJavaFXAvailability() {
        try {
            Class.forName("javafx.application.Application");
            System.out.println("[LAUNCHER] ✅ JavaFX runtime detected");
        } catch (ClassNotFoundException e) {
            System.out.println("[LAUNCHER] ⚠️ JavaFX runtime not found in classpath");
            System.out.println("[LAUNCHER] This is normal when using external JavaFX");
        }
    }
}
