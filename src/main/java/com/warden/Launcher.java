package com.warden;

/**
 * Launcher класс для обхода проблем с JavaFX модулями
 * Этот класс не наследует Application, что позволяет избежать
 * ошибки "JavaFX runtime components are missing"
 */
public class Launcher {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("🔍 WARDEN OSINT Tool v3.0");
        System.out.println("Advanced Intelligence Platform");
        System.out.println("========================================");
        System.out.println();
        
        System.out.println("[LAUNCHER] Starting JavaFX application...");
        
        try {
            // Запускаем основное приложение
            Main1.main(args);
        } catch (Exception e) {
            System.err.println("[ERROR] Failed to start application: " + e.getMessage());
            e.printStackTrace();
            
            System.err.println();
            System.err.println("========================================");
            System.err.println("TROUBLESHOOTING:");
            System.err.println("========================================");
            System.err.println("If you see 'JavaFX runtime components are missing':");
            System.err.println();
            System.err.println("1. Download JavaFX SDK from: https://openjfx.io/");
            System.err.println("2. Extract to C:\\javafx-sdk-17\\");
            System.err.println("3. Add VM arguments in IntelliJ:");
            System.err.println("   --module-path C:\\javafx-sdk-17\\lib");
            System.err.println("   --add-modules javafx.controls,javafx.fxml");
            System.err.println();
            System.err.println("Or run with Maven:");
            System.err.println("   mvn javafx:run");
            System.err.println("========================================");
        }
    }
}
