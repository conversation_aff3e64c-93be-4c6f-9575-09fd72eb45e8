package com.warden;

import com.warden.osint.auth.AuthManager;
import com.warden.osint.gui.LoginWindow;
import com.warden.osint.utils.Logger;
import javafx.application.Application;
import javafx.stage.Stage;

public class Main1 extends Application {

    private AuthManager authManager;
    private Logger logger;

    @Override
    public void start(Stage primaryStage) {
        // Инициализация компонентов
        logger = Logger.getInstance();
        authManager = new AuthManager();

        logger.system("WARDEN v3.0 starting...");
        logger.system("Initializing secure authentication module...");

        // Создаем окно входа
        LoginWindow loginWindow = new LoginWindow(primaryStage, authManager);
        loginWindow.show();

        logger.system("Application started successfully");
    }

    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("🔍 WARDEN v3.0");
        System.out.println("Intelligence Platform");
        System.out.println("========================================");
        System.out.println();

        launch(args);
    }
}