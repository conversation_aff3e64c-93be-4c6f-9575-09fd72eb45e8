@echo off
echo ========================================
echo 🎨 WARDEN Theme System Demo
echo ========================================
echo.

echo [DEMO] Starting theme demonstration...
echo.

REM Компилируем проект
echo [INFO] Compiling project...
mvn clean compile

REM Запускаем демо
echo [INFO] Starting theme demo...
java --module-path "C:\javafx-sdk-17\lib" --add-modules javafx.controls,javafx.fxml -cp "target\classes" com.warden.osint.demo.ThemeDemo

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] Theme demo failed to start.
    echo.
    echo Please make sure:
    echo 1. JavaFX SDK is installed at C:\javafx-sdk-17\
    echo 2. Java 17+ is installed
    echo 3. Project is compiled: mvn clean compile
    echo.
    pause
)

echo.
echo [SUCCESS] Theme demo completed!
pause
