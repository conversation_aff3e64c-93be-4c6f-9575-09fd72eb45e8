@echo off
echo ========================================
echo 🔍 WARDEN v3.0 Setup Script
echo 🎨 With Advanced Theme System
echo ========================================
echo.

echo [SETUP] Checking system requirements...
echo.

REM Проверяем Java
echo [CHECK] Checking Java version...
java -version 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java not found!
    echo.
    echo Please install Java 17 or higher:
    echo https://adoptium.net/
    echo.
    pause
    exit /b 1
)

echo ✅ Java found
echo.

REM Проверяем Maven
echo [CHECK] Checking Maven...
mvn -version 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Maven not found!
    echo.
    echo Please install Maven:
    echo https://maven.apache.org/download.cgi
    echo.
    pause
    exit /b 1
)

echo ✅ Maven found
echo.

REM Компилируем проект
echo [SETUP] Compiling WARDEN project...
mvn clean compile

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Compilation failed!
    echo.
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo ✅ Project compiled successfully
echo.

REM Тестируем систему тем
echo [TEST] Testing theme system...
java -cp "target\classes" com.warden.osint.test.ThemeTest

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Theme test failed!
    pause
    exit /b 1
)

echo ✅ Theme system working correctly
echo.

REM Проверяем JavaFX
echo [CHECK] Testing JavaFX availability...
mvn javafx:run -Djavafx.args="--help" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ JavaFX ready via Maven
    set JAVAFX_METHOD=maven
) else (
    echo ⚠️ JavaFX not available via Maven
    echo Checking for manual JavaFX installation...
    
    if exist "C:\javafx-sdk-17\lib" (
        echo ✅ JavaFX SDK found at C:\javafx-sdk-17\
        set JAVAFX_METHOD=manual
    ) else (
        echo ❌ JavaFX SDK not found
        echo.
        echo JAVAFX SETUP REQUIRED:
        echo 1. Download JavaFX SDK from: https://openjfx.io/
        echo 2. Extract to: C:\javafx-sdk-17\
        echo 3. Run this setup script again
        echo.
        echo Or use Maven method: mvn javafx:run
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 🎉 WARDEN v3.0 Setup Complete!
echo ========================================
echo.

echo ✅ System Requirements Met:
echo   • Java 17+ installed
echo   • Maven installed and working
echo   • Project compiled successfully
echo   • Theme system tested and working
if "%JAVAFX_METHOD%"=="maven" (
    echo   • JavaFX ready via Maven
) else (
    echo   • JavaFX SDK installed manually
)

echo.
echo 🚀 Ready to Launch:
if "%JAVAFX_METHOD%"=="maven" (
    echo   mvn javafx:run
) else (
    echo   run-warden.bat
)

echo.
echo 🎨 New Features to Try:
echo   • Switch between Matrix and Midnight themes
echo   • Test theme demo: run-theme-demo.bat
echo   • Explore the beautiful new interface
echo.

echo 📖 Documentation:
echo   • README.md - Full documentation
echo   • QUICK_START.md - Quick start guide
echo   • THEME_SYSTEM.md - Theme system details
echo.

echo 🎯 Next Steps:
echo 1. Run WARDEN: run-warden.bat
echo 2. Try switching themes in the interface
echo 3. Explore the new Midnight theme!
echo.

echo ========================================
echo Happy hacking with WARDEN v3.0! 🔍✨
echo ========================================

pause
