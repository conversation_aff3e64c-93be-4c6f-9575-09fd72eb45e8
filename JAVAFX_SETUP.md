# 🔧 JavaFX Setup Guide для WARDEN v3.0

## ❗ Важно понимать

**JavaFX SDK ≠ Java JDK**

- **Java JDK** - это основная платформа Java (у вас уже установлена Java 21 ✅)
- **JavaFX SDK** - это отдельная библиотека для создания GUI приложений

## 📥 Правильная установка JavaFX

### Шаг 1: Скачайте JavaFX SDK
1. Идите на https://openjfx.io/
2. Выберите **JavaFX 21** (совместимо с вашей Java 21)
3. Скачайте **SDK** (не JMods, не Runtime)
4. Выберите вашу ОС (Windows x64)

### Шаг 2: Правильное размещение
```
C:\
└── javafx-sdk-21\          ← Создайте эту папку
    └── lib\                ← Здесь должны быть .jar файлы
        ├── javafx.base.jar
        ├── javafx.controls.jar
        ├── javafx.fxml.jar
        └── другие .jar файлы
```

**НЕ ПУТАЙТЕ с Java JDK!**
- Java JDK: `C:\Program Files\Java\jdk-21\`
- JavaFX SDK: `C:\javafx-sdk-21\` (отдельная папка!)

### Шаг 3: Проверка установки
Откройте командную строку и выполните:
```cmd
dir C:\javafx-sdk-21\lib
```

Вы должны увидеть файлы типа:
```
javafx.base.jar
javafx.controls.jar
javafx.fxml.jar
javafx.graphics.jar
javafx.media.jar
javafx.swing.jar
javafx.web.jar
```

## 🚀 Способы запуска WARDEN

### Способ 1: Через наш скрипт (Рекомендуется)
```cmd
run-warden-simple.bat
```

### Способ 2: Прямая команда
```cmd
java --module-path "C:\javafx-sdk-21\lib" --add-modules javafx.controls,javafx.fxml -cp "target\classes" com.warden.Launcher
```

### Способ 3: Через IDE (IntelliJ IDEA)
1. File → Project Structure → Libraries
2. Нажмите "+" → Java
3. Выберите папку `C:\javafx-sdk-21\lib`
4. Run → Edit Configurations
5. В VM options добавьте:
```
--module-path C:\javafx-sdk-21\lib --add-modules javafx.controls,javafx.fxml
```

## 🔧 Если что-то не работает

### Проблема: "JavaFX runtime components are missing"
**Решение:**
1. Убедитесь, что JavaFX SDK распакован в `C:\javafx-sdk-21\`
2. Проверьте наличие файлов в `C:\javafx-sdk-21\lib\`
3. Используйте наш скрипт `run-warden-simple.bat`

### Проблема: "Module not found"
**Решение:**
1. Проверьте путь к JavaFX: `C:\javafx-sdk-21\lib`
2. Убедитесь, что используете правильную версию JavaFX (21)

### Проблема: "Class not found"
**Решение:**
1. Сначала скомпилируйте проект:
```cmd
javac -cp "C:\javafx-sdk-21\lib\*" -d target\classes src\main\java\com\warden\*.java src\main\java\com\warden\osint\**\*.java
```

## 📁 Структура после установки

```
C:\
├── Program Files\Java\jdk-21\     ← Ваша Java (уже есть)
├── javafx-sdk-21\                 ← JavaFX SDK (нужно создать)
│   └── lib\
│       ├── javafx.base.jar
│       ├── javafx.controls.jar
│       └── другие .jar файлы
└── Users\along\Downloads\warden\warden\  ← Проект WARDEN
    ├── src\
    ├── target\
    └── run-warden-simple.bat
```

## ✅ Быстрая проверка

Выполните эти команды по порядку:

1. **Проверка Java:**
```cmd
java -version
```
Должно показать Java 21 ✅

2. **Проверка JavaFX:**
```cmd
dir C:\javafx-sdk-21\lib\javafx.controls.jar
```
Должен найти файл ✅

3. **Компиляция проекта:**
```cmd
javac -version
```
Должно показать javac 21 ✅

4. **Запуск WARDEN:**
```cmd
run-warden-simple.bat
```
Должно запустить приложение с темами! 🎨

## 🎯 Альтернативные варианты

### Если не хотите возиться с JavaFX SDK:

1. **Установите IntelliJ IDEA Community** (бесплатно)
2. Откройте проект WARDEN
3. IDEA автоматически предложит скачать JavaFX
4. Запустите через IDEA

### Или используйте Eclipse:
1. Установите Eclipse IDE
2. Установите плагин e(fx)clipse
3. Импортируйте проект
4. Запустите

## 📞 Помощь

Если ничего не работает:
1. Скриншот ошибки
2. Результат команды `java -version`
3. Результат команды `dir C:\javafx-sdk-21\lib`
4. Обратитесь к @InfernoSoulAttack или @Svuanstvo

---

**Главное помнить: JavaFX SDK - это отдельная библиотека, не путайте с Java JDK!** 🎯
