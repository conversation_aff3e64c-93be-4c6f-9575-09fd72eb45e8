@echo off
echo ========================================
echo 🔍 WARDEN v3.0 - Intelligence Platform
echo 🎨 With Advanced Theme System
echo ========================================
echo.

echo [LAUNCHER] Checking Java version...
java -version
echo.

echo [LAUNCHER] Starting WARDEN with JavaFX support...
echo.

REM Попробуем запустить через Maven (рекомендуемый способ)
echo [INFO] Attempting to run via Maven...
mvn clean compile javafx:run

REM Если Maven не работает, попробуем прямой запуск
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [WARNING] Maven execution failed. Trying direct Java execution...
    echo.
    
    REM Компилируем проект
    echo [INFO] Compiling project...
    mvn clean compile
    
    REM Запускаем с JavaFX параметрами
    echo [INFO] Running with JavaFX parameters...
    java --module-path "C:\javafx-sdk-17\lib" --add-modules javafx.controls,javafx.fxml -cp "target\classes;target\dependency\*" com.warden.Launcher
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo [ERROR] Direct execution also failed.
        echo.
        echo ========================================
        echo TROUBLESHOOTING GUIDE:
        echo ========================================
        echo.
        echo 1. Download JavaFX SDK from: https://openjfx.io/
        echo 2. Extract to C:\javafx-sdk-17\
        echo 3. Make sure Java 17+ is installed
        echo 4. Run: mvn clean compile javafx:run
        echo.
        echo Alternative VM arguments for IDE:
        echo --module-path C:\javafx-sdk-17\lib
        echo --add-modules javafx.controls,javafx.fxml
        echo.
        echo For IntelliJ IDEA:
        echo 1. File ^> Project Structure ^> Libraries
        echo 2. Add JavaFX SDK lib folder
        echo 3. Run ^> Edit Configurations
        echo 4. Add VM options above
        echo.
        echo ========================================
        pause
    )
)

echo.
echo [SUCCESS] WARDEN started successfully!
echo 🎨 Try the new theme system - switch between Matrix and Midnight themes!
echo.
pause
