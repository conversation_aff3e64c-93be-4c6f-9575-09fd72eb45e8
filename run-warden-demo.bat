@echo off
echo ========================================
echo 🔍 WARDEN OSINT - Demo Launcher
echo 🚀 Демо версия без внешних зависимостей
echo ========================================
echo.

echo [COMPILE] Компиляция демо версии...

REM Создаем директории
if not exist "target\classes" mkdir target\classes

REM Компилируем утилиты
echo Compiling utilities...
javac -d target\classes src\main\java\com\warden\osint\utils\Logger.java 2>nul
javac -d target\classes src\main\java\com\warden\osint\utils\Localization.java 2>nul

REM Компилируем Swing Theme Manager
echo Compiling Swing Theme Manager...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

REM Компилируем API Scanner (без Jackson)
echo Compiling API Scanner...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIEndpointResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIScanner.java

REM Компилируем API Finder
echo Compiling API Finder...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APISource.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinderResult.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\api\APIFinder.java

REM Компилируем GUI
echo Compiling GUI modules...
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIScannerGUI.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\APIFinderGUI.java
javac -cp target\classes -d target\classes src\main\java\com\warden\osint\gui\swing\SwingMainWindow.java

if %ERRORLEVEL% NEQ 0 goto :compile_error

echo ✅ Компиляция завершена!
echo.

echo [LAUNCH] Запуск WARDEN OSINT Demo...
echo.
echo 🔍 WARDEN OSINT - Advanced Intelligence Platform
echo ═══════════════════════════════════════════════════════════════
echo 📱 Phone Lookup - демо поиск по номеру телефона
echo 📧 Email Search - демо поиск по email адресу  
echo 📱 Telegram OSINT - демо поиск по Telegram
echo 🔍 API Scanner - реальное сканирование API
echo 🔍 API Finder - реальный поиск API источников
echo ═══════════════════════════════════════════════════════════════
echo.
echo ⚠️  ДЕМО РЕЖИМ: Phone/Email/Telegram поиск показывает примеры
echo ✅ РАБОЧИЕ: API Scanner и API Finder полностью функциональны
echo.

REM Запускаем основное окно WARDEN
java -cp target\classes com.warden.osint.gui.swing.SwingMainWindow

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start WARDEN Demo
    pause
    exit /b 1
)

echo.
echo ✅ WARDEN OSINT Demo started successfully!
pause
goto :end

:compile_error
echo ❌ Compilation error occurred!
echo.
echo Возможные причины:
echo - Отсутствует Java JDK
echo - Синтаксические ошибки в коде
echo.
pause
exit /b 1

:end
