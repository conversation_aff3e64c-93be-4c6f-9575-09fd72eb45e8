@echo off
echo ========================================
echo 🔧 КОМПИЛЯЦИЯ CORE WARDEN OSINT
echo 🚀 Только основные модули без JavaFX
echo ========================================
echo.

REM Создаем директории
if not exist "target\classes" mkdir target\classes
if not exist "target\lib" mkdir target\lib

echo [STEP 1] Скачивание Jackson зависимостей...

REM Скачиваем Jackson
if not exist "target\lib\jackson-core-2.15.2.jar" (
    echo Downloading Jackson Core...
    curl -L -o target\lib\jackson-core-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar
)

if not exist "target\lib\jackson-databind-2.15.2.jar" (
    echo Downloading Jackson Databind...
    curl -L -o target\lib\jackson-databind-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar
)

if not exist "target\lib\jackson-annotations-2.15.2.jar" (
    echo Downloading Jackson Annotations...
    curl -L -o target\lib\jackson-annotations-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar
)

REM Создаем classpath
set CLASSPATH=target\lib\*;target\classes

echo.
echo [STEP 2] Компиляция основных модулей (без JavaFX)...

echo Compiling basic utilities...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\Logger.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\Localization.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\HWIDGenerator.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling authentication...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\User.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\AuthManager.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling API modules...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramSearchResult.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\TelegramOSINT.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\UsersBoxAPI.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling Swing GUI...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\swing\SwingThemeManager.java
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\swing\SwingWardenDemo.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo.
echo ✅ Компиляция core модулей завершена успешно!
echo.
echo 📊 Скомпилированные модули:
echo   ✅ Logger, Localization, HWIDGenerator
echo   ✅ User, AuthManager
echo   ✅ TelegramOSINT, UsersBoxAPI
echo   ✅ SwingThemeManager, SwingWardenDemo
echo.
echo 🚀 Команды для запуска:
echo   Swing Demo: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingWardenDemo
echo   Theme Test: java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingThemeManager
echo.
echo 💡 Для полной функциональности нужен JavaFX SDK
echo.

REM Запускаем Swing Demo
echo [LAUNCH] Запуск Swing Demo...
java -cp "%CLASSPATH%" com.warden.osint.gui.swing.SwingWardenDemo

pause
goto :end

:compile_error
echo ❌ Ошибка компиляции!
echo.
echo Возможные причины:
echo - Отсутствует Java JDK
echo - Проблемы с загрузкой зависимостей
echo - Синтаксические ошибки в коде
echo.
pause
exit /b 1

:end
