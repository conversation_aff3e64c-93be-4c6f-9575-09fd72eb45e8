@echo off
echo ========================================
echo 🔍 WARDEN OSINT Tool v3.0
echo Advanced Intelligence Platform
echo ========================================
echo.

echo [LAUNCHER] Компиляция и запуск основного лаунчера...

REM Создаем директории
if not exist "target\classes" mkdir target\classes
if not exist "target\lib" mkdir target\lib

echo Downloading JavaFX SDK...
if not exist "target\lib\javafx-controls-19.jar" (
    echo Downloading JavaFX Controls...
    curl -L -o target\lib\javafx-controls-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-controls/19/javafx-controls-19.jar
)

if not exist "target\lib\javafx-base-19.jar" (
    echo Downloading JavaFX Base...
    curl -L -o target\lib\javafx-base-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-base/19/javafx-base-19.jar
)

if not exist "target\lib\javafx-graphics-19.jar" (
    echo Downloading JavaFX Graphics...
    curl -L -o target\lib\javafx-graphics-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-graphics/19/javafx-graphics-19.jar
)

if not exist "target\lib\javafx-fxml-19.jar" (
    echo Downloading JavaFX FXML...
    curl -L -o target\lib\javafx-fxml-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-fxml/19/javafx-fxml-19.jar
)

echo Downloading Jackson...
if not exist "target\lib\jackson-core-2.15.2.jar" (
    echo Downloading Jackson Core...
    curl -L -o target\lib\jackson-core-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar
)

if not exist "target\lib\jackson-databind-2.15.2.jar" (
    echo Downloading Jackson Databind...
    curl -L -o target\lib\jackson-databind-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar
)

if not exist "target\lib\jackson-annotations-2.15.2.jar" (
    echo Downloading Jackson Annotations...
    curl -L -o target\lib\jackson-annotations-2.15.2.jar https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar
)

REM Создаем classpath
set CLASSPATH=target\lib\*;target\classes

echo.
echo [COMPILE] Компиляция всех модулей...

echo Compiling utilities...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\utils\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling authentication...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\auth\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling API modules...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\api\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling GUI...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\gui\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling chat...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\osint\chat\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo Compiling main classes...
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\warden\*.java
if %ERRORLEVEL% NEQ 0 goto :compile_error

echo.
echo ✅ Компиляция завершена!
echo.

echo [LAUNCH] Запуск WARDEN Launcher...
echo.

REM Запускаем лаунчер с JavaFX модулями
java --module-path target\lib --add-modules javafx.controls,javafx.fxml -cp "%CLASSPATH%" com.warden.Launcher

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Ошибка запуска с JavaFX модулями, пробуем без модулей...
    java -cp "%CLASSPATH%" com.warden.Launcher
)

pause
goto :end

:compile_error
echo ❌ Ошибка компиляции!
echo.
echo Возможные причины:
echo - Отсутствует Java JDK
echo - Проблемы с загрузкой зависимостей
echo - Синтаксические ошибки в коде
echo.
pause
exit /b 1

:end
