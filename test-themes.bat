@echo off
echo ========================================
echo 🧪 WARDEN Theme System Test
echo ========================================
echo.

echo [TEST] Compiling project...
mvn clean compile

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Compilation failed!
    pause
    exit /b 1
)

echo.
echo [TEST] Running theme system test...
echo.

java -cp "target\classes" com.warden.osint.test.ThemeTest

echo.
echo [TEST] Test completed!
echo.
echo To test the full GUI with themes:
echo 1. Install JavaFX SDK from https://openjfx.io/
echo 2. Run: run-warden.bat
echo 3. Try switching themes in the interface!
echo.
pause
